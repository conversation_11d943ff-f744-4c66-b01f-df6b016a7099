{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="call-to-action-section" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="call-to-action-content">
    {% if section.settings.title != blank %}
      <h2 class="cta-title">{{ section.settings.title }}</h2>
    {% endif %}
    
    {% if section.settings.button_text != blank and section.settings.button_url != blank %}
      <div class="cta-button-wrapper">
        <a href="{{ section.settings.button_url }}" class="cta-button">
          {{ section.settings.button_text }}
        </a>
      </div>
    {% endif %}
  </div>
</div>

<style>
/* Call to Action Section */
.call-to-action-section {
  background-color: rgb(250, 250, 250);
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.call-to-action-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
  text-align: center;
}

.cta-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 50px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: center;
  width: 100%;
  max-width: 1180px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}

.cta-button {
  background-color: rgb(74, 158, 218);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  border: 1px solid rgba(0, 0, 0, 0);
  border-radius: 4px;
  box-shadow: rgba(74, 158, 218, 0.5) 0px 9px 20px 0px;
  box-sizing: border-box;
  color: rgb(255, 255, 255);
  cursor: pointer;
  display: inline-block;
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  max-width: 295px;
  min-width: 200px;
  padding: 12px 10px;
  position: relative;
  text-align: center;
  text-decoration: none;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 295px;
  z-index: 2;
}

.cta-button:hover {
  background-color: rgb(84, 168, 228);
  text-decoration: none;
  color: rgb(255, 255, 255);
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .call-to-action-section {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }

  .call-to-action-content {
    padding: 0 15px;
  }

  .cta-title {
    font-size: 28px;
    line-height: 39.2px;
    margin-bottom: 30px;
    max-width: 100%;
  }

  .cta-button {
    width: 100%;
    max-width: 100%;
    min-width: auto;
  }
}

@media only screen and (max-width: 480px) {
  .cta-title {
    font-size: 24px;
    line-height: 33.6px;
    margin-bottom: 25px;
  }

  .cta-button {
    font-size: 14px;
    height: 45px;
    padding: 10px 8px;
  }
}
</style>

{% schema %}
{
  "name": "Call to Action",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Today could have been pain-free. Take the first step"
    },
    {
      "type": "text",
      "id": "button_text",
      "label": "Button Text",
      "default": "SHOP NOW"
    },
    {
      "type": "url",
      "id": "button_url",
      "label": "Button Link"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Desktop Spacing",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Mobile Spacing",
      "default": 60
    }
  ]
}
{% endschema %}
