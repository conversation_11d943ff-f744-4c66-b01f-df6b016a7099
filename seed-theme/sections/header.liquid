{% comment %}theme-check-disable ImgLazyLoading{% endcomment %}
{%- liquid
  assign search_compact = false
  assign logo_centered = false
  assign search_centered = false
  assign search_right = false
  assign has_menu_bar = true
  assign button_hidden = false

  assign app_blocks = section.blocks | where: 'type', '@app'
  assign liquid_blocks = section.blocks | where: 'type', 'liquid'
  assign usp_blocks = section.blocks | where: 'type', 'usp'
  assign button_block = section.blocks | where: 'type', 'button' | first
  if button_block.settings.button_menu != empty and button_block.settings.button_menu != null
    assign button_menu = button_block.settings.button_menu
  else
    assign button_menu = false
  endif

  assign hide_country_selector = false
  assign hide_language_selector = false
  unless section.settings.enable_country_selector
    assign hide_country_selector = true
  endunless
  unless section.settings.enable_language_selector
    assign hide_language_selector = true
  endunless

  case section.settings.layout
    when 'layout_1'
      assign search_centered = true
      if button_menu
        assign elements_order = 'logo,button,searchbar' | split: ','
      else
        assign elements_order = 'button,logo,searchbar' | split: ','
      endif
    when 'layout_2'
      if button_menu
        assign elements_order = 'logo,button,searchbar' | split: ','
      else
        assign elements_order = 'button,logo,searchbar' | split: ','
      endif
    when 'layout_3'
      assign search_right = true
      assign search_compact = true
      if button_menu
        assign elements_order = 'logo,button,searchbar' | split: ','
      else
        assign elements_order = 'button,logo,searchbar' | split: ','
      endif
    when 'layout_4'
      assign search_right = true
      if button_menu
        assign elements_order = 'logo,button,searchbar' | split: ','
      else
        assign elements_order = 'button,logo,searchbar' | split: ','
      endif
    when 'layout_5'
      assign has_menu_bar = false
      assign search_compact = true
      assign search_right = true
      if button_menu
        assign elements_order = 'searchbar,button,logo,menu' | split: ','
      else
        assign elements_order = 'button,searchbar,logo,menu' | split: ','
      endif
    when 'layout_6'
      assign logo_centered = true
      assign search_compact = true
      assign search_centered = true
      assign elements_order = 'button,searchbar,logo' | split: ','
    when 'layout_7'
      if section.settings.logo
        assign logo_centered = true
      elsif shop.name.size <= 45
        assign logo_centered = true
      else
        assign logo_text_centered = true
      endif
      assign button_hidden = true
      assign elements_order = 'button,searchbar,logo' | split: ','
    when 'layout_8'
      assign has_menu_bar = false
      assign logo_centered = true
      assign search_compact = true
      assign search_right = true
      assign button_hidden = true
      assign elements_order = 'button,menu,logo,searchbar' | split: ','
  endcase

  if button_hidden
    assign button_menu = false
  endif

  comment
    Remove searchbar from elements_order if search is hidden
  endcomment
  if section.settings.hide_search
    assign elements_order_filtered = ''
    for element in elements_order
      unless element == 'searchbar'
        if elements_order_filtered == ''
          assign elements_order_filtered = element
        else
          assign elements_order_filtered = elements_order_filtered | append: ',' | append: element
        endif
      endunless
    endfor
    assign elements_order = elements_order_filtered | split: ','
  endif

  if section.settings.menubar_menu != empty
    assign menu = section.settings.menubar_menu
    assign menu_layout = section.settings.menu_layout
  else
    assign menu = false
    assign menu_layout = false
  endif

  if menu or button_menu
    if menu and button_menu
      assign nav_menu = button_menu
      assign navbar_menu = menu
    else
      assign navbar_menu = false
      if menu
        assign nav_menu = menu
      else
        assign nav_menu = button_menu
      endif
    endif
  endif
-%}

<script>
  {%- if section.settings.sticky == 'sticky-menu' and section.settings.enable_sticky_header and section.settings.menu_scroll == false -%}
  document.documentElement.classList.add('t1sn');
  {%- endif -%}
  {%- if search_compact -%}
  document.documentElement.classList.add('t1sr');
  {%- endif -%}
  {%- unless button_menu -%}
  document.documentElement.classList.add('t1nb');
  {%- endunless -%}
  {%- if menu == false and button_menu == false -%}
  document.documentElement.classList.add('t1mn');
  {%- elsif has_menu_bar == false -%}
  document.documentElement.classList.add('t1nn');
  {%- endif -%}
  {%- if search_centered -%}
  document.documentElement.classList.add('search-compact-is-centered');
  {%- endif -%}
</script>

<style>
  :root {
  --content_p:                  {{ section.settings.spacing_desktop }}px;
  {% comment %} header width {% endcomment %}
  --ghw:  {% if section.settings.width == 2000 %}10000{% else %}{{ section.settings.width }}{% endif %}px;
  {% comment %} top bar {% endcomment %}
  --custom_top_up_bg:           var(--{{ section.settings.top_bar_color }}_bg);
  --custom_top_up_fg:           var(--{{ section.settings.top_bar_color }}_fg);
  --custom_top_up_fg_hover:     var(--{{ section.settings.top_bar_color }}_btn_bg);
  --custom_top_up_h: 	 	{{ section.settings.top_bar_height | split: '|' | first }};
  {% comment %} main {% endcomment %}
  --header_mih:                 {{ section.settings.header_height | split: '|' | first }};
  {%- if section.settings.header_background_color != blank -%}
    --custom_top_main_bg:       {{ section.settings.header_background_color }};
  {%- else -%}
    --custom_top_main_bg:       var(--{{ section.settings.header_color }}_bg);
  {%- endif -%}
  --custom_top_main_fg:         var(--{{ section.settings.header_color }}_fg);
  {%- if button_menu -%}
    {%- liquid
      assign button_color = button_block.settings.button_style | split: ' ' | first
      assign button_style = button_block.settings.button_style | split: ' ' | last
    -%}
    {% if button_color != 'primary' and button_color != 'secondary' and button_color != 'tertiary' %}
      --custom_top_main_link_bg:    var(--{{ button_color }}_bg);
      --custom_top_main_link_dark:  var(--{{ button_color }}_bg_dark);
      --custom_top_main_link_text:  var(--{{ button_color }}_fg);
    {% else %}
      --custom_top_main_link_bg:    var(--{{ section.settings.header_color }}_{{ button_color }}_btn_bg);
      --custom_top_main_link_dark:  var(--{{ section.settings.header_color }}_{{ button_color }}_btn_bg_dark);
      --custom_top_main_link_text:  var(--{{ section.settings.header_color }}_{{ button_color }}_btn_fg);
    {% endif %}
  {% endif %}
  {% comment %} menubar {% endcomment %}
  {% if has_menu_bar %} --custom_top_nav_h: {{ section.settings.menubar_height }};{% endif %}
  --custom_top_nav_bg:          var(--{{ section.settings.menubar_color }}_bg);
  --custom_top_nav_scrollbar_bg:var(--{{ section.settings.menubar_color }}_fg_sat);
  --custom_top_nav_fz:          {{ section.settings.menu_font_size }}px;
  --mobile_nav_fz:              {{ section.settings.menu_font_size_mobile }}px;
  --custom_top_nav_ls:          {% if section.settings.menu_letter_spacing == '0' %}normal{% else %}{{ section.settings.menu_letter_spacing | divided_by: 100.0 }}em{% endif %};
  --custom_top_nav_fw:          {{ section.settings.menu_font_weight }};
  {% if section.settings.enable_menu_subs_font_size %}
    --custom_top_nav_fz_sub:     {{ section.settings.menu_subs_font_size }}px;
  {% endif %}
  {% unless section.settings.header_shadow %}
    --custom_top_main_sh: none;
  {% else %}
    --custom_top_main_sh: 0 9px 32px rgba(0,0,0,{{ section.settings.header_shadow_opacity | divided_by: 100.0 }});
  {% endunless %}
  {% if section.settings.header_border_opacity != 0 %}
    {%- liquid
      assign header_color_scheme = settings.color_schemes | where: 'id', section.settings.header_color.id | first
      assign header_border_opacity = section.settings.header_border_opacity | divided_by: 100.0
      assign header_border_color = header_color_scheme.settings.primary_bd | color_to_rgb | color_modify: 'alpha', header_border_opacity
    -%}
    --custom_top_nav_bd_op:       {{ header_border_opacity }};
    --custom_top_nav_bd:          {{ header_border_color }};
  {% else %}
    --custom_top_nav_bd_op:     0;
    --custom_top_nav_bd:        transparent;
  {% endif %}
  --custom_top_nav_fg:          var(--{{ section.settings.menubar_color }}_fg);
  --custom_top_nav_fg_hover:    var(--{{ section.settings.menubar_color }}_link_hover_color);
  {% comment %} searchbar {% endcomment %}
  {%- if section.settings.show_searchbar_border -%}
    --custom_top_search_bd:     var(--{{ section.settings.header_color }}_bd);
  {%- else -%}
    --custom_top_search_bd:     transparent;
  {%- endif -%}
  --custom_top_search_bg:       var(--{{ section.settings.header_color }}_input_bg);
  --custom_top_search_fg:       var(--{{ section.settings.header_color }}_input_fg);
  --custom_top_search_pl:       var(--{{ section.settings.header_color }}_input_pl);
  }
  @media only screen and (max-width: 760px) {
  :root {
  --content_p:                  {{ section.settings.spacing_mobile }}px;
  --custom_top_search_h:        {{ section.settings.searchbar_height_mobile }}px;
  }
  {%- if settings.logo -%}
    .shopify-section-header { --logo_w: {{ settings.logo_width_mobile }}px; }
  {%- endif -%}
  html #root .shopify-section-header #search.full:not(.no-autocomplete) > p, html #root .shopify-section-header #search.full:not(.no-autocomplete) > fieldset > p {
  border-color: var(--{{ settings.dropdown_color }}_bg_secondary);
  background: var(--{{ settings.dropdown_color }}_bg_secondary);
  }
  html #search > a.toggle, #search p > a.search-back {
  --custom_top_search_fg: var(--{{ settings.dropdown_color }}_fg);
  }
  }
  @media only screen and (min-width: 761px) {
  :root {
  --custom_top_search_h:        clamp(calc(var(--main_fz) + 20px), {{ section.settings.searchbar_height }}px, calc(var(--header_mih) - 5px));
  --search_w:               {% if section.settings.searchbar_width == 600 %}100%{% else %}{{ section.settings.searchbar_width }}px{% endif %};
  }
  #nav-bar > ul > li > ul > li > ul > li > a, #nav > ul > li > ul > li > ul > li > a, #nav-bar > ul > li > ul > li > ul > li > ul > li > a, #nav > ul > li > ul > li > ul > li > ul > li > a { --main_fw: {{ section.settings.menu_subs_font_weight }}; font-weight: {{ section.settings.menu_subs_font_weight }}; }
  {%- if settings.logo -%}
    .shopify-section-header { --logo_w: {{ settings.logo_width }}px; }
  {%- endif -%}
  }
  @media only screen and (max-width: 62.5em ) {
  #shopify-section-{{ section.id }} #nav {
  --custom_drop_nav_bg:         var(--{{ section.settings.mobile_menu_color }}_bg);
  --custom_drop_nav_fg:         var(--{{ section.settings.mobile_menu_color }}_fg);
  --custom_drop_nav_fg_text:    var(--{{ section.settings.mobile_menu_color }}_fg);
  --custom_drop_nav_fg_hover:   var(--{{ section.settings.mobile_menu_color }}_btn_bg);
  --custom_drop_nav_head_bg:    var(--{{ section.settings.mobile_menu_color }}_bg_var);
  --custom_drop_nav_head_fg:    var(--{{ section.settings.mobile_menu_color }}_fg);
  --custom_drop_nav_bd:         var(--{{ section.settings.mobile_menu_color }}_bd);
  }
  }
  @media only screen and (min-width: 62.5em ) {
  {% if logo_text_centered or logo_centered %}
    {% unless search_compact %}
      #shopify-section-{{ section.id }} #header-inner #search { max-width: min(var(--search_w), calc(50% - var(--logo_w) / 2)); }
      #shopify-section-{{ section.id }} #header-inner.text-center-logo #search { max-width: min(var(--search_w), calc(50% - var(--maw_la) / 2)); }
    {% endunless %}
  {% endif %}
  }
  #root #header .f8vl *:not(a[class*="overlay-"], button[class*="overlay-"]), #root .m6pn.f8vl *:not(a[class*="overlay-"], button[class*="overlay-"]) {
  --secondary_btn_text:             var(--{{ settings.dropdown_color }}_btn_{% if button_style == 'inv' %}bg{% else %}fg{% endif %});
  --secondary_bg_btn:               var(--{{ settings.dropdown_color }}_btn_bg);
  --secondary_bg_btn_dark:          var(--{{ settings.dropdown_color }}_btn_bg_dark);
  --secondary_bg_btn_fade:          var(--{{ settings.dropdown_color }}_btn_bg_dark);
  --white:                      var(--{{ settings.dropdown_color }}_btn_fg);
  }
  :root {
  --header_mih:                 {{ section.settings.header_height | split: '|' | last }};
  --custom_top_up_h:       {{ section.settings.top_bar_height | split: '|' | last }};
  }
  #root .shopify-section-header #header-inner > .link-btn a:first-child:after {
  content: "\{{ section.settings.menu_icon | split: '|' | last }}";
  }
  }
  #root #header .f8vl .submit, #root .m6pn.f8vl .submit {
  --secondary_btn_text:             var(--{{ settings.dropdown_color }}_btn_fg);
  --secondary_bg_btn:               var(--{{ settings.dropdown_color }}_btn_bg);
  --secondary_bg_btn_dark:          var(--{{ settings.dropdown_color }}_btn_bg_dark);
  --secondary_bg_btn_fade:          var(--{{ settings.dropdown_color }}_btn_bg_dark);
  --secondary_bg_fade:          var(--secondary_bg_btn_fade);
  }
  {% if section.settings.show_header_border == 'top' %}
    #nav.plain:before, #nav-bar.plain:before { border-bottom-width: 0!important; }
  {% elsif section.settings.show_header_border == 'bottom' %}
    #nav.plain:before, #nav-bar.plain:before { border-top-width: 0!important; }
  {% endif %}
  {% comment %} cart icon {% endcomment %}
  #root .icon-cart:before { content: "\{{ section.settings.cart_icon | split: '|' | last }}"; }
  #nav-user li:not(.link-btn) {
  {% assign cart_icon_color = section.settings.cart_icon_color %}
  {% if cart_icon_color != 'primary' and cart_icon_color != 'secondary' and cart_icon_color != 'tertiary' %}
    --custom_top_main_link_bg:    var(--{{ cart_icon_color }}_bg);
    --custom_top_main_link_dark:  var(--{{ cart_icon_color }}_bg_dark);
    --custom_top_main_link_text:  var(--{{ cart_icon_color }}_fg);
  {% else %}
    --custom_top_main_link_bg:    var(--{{ section.settings.header_color }}_{{ cart_icon_color }}_btn_bg);
    --custom_top_main_link_dark:  var(--{{ section.settings.header_color }}_{{ cart_icon_color }}_btn_bg_dark);
    --custom_top_main_link_text:  var(--{{ section.settings.header_color }}_{{ cart_icon_color }}_btn_fg);
  {% endif %}
  }
  {% if section.settings.menu_enable_custom_text_styling %}
    #nav-bar, #nav { --custom_top_main_fg: var(--{% if has_menu_bar %}{{ section.settings.menubar_color }}{% else %}{{ section.settings.header_color }}{% endif %}_fg); --custom_top_nav_fg: var(--custom_top_main_fg); }
  {% endif %}
</style>

{%- capture logo -%}
  <p id="logo" class="{% if section.settings.layout_mobile == 'logo_right' %}mobile-text-end{% endif %}">
    <a href="{{ routes.root_url }}">
      {%- if settings.logo -%}
        {%- capture sizes %}(min-width: 760px) {{ settings.logo_width }}px, {{ settings.logo_width_mobile }}px{%- endcapture -%}
        {%- capture widths %}{{ settings.logo_width }},{{ settings.logo_width | times: 2 }}{%- endcapture -%}
        {%- capture widths_mobile %}{{ settings.logo_width_mobile }},{{ settings.logo_width_mobile | times: 2 }}{%- endcapture -%}
        {%- liquid
          assign width = settings.logo_width | times: 2
          assign alt = section.settings.logo.alt | default: shop.name | escape
          assign logo_height = settings.logo_width | divided_by: settings.logo.aspect_ratio
        -%}
        <picture>
          {%- if settings.mobile_logo -%}
            <source
                    media="(max-width: 760px)"
                    srcset="
                {%- assign widths_mobile_arr = widths_mobile | split: ',' -%}
                {%- for width in widths_mobile_arr -%}
                  {{ settings.mobile_logo | image_url: width: width }} {{ width }}w,
                {%- endfor -%}
              ">
          {%- endif -%}
          {{ settings.logo | image_url: width: width | image_tag:
          class: 'logo-img',
          height: logo_height,
          width: settings.logo_width,
          widths: widths,
          alt: alt,
          sizes: sizes
          }}
        </picture>
      {%- else -%}
        {%- if section.settings.show_default_logo and section.settings.default_logo_svg != blank -%}
          {{ section.settings.default_logo_svg }}
        {%- else -%}
          <span>{{ shop.name }}</span>
        {%- endif -%}
      {%- endif -%}
    </a>
    {%- if settings.logo -%}
      <style>
        #logo {
        --logo_w: {{ settings.logo_width }}px;
        }
        @media only screen and (max-width: 47.5em) {
        #logo {
        --logo_w: {{ settings.logo_width_mobile }}px;
        }
        }
      </style>
    {%- elsif section.settings.show_default_logo and section.settings.default_logo_svg != blank -%}
      <style>
        #logo {
        --logo_w: 96px;
        }
        @media only screen and (max-width: 47.5em) {
        #logo {
        --logo_w: 80px;
        }
        }
        #logo svg {
          width: var(--logo_w);
          height: auto;
        }
      </style>
    {%- endif -%}
  </p>
{%- endcapture -%}

{%- capture button -%}
  <p class="link-btn{% if section.settings.header_icons %} menu-text-style ff-{{ section.settings.menu_font }}{% endif %}">
    <a class="text-justify{% if section.settings.menubar_caps %} mobile-text-uppercase{% endif %}{% if button_style == 'inv' %} inv{% endif %}" href="{{ routes.root_url }}" aria-controls="nav">{{ 'header.navigation.view_catalog' | t }} <i aria-hidden="true" class="icon-chevron-down"></i></a>
    <a class="search-compact{% if search_right %} text-end{% endif %}" href="#search" aria-label="search" aria-controls="search">
      {% if section.settings.header_icons %}
        <i aria-hidden="true" class="icon-zoom mobile-only"></i> <span class="mobile-hide{% if section.settings.menubar_caps %} text-uppercase{% endif %}">{{ 'search.title' | t }}</span>
      {% else %}
        <i aria-hidden="true" class="icon-zoom"></i> <span class="hidden">{{ 'search.title' | t }}</span>
      {% endif %}
    </a>
  </p>
{%- endcapture -%}

{%- capture searchbar -%}
  <form action="{{ routes.search_url }}" method="get" id="search" class="
    {% if search_compact %}compact{% endif %}
    {% if search_centered %}text-center-sticky{% endif %}
    {% if search_right %}text-end{% endif %}
    {% if search_compact %}wide text-center-sticky{% endif %}
    {% unless settings.enable_search_drawer %}no-autocomplete{% endunless %}
    blur solid
    {% if section.settings.search_compact_mobile %}m-pos-up{% else %}no-bg {% unless section.settings.search_mobile_bd == 'none' %}{{ section.settings.search_mobile_bd }}{% unless section.settings.search_mobile_bd contains 'bd-t' %} no-pd-t{% endunless %}{% unless section.settings.search_mobile_bd contains 'bd-b' %} no-pd-b{% endunless %}{% endunless %}{% endif %}
    {% if search_right and search_compact %}compact-handle-mobile{% else %}compact-handle{% endif %}
    "
        style="--main_fz:{{ section.settings.searchbar_font_size }};--price_fz:{{ section.settings.searchbar_font_size }};font-size:{{ section.settings.searchbar_font_size }};--placeholder_fz:{{ section.settings.searchbar_font_size }};"
  >
    <fieldset>
      <legend>{{ 'search.title' | t }}</legend>
      <p>
        <label for="search_main">{{ 'search.title' | t }}</label>
        <input type="search" id="search_main" name="q" placeholder="{{ 'search.search_form.placeholder' | t }}" autocomplete="off" required>
        <button type="submit" class="override">{{ 'search.search_form.submit' | t }}</button>
      </p>
      <div id="livesearch">
        {%- if settings.search_drawer_popular_collections.count > 0 or settings.search_drawer_popular_products.count > 0 -%}
          <div class="search-placeholders">
            {%- if settings.search_drawer_popular_collections.count > 0 -%}
              {%- if settings.search_drawer_popular_collections_title != empty -%}<p class="strong">{{ settings.search_drawer_popular_collections_title }}</p>{%- endif -%}
              <ul class="l4pl">
                {%- for collection in settings.search_drawer_popular_collections -%}
                  <li><a href="{{ collection.url }}">{{ collection.title }}</a></li>
                {%- endfor -%}
              </ul>
            {%- endif -%}
            {%- if settings.search_drawer_popular_products.count > 0 -%}
              {%- if settings.search_drawer_popular_products_title != empty -%}<p class="strong margin-10">{{ settings.search_drawer_popular_products_title }}</p>{%- endif -%}
              <ul class="l4ca compact">
                {%- for product in settings.search_drawer_popular_products -%}
                  <li class="{% if product.featured_media == blank %} no-img{% endif %}{% if settings.fill_product_images %} cover{% endif %}{% if product.compare_at_price > product.price %} has-discount{% endif %}">
                    {%- if product.featured_media != blank -%}
                      <figure{% if settings.multiply_product_images == 'multiply' %} class="img-multiply"{% elsif settings.multiply_product_images == 'multiply-bg' %} class="img-multiply-bg"{% endif %}>
                        <picture>
                          <img src="{{ product.featured_media | image_url: width: 140 }}"
                               srcset="{{ product.featured_media | image_url: width: 70 }} 1x,{{ product.featured_media | image_url: width: 140 }} 2x"
                               alt="{{ product.featured_media.alt }}"
                               width="70"
                               height="71"
                          >
                        </picture>
                      </figure>
                    {%- endif -%}
                    <section>
                      <div class="cols align-middle">
                        <div>
                          <h2>
                            {%- if settings.search_drawer_show_vendor and product.vendor != "vendor-unknown" and product.vendor != shop.name -%}
                              <span class="small">{{ product.vendor }}</span>
                            {%- endif -%}
                            <a href="{{ product.url }}">{{ product.title }} </a>
                          </h2>
                        </div>
                        {%- if settings.search_drawer_show_price -%}
                          <p class="price s1pr">
                            {%- if product.compare_at_price > product.price -%}<span class="old-price">{{ product.compare_at_price | money }}</span>&nbsp;{%- endif -%}
                            {{ product.price | money }}
                          </p>
                        {%- endif -%}
                      </div>
                    </section>
                  </li>
                {%- endfor -%}
              </ul>
            {%- endif -%}
          </div>
        {%- else -%}
          <div class="cols">
            <p>{{ 'search.results' | t }}</p>
          </div>
        {%- endif -%}
      </div>
    </fieldset>
  </form>
{%- endcapture -%}

{%- if section.settings.show_top_bar -%}
  <nav
    id="nav-top"
    class="ff-{{ section.settings.top_bar_font }}"
    style="--main_fz:{{ section.settings.top_bar_font_size }}"
  >
    {%- if usp_blocks -%}
      <ul
        class="l4us mobile-text-center{% if usp_blocks.size > 1 %}{% if section.settings.enable_usp_slider %} slider slider-single{% endif %}{% unless section.settings.enable_usp_slider_arrows %} no-arrows{% endunless %}{% endif %}"
        {% if section.settings.enable_usp_slider_autoplay %}
          data-autoplay="{{ section.settings.usp_slider_autoplay_seconds | times: 1000 }}"
        {% endif %}
      >
        {%- for block in usp_blocks limit: 3 -%}
          {%- if block.settings.usp != empty -%}
            <li
              {% if block.settings.icon != 'default' or block.settings.image %}
                class="no-checks"
              {% endif %}
              style="--cols: 8px;"
              {{ block.shopify_attributes }}
            >
              {% if block.settings.image or block.settings.icon != 'none' %}
                {% if block.settings.image %}
                  {%- assign image_width = block.settings.image_width -%}
                  {%- assign image_width_2 = block.settings.image_width | times: 2 -%}
                  <figure
                    class="m0 no-border"
                    style="width: calc({{ image_width }}px + var(--cols));"
                  >
                    <picture>
                      <img
                        src="{{ block.settings.image | image_url: height: image_width }}"
                        srcset="{{ block.settings.image | image_url: height: image_width }} 1x,{{ block.settings.image | image_url: height: image_width_2 }} 2x"
                        height="50"
                        width="{{ image_width }}"
                        style="width:{{ image_width }}px!important; height: auto!important;"
                        alt="{{ block.settings.image.alt | default: block.settings.title | escape }}"
                        loading="{% if section.index > 1 or forloop.first == false %}lazy{% else %}eager{% endif %}"
                      >
                    </picture>
                  </figure>
                {% elsif block.settings.icon != 'default' %}
                  {%- render 'icons', icon: block.settings.icon -%}
                {% endif %}
              {% endif %}
              <span>
                {%- assign usp_text = block.settings.usp | remove: '<p>' | remove: '</p>' | split: '|' -%}
                {%- assign cycle_group = 'cycle-' | append: forloop.index -%}
                {%- for text in usp_text -%}
                  {%- capture cycle -%}{% cycle cycle_group: 'odd', 'even' %}{%- endcapture -%}
                  {%- if cycle == 'odd' -%}
                    <span>{{ text }}</span>
                  {%- else -%}
                    <span class="overlay-theme">{{ text }}</span>
                  {%- endif -%}
                {%- endfor -%}
              </span>
            </li>
          {%- endif -%}
        {%- endfor -%}
      </ul>
    {%- endif -%}
    <ul data-type="top-nav">
      {%- for block in liquid_blocks -%}
        {% if block.settings.liquid != empty and block.settings.location == 'top' %}
          <li class="desktop-only">
            {{ block.settings.liquid }}
          </li>
        {% endif %}
      {%- endfor -%}
      {% if section.settings.navbar_menu_item_text %}
        <li>
          <a
            href="{{ section.settings.navbar_menu_item_link }}"
            {% unless section.settings.navbar_menu_item_link %}
              role="none" style="pointer-events: none"
            {% endunless %}
          >
            {% if section.settings.navbar_menu_item_icon != 'none' %}
              {%- render 'icons', icon: section.settings.navbar_menu_item_icon -%}
            {% endif %}
            <span>{{ section.settings.navbar_menu_item_text }}</span>
          </a>
        </li>
      {% endif %}
      {% if settings.show_accessibility and settings.enable_accessibility_default == false %}
        <li>
          <a
            href="#"
            class="link-accessible"
            aria-label="{{ 'general.accessibility.toggle_accessibility_mode' | t }}"
            ><i aria-hidden="true" class="icon-text-size"></i>
            <span class="hidden">{{ 'general.accessibility.toggle_accessibility_mode' | t }}</span></a
          >
        </li>
      {% endif %}
      {%- liquid
        if localization.available_countries.size > 1
          render 'language-country-selector', display: 'country', extra_classes: 'mobile-nav-only', hide: hide_country_selector
        endif
        if localization.available_languages.size > 1
          render 'language-country-selector', display: 'language', extra_classes: 'mobile-nav-only', hide: hide_language_selector
        endif
      -%}
    </ul>
  </nav>
{%- elsif has_menu_bar == false %}
  <nav
    id="nav-top"
    class="hidden ff-{{ section.settings.menu_font }}"
    aria-label="{{ 'general.accessibility.menu' | t }}"
  >
    {%- for block in liquid_blocks -%}
      {% if block.settings.liquid != empty and block.settings.location == 'top' %}
        <li class="mobile-nav-only">
          {{ block.settings.liquid }}
        </li>
      {% endif %}
    {%- endfor -%}
    <ul data-type="top-nav">
      {% if section.settings.navbar_menu_item_text %}
        <li>
          <a
            href="{{ section.settings.navbar_menu_item_link }}"
            {% unless section.settings.navbar_menu_item_link %}
              role="none" style="pointer-events: none"
            {% endunless %}
          >
            {% if section.settings.navbar_menu_item_icon != 'none' %}
              {%- render 'icons', icon: section.settings.navbar_menu_item_icon -%}
            {% endif %}
            {{ section.settings.navbar_menu_item_text }}
          </a>
        </li>
      {% endif %}
      {%- liquid
        if localization.available_countries.size > 1
          render 'language-country-selector', display: 'country', extra_classes: 'mobile-nav-only', hide: hide_country_selector
        endif
        if localization.available_languages.size > 1
          render 'language-country-selector', display: 'language', extra_classes: 'mobile-nav-only', hide: hide_language_selector
        endif
      -%}
    </ul>
  </nav>
{% endif %}

<div id="header-outer">
  <div id="header">
    <div
      id="header-inner"
      class="
        {% if section.settings.sticky == 'sticky-menu'%}{% if section.settings.enable_sticky_header and section.settings.menu_scroll == false and has_menu_bar %}sticky-nav{% else %}no-sticky{% endif %}{% elsif section.settings.enable_sticky_header == false %}no-sticky{% endif %}
        {% if button_hidden or button_menu == false %}hide-btn{% endif %}
        {% unless button_block.settings.show_mobile %}hide-btn-mobile{% endunless %}
        {% unless section.settings.search_compact_mobile %}mobile-visible-search{% endunless %}
        {% if section.settings.layout_mobile == 'logo_center' %}text-center-mobile{% endif %}
        {% if logo_centered %}text-center-sticky text-center-logo{% endif %}
        {% if logo_text_centered %}text-center-logo{% endif %}
        {% unless section.settings.logo %}logo-text{% endunless %}
      "
    >
      {%- for element in elements_order -%}
        {%- case element -%}
          {%- when 'button' -%}
            {{ button }}
          {%- when 'logo' -%}
            {{ logo }}
          {%- when 'searchbar' -%}
            {{ searchbar }}
          {%- when 'menu' -%}
            {% comment %} START #nav-bar - in-header-menu {% endcomment %}
            {% if menu %}
              <div id="nav-outer">
                <nav
                  id="nav-bar"
                  aria-label="{{ 'general.accessibility.menu' | t }}"
                  class="hr  {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' %}nav-scroll-wrapper{% endif %} text-{{ section.settings.menubar_alignment }}{% if section.settings.header_border_opacity != 0 %} plain{% if section.settings.header_border_width == 'boxed' and section.settings.header_shadow == false %} no-wide{% endif %}{% else %} no-bd{% endif %}{% if section.settings.sticky == 'sticky-menu' and section.settings.enable_sticky_header %} sticky-menu{% endif %} ff-{{ section.settings.menu_font }} {{ section.settings.menu_layout }}"
                >
                  <ul
                    data-type="horizontal-nav"
                    {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' %}
                      class="nav-scroll"
                    {% endif %}
                  >
                    {%- render 'header-menu',
                      menu: menu,
                      show_dropdown_images: section.settings.show_dropdown_images,
                      show_dropdown_images_autofill: section.settings.show_dropdown_images_autofill,
                      show_dropdown_images_rounded: section.settings.show_dropdown_images_rounded,
                      show_dropdown_images_subs: section.settings.show_dropdown_images_subs,
                      layout: menu_layout,
                      main_menu_items_clickable: section.settings.main_menu_items_clickable,
                      menubar_caps: section.settings.menubar_caps,
                      sale_highlight: section.settings.sale_highlight,
                      sale_highlight_item: section.settings.sale_highlight_item,
                      manual_collapse: false,
                      submenu_items_limit: section.settings.submenu_items_limit,
                      custom_menu_items_per_columns: section.settings.custom_menu_items_per_columns,
                      menu_items_limit: section.settings.menu_items_limit,
                      blocks: section.blocks,
                      header_width: section.settings.width,
                      main_menu_show_lines: section.settings.main_menu_show_lines,
                      menu_subs_font: section.settings.menu_subs_font,
                      show_chevrons: section.settings.menu_show_chevrons
                    -%}
                  </ul>
                </nav>
              </div>
            {% endif %}
            {% comment %} END #nav-bar - in-header-menu {% endcomment %}
        {%- endcase -%}
      {%- endfor -%}
      <nav
        id="nav-user"
        {% if section.settings.header_icons %}
          class="menu-text-style ff-{{ section.settings.menu_font }}{% if section.settings.menubar_caps %} text-uppercase{% endif %}"
        {% endif %}
      >
        <ul data-type="user-nav">
          {%- for block in app_blocks -%}
            {%- render block -%}
          {%- endfor -%}
          {%- for block in liquid_blocks -%}
            {% if block.settings.liquid != empty %}
              {% if block.settings.location == 'main' %}
                <li class="desktop-only mobile-hide">
                  {{ block.settings.liquid }}
                </li>
                {% if block.settings.show_mobile %}
                  <li class="mobile-only">
                    <span>{{ block.settings.liquid }}</span>
                  </li>
                {% endif %}
              {% elsif block.settings.location == 'top' and block.settings.show_mobile %}
                <li class="mobile-only">
                  <span>{{ block.settings.liquid }}</span>
                </li>
              {% endif %}
            {% endif %}
          {%- endfor -%}
          {% if section.settings.navbar_menu_item_text %}
            <li class="mobile-only">
              <a
                href="{{ section.settings.navbar_menu_item_link }}"
                {% unless section.settings.navbar_menu_item_link %}
                  role="none" style="pointer-events: none"
                {% endunless %}
              >
                {% if section.settings.navbar_menu_item_icon != 'none' %}
                  {%- render 'icons', icon: section.settings.navbar_menu_item_icon -%}
                {% endif %}
                {{ section.settings.navbar_menu_item_text }}
              </a>
            </li>
          {% endif %}
          {%- if section.settings.extra_image -%}
            {%- assign default_alt = 'header.extra_img_default_alt' | t %}
            <li class="mobile-hide">
              {%- if section.settings.extra_image_link != blank -%}
                <a
                  href="{{ section.settings.extra_image_link }}"
                  rel="external noopener"
                  target="external"
                >
              {%- endif -%}
              {% if section.settings.darkmode_extra_image %}
                <img
                  class="header-extra-img dark-only"
                  srcset="{%- render 'image-srcset', image: section.settings.darkmode_extra_image, max_width: 400 -%}"
                  src="{{ section.settings.darkmode_extra_image | image_url: width: section.settings.extra_image_width }}"
                  width="{{ section.settings.extra_image_width }}"
                  height="35"
                  alt="{{ section.settings.darkmode_extra_image.alt | default: default_alt | escape }}"
                >
              {% endif %}
              <img
                class="header-extra-img{% if section.settings.darkmode_extra_image %} dark-hide{% endif %}"
                srcset="{%- render 'image-srcset', image: section.settings.extra_image, max_width: 400 -%}"
                src="{{ section.settings.extra_image | image_url: width: section.settings.extra_image_width }}"
                width="{{ section.settings.extra_image_width }}"
                height="35"
                alt="{{ section.settings.extra_image.alt | default: default_alt | escape }}"
              >
              {%- if section.settings.extra_image_link != blank -%}</a>{%- endif -%}
              <style>
                #nav-user > ul > li > a img.header-extra-img {
                {% if settings.logo %}
                  width: {{ section.settings.extra_image_width }}px;
                {% else %}
                  max-height: 44px;
                {% endif %}
                }
              </style>
            </li>
          {%- endif -%}
          {%- assign show_account = true -%}
          {%- if section.settings.enable_button and section.settings.button_text != empty -%}
            {%- liquid
              assign show_button = true
              if section.settings.button_url == empty or section.settings.button_url == blank
                if shop.customer_accounts_enabled and section.settings.disable_online_store == false and customer == null
                  assign show_account = false
                else
                  assign show_button = false
                endif
              endif
            -%}
            {%- if show_button -%}
              {%- liquid
                assign button_color = section.settings.button_style | split: ' ' | first
                assign button_style = section.settings.button_style | split: ' ' | last
              -%}
              {% capture button_styling %}
                {% if button_color != 'primary' and button_color != 'secondary' and button_color != 'tertiary' %}
                  --custom_top_main_link_bg:    var(--{{ button_color }}_bg);
                  --custom_top_main_link_dark:  var(--{{ button_color }}_bg_dark);
                  --custom_top_main_link_text:  var(--{{ button_color }}_fg);
                {% else %}
                  --custom_top_main_link_bg:    var(--{{ section.settings.header_color }}_{{ button_color }}_btn_bg);
                  --custom_top_main_link_dark:  var(--{{ section.settings.header_color }}_{{ button_color }}_btn_bg_dark);
                  --custom_top_main_link_text:  var(--{{ section.settings.header_color }}_{{ button_color }}_btn_fg);
                {% endif %}
              {% endcapture %}
              <li class="link-btn mobile-hide" style="{{ button_styling }}">
                <a
                  href="{% if section.settings.button_url != empty and section.settings.button_url != blank %}{{ section.settings.button_url }}{% else %}{{ routes.account_login_url }}{% endif %}"
                  class="{% if button_style == 'inv' %} inv{% endif %}"
                >
                  {{- section.settings.button_text -}}
                </a>
              </li>
            {%- endif -%}
          {%- endif -%}
          {% if search_right and search_compact %}
            <li class="search">
              <a href="#search" aria-controls="search">
                {% if section.settings.header_icons %}
                  <span>{{ 'search.title' | t }}</span>
                {% else %}
                  <i aria-hidden="true" class="icon-zoom"></i>
                  <span class="hidden">{{ 'search.title' | t }}</span>
                {% endif %}
              </a>
            </li>
          {% endif %}
          {%- liquid
            if section.settings.enable_country_selector and localization.available_countries.size > 1
              if section.settings.header_icons
                render 'language-country-selector', display: 'country', extra_classes: 'text-only'
              else
                render 'language-country-selector', display: 'country'
              endif
            endif
            if section.settings.enable_language_selector and localization.available_languages.size > 1
              if section.settings.header_icons
                render 'language-country-selector', display: 'language', extra_classes: 'text-only'
              else
                render 'language-country-selector', display: 'language'
              endif
            endif
          -%}
          {%- if section.settings.enable_socials -%}
            {%- capture socials -%}
              {%- if settings.social_instagram -%}instagram|{{settings.social_instagram}}|{{ settings.social_instagram_name }}->{%- endif -%}
              {%- if settings.social_pinterest -%}pinterest|{{settings.social_pinterest}}|{{ settings.social_pinterest_name }}->{%- endif -%}
              {%- if settings.social_youtube -%}youtube|{{settings.social_youtube}}|{{ settings.social_youtube_name }}->{%- endif -%}
              {%- if settings.social_facebook -%}facebook|{{settings.social_facebook}}|{{ settings.social_facebook_name }}->{%- endif -%}
              {%- if settings.social_twitter -%}twitter|{{settings.social_twitter}}|{{ settings.social_twitter_name }}->{%- endif -%}
              {%- if settings.social_tiktok -%}tiktok|{{settings.social_tiktok}}|{{ settings.social_tiktok_name }}->{%- endif -%}
              {%- if settings.social_tumblr -%}tumblr|{{settings.social_tumblr}}|{{ settings.social_tumblr_name }}->{%- endif -%}
              {%- if settings.social_snapchat -%}snapchat|{{settings.social_snapchat}}|{{ settings.social_snapchat_name }}{%- endif -%}
            {%- endcapture -%}
            {%- assign socials = socials | split: '->' -%}
            {%- if socials -%}
              {%- liquid
                assign first_social_info = socials | first | split: '|'
                assign first_social_name = first_social_info[2]
              -%}
              {%- if socials.size > 0 -%}
                <li
                  {% if socials.size > 1 %}
                    class="sub mobile-hide"
                  {% endif %}
                >
                  {%- if socials.size > 1 -%}
                    <a
                      href="./"
                      class="toggle"
                      aria-label="{{ first_social_name | escape }}, {{ 'general.read_more.read_more' | t }}"
                      ><i aria-hidden="true" class="icon-{{ first_social_info[0] }}"></i
                      ><span>{{ first_social_name }}</span></a
                    >
                    <ul>
                      {%- for social in socials -%}
                        {%- liquid
                          assign social_info = social | split: '|'
                          assign social_name = social_info[2]
                          assign social_translation = 'socials.' | append: social_info[0]
                        -%}
                        <li>
                          <a
                            aria-label="{{ social_translation | t }}"
                            href="{{ social_info[1] }}"
                            rel="external noopener"
                            target="external"
                            ><i aria-hidden="true" class="icon-{{ social_info[0] }}"></i>
                            {{- social_name -}}
                          </a>
                        </li>
                      {%- endfor -%}
                    </ul>
                  {%- else -%}
                    <a href="{{ first_social_info[1] }}"
                      ><i aria-hidden="true" class="icon-{{ first_social_info[0] }}"></i
                      ><span>{{ first_social_name }}</span></a
                    >
                  {%- endif -%}
                </li>
              {%- else -%}
                {%- if shop.brand.metafields.social_links.size > 0 -%}
                  {%- liquid
                    assign first_social = shop.brand.metafields.social_links.first
                    assign first_social_name = first_social[1] | split: '/' | last
                    if first_social_name == empty
                      assign first_social_name = first_social[1] | split: '/'
                      assign next_to_last = first_social_name.size | minus: 1
                      assign first_social_name = first_social_name[next_to_last]
                    endif
                    assign first_social_name = first_social_name | remove: '.tumblr.com'
                  -%}
                  <li
                    {% if shop.brand.metafields.social_links.size > 1 %}
                      class="sub mobile-hide"
                    {% endif %}
                  >
                    {%- if shop.brand.metafields.social_links.size.size > 1 -%}
                      <a
                        href="./"
                        class="toggle"
                        aria-label="{{ first_social_name | escape }}, {{ 'general.read_more.read_more' | t }}"
                        ><i aria-hidden="true" class="icon-{{ first_social[0] }}"></i
                        ><span>{{ first_social_name }}</span></a
                      >
                      <ul>
                        {%- for social in shop.brand.metafields.social_links -%}
                          {%- liquid
                            assign social_translation = 'socials.' | append: social[0]
                            assign social_name = social[1] | split: '/' | last
                            if social_name == empty
                              assign social_name = social[1] | split: '/'
                              assign next_to_last = social_name.size | minus: 1
                              assign social_name = social_name[next_to_last]
                            endif
                            assign social_name = social_name | remove: '.tumblr.com'
                          -%}
                          <li>
                            <a
                              aria-label="{{ social_translation | t }}"
                              href="{{ social[1] }}"
                              rel="external noopener"
                              target="external"
                              ><i aria-hidden="true" class="icon-{{ social[0] }}"></i>
                              {{- social_name -}}
                            </a>
                          </li>
                        {%- endfor -%}
                      </ul>
                    {%- else -%}
                      <a href="{{ first_social[1] }}"
                        ><i aria-hidden="true" class="icon-{{ first_social[0] }}"></i
                        ><span>{{ first_social_name }}</span></a
                      >
                    {%- endif -%}
                  </li>
                {%- endif -%}
              {%- endif -%}
            {%- endif -%}
          {%- endif -%}
          {%- if shop.customer_accounts_enabled and section.settings.disable_online_store == false and section.settings.hide_account_button == false -%}
            <li class="{% unless section.settings.disable_account_dropdown %}sub{% endunless %} {% if customer %}no-sub{% else %}user-login menu-hide{% endif %}{% unless show_account %} desktop-hide tablet-hide{% endunless %}">
              {%- if customer -%}
                <a
                  href="{{ routes.account_url }}"
                  {% unless section.settings.disable_account_dropdown %}
                    class="toggle"
                  {% endunless %}
                >
                  {% if section.settings.header_icons %}
                    <span class="mobile-hide">{{ 'header.account.my_account' | t }}</span>
                    <i
                      aria-hidden="true"
                      aria-label="{{ 'header.account.my_account' | t }}"
                      class="icon-user mobile-only"
                    >
                      <span><i aria-hidden="true" class="icon-check"></i></span>
                    </i>
                    <span class="hidden">
                      {{- 'header.account.welcome_user_html' | t: name: customer.first_name -}}
                    </span>
                  {% else %}
                    <i
                      aria-hidden="true"
                      aria-label="{{ 'header.account.my_account' | t }}"
                      class="icon-user"
                    >
                      <span><i aria-hidden="true" class="icon-check"></i></span>
                    </i>
                    <span class="desktop-hide">
                      {{- 'header.account.welcome_user_html' | t: name: customer.first_name -}}
                    </span>
                  {% endif %}
                </a>
                {% unless section.settings.disable_account_dropdown %}
                  <ul class="base-font">
                    <li>
                      <a href="{{ routes.account_url }}"
                        ><i aria-hidden="true" class="icon-user"></i>
                        {{ 'header.account.overview' | t -}}
                      </a>
                    </li>
                    <li>
                      <a href="{{ routes.account_url }}"
                        ><i aria-hidden="true" class="icon-box"></i>
                        {{ 'header.account.orders' | t -}}
                      </a>
                    </li>
                    <li>
                      <a href="{{ routes.account_addresses_url }}"
                        ><i aria-hidden="true" class="icon-edit-off"></i>
                        {{ 'header.account.info' | t -}}
                      </a>
                    </li>
                    <li>
                      <a href="{{ routes.account_logout_url }}"
                        ><i aria-hidden="true" class="icon-logout"></i>
                        {{ 'header.account.logout' | t -}}
                      </a>
                    </li>
                  </ul>
                {% endunless %}
              {%- else -%}
                <a
                  href="{{ routes.account_login_url }}"
                  class="{% unless section.settings.disable_account_dropdown %}toggle{% endunless %} mobile-hide"
                  aria-label="{{ 'header.account.login' | t }}"
                >
                  {% if section.settings.header_icons %}
                    <span>{{ 'header.account.account' | t }}</span>
                  {% else %}
                    <i
                      aria-hidden="true"
                      aria-label="{{ 'header.account.login' | t }}"
                      class="icon-user"
                    ></i>
                    <span class="desktop-hide">{{ 'header.account.login' | t }}</span>
                  {% endif %}
                </a>
                <a
                  href="{{ routes.account_login_url }}"
                  class="mobile-only"
                  {% unless section.settings.disable_account_dropdown %}
                    data-panel="login"
                  {% endunless %}
                  aria-label="{{ 'header.account.login' | t }}"
                  ><i aria-hidden="true" class="icon-user"></i>
                  <span class="hidden">{{ 'header.account.login' | t }}</span></a
                >
                {% unless section.settings.disable_account_dropdown %}
                  {%- form 'customer_login', class: 'f8vl base-font' -%}
                    <fieldset>
                      <legend>{{ 'customer.login.title' | t }}</legend>
                      <p class="strong">{{ 'customer.login.title' | t }}</p>
                      <p>
                        <label for="login_email_address">
                          {{- 'customer.login.email' | t -}}
                          <span class="overlay-theme">*</span></label
                        >
                        <input
                          type="email"
                          id="login_email_address"
                          name="customer[email]"
                          placeholder="{{ 'customer.login.email' | t }}"
                          required
                        >
                      </p>
                      <p>
                        <label for="login_password">
                          {{- 'customer.login.password' | t -}}
                          <span class="overlay-theme">*</span>
                          <a href="./" class="show"><span>Toon</span> <span class="hidden">Hide</span></a></label
                        >
                        <input
                          type="password"
                          id="login_password"
                          name="customer[password]"
                          placeholder="{{ 'customer.login.password' | t }}"
                          required
                        >
                        <a href="{{ routes.account_login_url }}#recover" class="size-12 underline"
                          ><span class="link-underline">
                            {{- 'customer.login.forgot_password' | t -}}
                          </span></a
                        >
                      </p>
                      <p class="submit">
                        <button class="overlay-primary {% if settings.button_style == 'inv' %}inv{% endif %}">
                          {{ 'customer.login.submit' | t }}
                        </button>
                        {{ 'customer.register.title' | t -}}
                        <br>
                        <a href="{{ routes.account_register_url }}">
                          {{- 'customer.register.create_account_info' | t -}}
                        </a>
                      </p>
                    </fieldset>
                  {%- endform -%}
                {% endunless %}
              {%- endif -%}
            </li>
          {%- endif -%}
          <li class="wishlist-header hidden">
            <a href="#wishlist" aria-label="Wishlist"><i class="icon-heart-outline"></i></a>
          </li>
          {% unless section.settings.disable_online_store %}
            <li class="cart base-font">
              <a
                href="{{ routes.cart_url }}"
                {% if settings.enable_cart_drawer %}
                  data-panel="cart"
                {% endif %}
                aria-label="{{ 'cart.title' | t }} {{ cart.total_price | money }}"
              >
                {% if section.settings.header_icons %}
                  <span class="mobile-hide">
                    {{- 'cart.title' | t -}}
                    {%- if section.settings.show_cart_total -%}
                      <span id="cart-total" class="mobile-hide" style="margin-left: 5px;">
                        {{- cart.total_price | money -}}
                      </span>
                    {%- else -%}
                      {%- if cart.item_count > 0 -%}
                        <span style="margin-left: 5px;">({{ cart.item_count }})</span>
                      {%- endif -%}
                    {%- endif -%}
                  </span>
                  <i
                    aria-hidden="true"
                    class="icon-{{ section.settings.cart_icon | split: '|' | first }} mobile-only"
                    ><span id="cart-count" class="plain"> {{ cart.item_count }}</span></i
                  >
                {% else %}
                  <span class="hidden">{{ 'cart.title' | t }}</span
                  ><i
                    aria-hidden="true"
                    class="icon-{{ section.settings.cart_icon | split: '|' | first }}"
                    ><span id="cart-count" class="plain">{{ cart.item_count }}</span></i
                  >
                  {%- if section.settings.show_cart_total -%}
                    <span id="cart-total" class="mobile-hide s1pr-fw">
                      {{- cart.total_price | money -}}
                    </span>
                  {%- endif %}
                {% endif %}
              </a>
            </li>
          {% endunless %}
        </ul>
      </nav>
    </div>
  </div>
  {%- if has_menu_bar or button_menu -%}
    {%- if has_menu_bar and navbar_menu -%}
      {%- liquid
        assign has_images = false
        if section.settings.show_dropdown_images
          for link in linklists[navbar_menu].links
            if link.object.featured_image
              assign has_images = true
              break
            endif
          endfor
        endif
      -%}
      <nav
        id="nav-bar"
        aria-label="{{ 'general.accessibility.menu' | t }}"
        class="hr {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' %}nav-scroll-wrapper{% endif %} {% unless has_menu_bar %}hidden {% endunless %}text-{{ section.settings.menubar_alignment }}{% if section.settings.header_border_opacity != 0 %} plain{% if section.settings.header_border_width == 'boxed' and section.settings.header_shadow == false %} no-wide{% endif %}{% else %} no-bd{% endif %} tr_bd{% if section.settings.sticky == 'sticky-menu' and section.settings.enable_sticky_header %} sticky-menu{% endif %} ff-{{ section.settings.menu_font }} {{ section.settings.menu_layout }}{% if button_menu %} bm-a{% endif %}"
      >
        <ul
          data-type="horizontal-nav"
          {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' %}
            class="nav-scroll no-scroll"
          {% endif %}
        >
          {%- render 'header-menu',
            menu: navbar_menu,
            show_dropdown_images: section.settings.show_dropdown_images,
            show_dropdown_images_autofill: section.settings.show_dropdown_images_autofill,
            has_images: has_images,
            show_dropdown_images_rounded: section.settings.show_dropdown_images_rounded,
            show_dropdown_images_subs: section.settings.show_dropdown_images_subs,
            layout: menu_layout,
            main_menu_items_clickable: section.settings.main_menu_items_clickable,
            menubar_caps: section.settings.menubar_caps,
            sale_highlight: section.settings.sale_highlight,
            sale_highlight_item: section.settings.sale_highlight_item,
            manual_collapse: false,
            submenu_items_limit: section.settings.submenu_items_limit,
            custom_menu_items_per_columns: section.settings.custom_menu_items_per_columns,
            menu_items_limit: section.settings.menu_items_limit,
            blocks: section.blocks,
            header_width: section.settings.width,
            main_menu_show_lines: section.settings.main_menu_show_lines,
            menu_subs_font: section.settings.menu_subs_font,
            show_chevrons: section.settings.menu_show_chevrons
          -%}
        </ul>
      </nav>
    {%- endif -%}
    {%- liquid
      assign is_button_menu = false
      if nav_menu == button_menu
        assign is_button_menu = true
        assign menu_layout = false
      endif
    -%}
    <nav
      id="nav"
      aria-label="{{ 'general.accessibility.menu' | t }}"
      class="hr {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' and button_menu == false %}nav-scroll-wrapper{% endif %} {% unless has_menu_bar %}desktop-hide {% endunless %}text-{{ section.settings.menubar_alignment }}{% if section.settings.header_border_opacity != 0 %} plain{% if section.settings.header_border_width == 'boxed' and section.settings.header_shadow == false %} no-wide{% endif %}{% else %} no-bd{% endif %} tr_bd{% if section.settings.sticky == 'sticky-menu' and section.settings.enable_sticky_header %} sticky-menu{% endif %} ff-{{ section.settings.menu_font }} {{ section.settings.menu_layout }}"
    >
      <ul
        data-type="main-nav"
        {% if section.settings.menu_scroll and section.settings.menubar_alignment == 'left' and button_menu == false %}
          class="nav-scroll"
        {% endif %}
      >
        {%- render 'header-menu',
          menu: nav_menu,
          show_dropdown_images: section.settings.show_dropdown_images,
          show_dropdown_images_autofill: section.settings.show_dropdown_images_autofill,
          show_dropdown_images_rounded: section.settings.show_dropdown_images_rounded,
          show_dropdown_images_subs: section.settings.show_dropdown_images_subs,
          layout: menu_layout,
          main_menu_items_clickable: section.settings.main_menu_items_clickable,
          menubar_caps: section.settings.menubar_caps,
          sale_highlight: section.settings.sale_highlight,
          sale_highlight_item: section.settings.sale_highlight_item,
          manual_collapse: false,
          submenu_items_limit: section.settings.submenu_items_limit,
          custom_menu_items_per_columns: section.settings.custom_menu_items_per_columns,
          menu_items_limit: section.settings.menu_items_limit,
          blocks: section.blocks,
          header_width: section.settings.width,
          main_menu_show_lines: section.settings.main_menu_show_lines,
          menu_subs_font: section.settings.menu_subs_font,
          show_chevrons: section.settings.menu_show_chevrons
        -%}
      </ul>
      {% unless section.settings.show_top_bar %}
        <ul data-type="top-nav" class="nav-top">
          {% if section.settings.navbar_menu_item_link != empty %}
            <li>
              <a href="{{ section.settings.navbar_menu_item_link }}">
                {{- section.settings.navbar_menu_item_text -}}
              </a>
            </li>
          {% endif %}
          {% if settings.show_accessibility and settings.enable_accessibility_default == false %}
            <li>
              <a
                href="#"
                class="link-accessible"
                aria-label="{{ 'general.accessibility.toggle_accessibility_mode' | t }}"
                ><i aria-hidden="true" class="icon-text-size"></i>
                <span class="hidden">
                  {{- 'general.accessibility.toggle_accessibility_mode' | t -}}
                </span></a
              >
            </li>
          {% endif %}
          {%- liquid
            if localization.available_countries.size > 1
              render 'language-country-selector', display: 'country', extra_classes: 'mobile-nav-only', hide: hide_country_selector
            endif
            if localization.available_languages.size > 1
              render 'language-country-selector', display: 'language', extra_classes: 'mobile-nav-only', hide: hide_language_selector
            endif
          -%}
        </ul>
      {% endunless %}
    </nav>
  {%- else -%}
    <div
      id="nav"
      aria-label="{{ 'general.accessibility.menu' | t }}"
      class="hidden ff-{{ section.settings.menu_font }}"
    >
      <ul data-type="horizontal-nav"></ul>
    </div>
  {% endif %}
</div>

{% schema %}
{
  "name": "Header",
  "class": "shopify-section-header",
  "settings": [
    {
      "type": "select",
      "id": "layout",
      "label": "t:static_sections.header.settings.layout.label",
      "info": "t:static_sections.header.settings.layout.info",
      "options": [
        {
          "value": "layout_1",
          "label": "t:static_sections.header.settings.layout.options__1.label",
          "group": "t:static_sections.header.settings.layout.options__1.group"
        },
        {
          "value": "layout_2",
          "label": "t:static_sections.header.settings.layout.options__2.label",
          "group": "t:static_sections.header.settings.layout.options__2.group"
        },
        {
          "value": "layout_3",
          "label": "t:static_sections.header.settings.layout.options__3.label",
          "group": "t:static_sections.header.settings.layout.options__3.group"
        },
        {
          "value": "layout_4",
          "label": "t:static_sections.header.settings.layout.options__4.label",
          "group": "t:static_sections.header.settings.layout.options__4.group"
        },
        {
          "value": "layout_5",
          "label": "t:static_sections.header.settings.layout.options__5.label",
          "group": "t:static_sections.header.settings.layout.options__5.group"
        },
        {
          "value": "layout_6",
          "label": "t:static_sections.header.settings.layout.options__6.label",
          "group": "t:static_sections.header.settings.layout.options__6.group"
        },
        {
          "value": "layout_7",
          "label": "t:static_sections.header.settings.layout.options__7.label",
          "group": "t:static_sections.header.settings.layout.options__7.group"
        },
        {
          "value": "layout_8",
          "label": "t:static_sections.header.settings.layout.options__8.label",
          "group": "t:static_sections.header.settings.layout.options__8.group"
        }
      ],
      "default": "layout_3"
    },
    {
      "type": "range",
      "id": "width",
      "label": "t:static_sections.header.settings.width.label",
      "info": "t:static_sections.header.settings.width.info",
      "min": 1000,
      "max": 2000,
      "step": 20,
      "unit": "px",
      "default": 1280
    },
    {
      "id": "header_height",
      "type": "select",
      "label": "t:static_sections.header.settings.header_height.label",
      "options": [
        {
          "label": "t:static_sections.header.settings.header_height.options__1.label",
          "value": "69px|55px"
        },
        {
          "label": "t:static_sections.header.settings.header_height.options__2.label",
          "value": "89px|70px"
        },
        {
          "label": "t:static_sections.header.settings.header_height.options__3.label",
          "value": "109px|90px"
        }
      ],
      "default": "69px|55px"
    },
    {
      "type": "select",
      "id": "show_header_border",
      "label": "t:static_sections.header.settings.show_header_border.label",
      "options": [
        {
          "value": "top",
          "label": "t:static_sections.header.settings.show_header_border.options__1.label"
        },
        {
          "value": "bottom",
          "label": "t:static_sections.header.settings.show_header_border.options__2.label"
        },
        {
          "value": "both",
          "label": "t:static_sections.header.settings.show_header_border.options__3.label"
        }
      ],
      "default": "both"
    },
    {
      "id": "header_border_opacity",
      "type": "range",
      "label": "t:static_sections.header.settings.header_border_opacity.label",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "default": 100
    },
    {
      "id": "header_border_width",
      "type": "select",
      "label": "t:static_sections.header.settings.header_border_width.label",
      "options": [
        {
          "value": "boxed",
          "label": "t:global.layout.width.options__1.label"
        },
        {
          "value": "wide",
          "label": "t:global.layout.width.options__2.label"
        }
      ],
      "default": "wide"
    },
    {
      "id": "header_shadow",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.header_shadow.label"
    },
    {
      "id": "header_shadow_opacity",
      "type": "range",
      "label": "t:static_sections.header.settings.header_shadow_opacity.label",
      "min": 1,
      "max": 50,
      "step": 1,
      "unit": "%",
      "default": 10,
      "visible_if": "{{ section.settings.header_shadow }}"
    },
    {
      "id": "enable_country_selector",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.enable_country_selector.label",
      "default": false
    },
    {
      "id": "enable_language_selector",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.enable_language_selector.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.colors.header"
    },
    {
      "type": "color_scheme",
      "id": "top_bar_color",
      "label": "t:static_sections.header.settings.colors.top_bar_color.label",
      "default": "scheme-4"
    },
    {
      "type": "color_scheme",
      "id": "header_color",
      "label": "t:static_sections.header.settings.colors.header_color.label",
      "default": "scheme-1"
    },
    {
      "type": "color_scheme",
      "id": "menubar_color",
      "label": "t:static_sections.header.settings.colors.menubar_color.label",
      "default": "scheme-3"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.top_bar.header"
    },
    {
      "id": "show_top_bar",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.top_bar.show_top_bar.label",
      "default": true
    },
    {
      "type": "select",
      "id": "top_bar_height",
      "label": "t:static_sections.header.settings.top_bar.top_bar_height.label",
      "options": [
        {
          "label": "t:static_sections.header.settings.top_bar.top_bar_height.options__1.label",
          "value": "35px|25px"
        },
        {
          "label": "t:static_sections.header.settings.top_bar.top_bar_height.options__2.label",
          "value": "45px|35px"
        },
        {
          "label": "t:static_sections.header.settings.top_bar.top_bar_height.options__3.label",
          "value": "55px|45px"
        },
        {
          "label": "t:static_sections.header.settings.top_bar.top_bar_height.options__4.label",
          "value": "65px|55px"
        }
      ],
      "default": "35px|25px",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "id": "top_bar_font",
      "type": "select",
      "label": "t:static_sections.header.settings.top_bar.top_bar_font.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.typography.title_font.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.typography.title_font.secondary.label"
        }
      ],
      "default": "secondary",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "type": "select",
      "id": "top_bar_font_size",
      "label": "t:static_sections.header.settings.top_bar.top_bar_font_size.label",
      "options": [
        {
          "value": "13px",
          "label": "t:static_sections.header.settings.top_bar.top_bar_font_size.options__1.label"
        },
        {
          "value": "14px",
          "label": "t:static_sections.header.settings.top_bar.top_bar_font_size.options__2.label"
        },
        {
          "value": "16px",
          "label": "t:static_sections.header.settings.top_bar.top_bar_font_size.options__3.label"
        },
        {
          "value": "18px",
          "label": "t:static_sections.header.settings.top_bar.top_bar_font_size.options__4.label"
        }
      ],
      "default": "14px",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "id": "navbar_menu_item_text",
      "type": "text",
      "label": "t:static_sections.header.settings.top_bar.navbar_menu_item_text.label",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "id": "navbar_menu_item_icon",
      "type": "select",
      "label": "t:global.icon.label",
      "info": "t:global.icon.info",
      "options": [
        {
          "value": "none",
          "label": "t:global.icon.options__1.label"
        },
        {
          "value": "group",
          "label": "t:global.icon.options__2.label"
        },
        {
          "value": "notification",
          "label": "t:global.icon.options__3.label"
        },
        {
          "value": "cloud_data",
          "label": "t:global.icon.options__4.label"
        },
        {
          "value": "verified",
          "label": "t:global.icon.options__5.label"
        },
        {
          "value": "truck",
          "label": "t:global.icon.options__6.label"
        },
        {
          "value": "image_placeholder",
          "label": "t:global.icon.options__7.label"
        },
        {
          "value": "help_call",
          "label": "t:global.icon.options__8.label"
        },
        {
          "value": "filters",
          "label": "t:global.icon.options__9.label"
        },
        {
          "value": "shopping_bag",
          "label": "t:global.icon.options__10.label"
        },
        {
          "value": "global_shipping",
          "label": "t:global.icon.options__11.label"
        },
        {
          "value": "barcode",
          "label": "t:global.icon.options__12.label"
        },
        {
          "value": "delivery_box_1",
          "label": "t:global.icon.options__13.label"
        },
        {
          "value": "delivery_box_2",
          "label": "t:global.icon.options__14.label"
        },
        {
          "value": "statistic",
          "label": "t:global.icon.options__15.label"
        },
        {
          "value": "review",
          "label": "t:global.icon.options__16.label"
        },
        {
          "value": "email",
          "label": "t:global.icon.options__17.label"
        },
        {
          "value": "coin",
          "label": "t:global.icon.options__18.label"
        },
        {
          "value": "24_hour_clock",
          "label": "t:global.icon.options__19.label"
        },
        {
          "value": "question",
          "label": "t:global.icon.options__20.label"
        },
        {
          "value": "24_7_call",
          "label": "t:global.icon.options__21.label"
        },
        {
          "value": "speech_bubbles",
          "label": "t:global.icon.options__22.label"
        },
        {
          "value": "coupon",
          "label": "t:global.icon.options__23.label"
        },
        {
          "value": "mobile_payment",
          "label": "t:global.icon.options__24.label"
        },
        {
          "value": "calculator",
          "label": "t:global.icon.options__25.label"
        },
        {
          "value": "secure",
          "label": "t:global.icon.options__26.label"
        }
      ],
      "default": "none",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "id": "navbar_menu_item_link",
      "type": "url",
      "label": "t:static_sections.header.settings.top_bar.navbar_menu_item_link.label",
      "visible_if": "{{ section.settings.show_top_bar }}"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.information.header"
    },
    {
      "type": "paragraph",
      "content": "t:static_sections.header.settings.information.paragraph"
    },
    {
      "id": "enable_usp_slider",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.information.enable_usp_slider.label",
      "default": true
    },
    {
      "id": "enable_usp_slider_arrows",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.information.enable_usp_slider_arrows.label",
      "default": true,
      "visible_if": "{{ section.settings.enable_usp_slider }}"
    },
    {
      "id": "enable_usp_slider_autoplay",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.information.enable_usp_slider_autoplay.label",
      "default": true,
      "visible_if": "{{ section.settings.enable_usp_slider }}"
    },
    {
      "id": "usp_slider_autoplay_seconds",
      "type": "range",
      "label": "t:static_sections.header.settings.information.usp_slider_autoplay_seconds.label",
      "min": 1,
      "max": 10,
      "step": 1,
      "unit": "s",
      "default": 3,
      "visible_if": "{{ section.settings.enable_usp_slider and section.settings.enable_usp_slider_autoplay }}"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.searchbar.header"
    },
    {
      "type": "select",
      "id": "searchbar_font_size",
      "label": "t:static_sections.header.settings.searchbar.searchbar_font_size.label",
      "options": [
        {
          "value": "13px",
          "label": "t:static_sections.header.settings.searchbar.searchbar_font_size.options__1.label"
        },
        {
          "value": "14px",
          "label": "t:static_sections.header.settings.searchbar.searchbar_font_size.options__2.label"
        },
        {
          "value": "16px",
          "label": "t:static_sections.header.settings.searchbar.searchbar_font_size.options__3.label"
        },
        {
          "value": "18px",
          "label": "t:static_sections.header.settings.searchbar.searchbar_font_size.options__4.label"
        }
      ],
      "default": "14px"
    },
    {
      "type": "range",
      "id": "searchbar_height",
      "label": "t:static_sections.header.settings.searchbar.searchbar_height.label",
      "min": 35,
      "max": 85,
      "step": 1,
      "unit": "px",
      "default": 50
    },
    {
      "type": "range",
      "id": "searchbar_width",
      "label": "t:static_sections.header.settings.searchbar.searchbar_width.label",
      "info": "t:static_sections.header.settings.searchbar.searchbar_width.info",
      "min": 200,
      "max": 600,
      "step": 5,
      "unit": "px",
      "default": 390
    },
    {
      "id": "show_searchbar_border",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.searchbar.show_searchbar_border.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.sticky_header.header"
    },
    {
      "id": "enable_sticky_header",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.sticky_header.enable_sticky_header.label",
      "default": true
    },
    {
      "id": "sticky",
      "type": "select",
      "label": "t:static_sections.header.settings.sticky_header.sticky.label",
      "info": "t:static_sections.header.settings.sticky_header.sticky.info",
      "options": [
        {
          "label": "t:static_sections.header.settings.sticky_header.sticky.options__1.label",
          "value": "sticky-menu"
        },
        {
          "label": "t:static_sections.header.settings.sticky_header.sticky.options__2.label",
          "value": "sticky-header"
        }
      ],
      "default": "sticky-header",
      "visible_if": "{{ section.settings.enable_sticky_header }}"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.cart_icon.header"
    },
    {
      "type": "select",
      "id": "cart_icon",
      "label": "t:static_sections.header.settings.cart_icon.cart_icon.label",
      "options": [
        {
          "value": "basket|e962",
          "label": "t:static_sections.header.settings.cart_icon.cart_icon.options__1.label"
        },
        {
          "value": "cart-bag-wide|e965",
          "label": "t:static_sections.header.settings.cart_icon.cart_icon.options__2.label"
        },
        {
          "value": "cart|e903",
          "label": "t:static_sections.header.settings.cart_icon.cart_icon.options__3.label"
        }
      ],
      "default": "cart|e903"
    },
    {
      "type": "select",
      "id": "cart_icon_color",
      "label": "t:static_sections.header.settings.cart_icon.cart_icon_color.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.button.button_style.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.button.button_style.secondary.label"
        },
        {
          "value": "tertiary",
          "label": "t:global.button.button_style.tertiary.label"
        },
        {
          "value": "positive",
          "label": "t:global.button.button_style.positive.label"
        },
        {
          "value": "buy_button",
          "label": "t:global.button.button_style.buy_button.label"
        }
      ],
      "default": "primary"
    },
    {
      "id": "show_cart_total",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.cart_icon.show_cart_total.label"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.navigation_menu.header"
    },
    {
      "id": "menubar_menu",
      "type": "link_list",
      "label": "t:static_sections.header.settings.navigation_menu.menubar_menu.label",
      "default": "main-menu"
    },
    {
      "id": "menubar_height",
      "type": "select",
      "label": "t:static_sections.header.settings.navigation_menu.menubar_height.label",
      "options": [
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_height.options__1.label",
          "value": "45px"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_height.options__2.label",
          "value": "55px"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_height.options__3.label",
          "value": "65px"
        }
      ],
      "default": "45px"
    },
    {
      "id": "menu_layout",
      "type": "select",
      "label": "t:static_sections.header.settings.navigation_menu.menu_layout.label",
      "options": [
        {
          "label": "t:static_sections.header.settings.navigation_menu.menu_layout.options__1.label",
          "value": "mega"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menu_layout.options__2.label",
          "value": "dropdown"
        }
      ],
      "default": "mega"
    },
    {
      "id": "menu_scroll",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.menu_scroll.label",
      "info": "t:static_sections.header.settings.navigation_menu.menu_scroll.info",
      "default": false
    },
    {
      "id": "menu_compact",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.menu_compact.label",
      "default": false,
      "visible_if": "{{ section.settings.menu_layout == 'mega' }}"
    },
    {
      "id": "show_dropdown_images",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.show_dropdown_images.label",
      "info": "t:static_sections.header.settings.navigation_menu.show_dropdown_images.info",
      "default": true
    },
    {
      "id": "show_dropdown_images_subs",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.show_dropdown_images_subs.label",
      "default": true,
      "visible_if": "{{ section.settings.show_dropdown_images }}"
    },
    {
      "id": "show_dropdown_images_autofill",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.show_dropdown_images_autofill.label",
      "info": "t:static_sections.header.settings.navigation_menu.show_dropdown_images_autofill.info",
      "visible_if": "{{ section.settings.show_dropdown_images }}"
    },
    {
      "type": "checkbox",
      "id": "show_dropdown_images_rounded",
      "label": "t:static_sections.header.settings.navigation_menu.show_dropdown_images_rounded.label",
      "visible_if": "{{ section.settings.show_dropdown_images }}"
    },
    {
      "id": "menubar_alignment",
      "type": "select",
      "label": "t:static_sections.header.settings.navigation_menu.menubar_alignment.label",
      "options": [
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_alignment.options__1.label",
          "value": "left"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_alignment.options__2.label",
          "value": "center"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_alignment.options__3.label",
          "value": "end"
        },
        {
          "label": "t:static_sections.header.settings.navigation_menu.menubar_alignment.options__4.label",
          "value": "justify"
        }
      ],
      "default": "left"
    },
    {
      "id": "main_menu_items_clickable",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.main_menu_items_clickable.label",
      "default": true
    },
    {
      "id": "menu_show_chevrons",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.menu_show_chevrons.label"
    },
    {
      "id": "sale_highlight",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.navigation_menu.sale_highlight.label",
      "default": true
    },
    {
      "id": "sale_highlight_item",
      "type": "text",
      "label": "t:static_sections.header.settings.navigation_menu.sale_highlight_item.label",
      "visible_if": "{{ section.settings.sale_highlight }}",
      "default": "Sale"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.menu_typography.header"
    },
    {
      "id": "menu_font",
      "type": "select",
      "label": "t:static_sections.header.settings.menu_typography.menu_font.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.typography.title_font.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.typography.title_font.secondary.label"
        }
      ],
      "default": "secondary"
    },
    {
      "id": "menu_font_weight",
      "type": "select",
      "label": "t:static_sections.header.settings.menu_typography.menu_font_weight.label",
      "options": [
        {
          "value": "100",
          "label": "t:global.typography.font_weight.100.label"
        },
        {
          "value": "200",
          "label": "t:global.typography.font_weight.200.label"
        },
        {
          "value": "300",
          "label": "t:global.typography.font_weight.300.label"
        },
        {
          "value": "400",
          "label": "t:global.typography.font_weight.400.label"
        },
        {
          "value": "500",
          "label": "t:global.typography.font_weight.500.label"
        },
        {
          "value": "600",
          "label": "t:global.typography.font_weight.600.label"
        },
        {
          "value": "700",
          "label": "t:global.typography.font_weight.700.label"
        },
        {
          "value": "800",
          "label": "t:global.typography.font_weight.800.label"
        },
        {
          "value": "900",
          "label": "t:global.typography.font_weight.900.label"
        }
      ],
      "default": "400"
    },
    {
      "id": "menu_font_size",
      "type": "range",
      "label": "t:static_sections.header.settings.menu_typography.menu_font_size.label",
      "unit": "px",
      "min": 10,
      "max": 20,
      "step": 1,
      "default": 14
    },
    {
      "type": "range",
      "id": "menu_letter_spacing",
      "label": "t:static_sections.header.settings.menu_typography.menu_letter_spacing.label",
      "min": -25,
      "max": 25,
      "step": 1,
      "default": 0
    },
    {
      "id": "menubar_caps",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.menu_typography.menubar_caps.label",
      "default": false
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.menu_subs_typography.header"
    },
    {
      "type": "select",
      "id": "menu_subs_font",
      "label": "t:static_sections.header.settings.menu_typography.menu_font.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.typography.title_font.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.typography.title_font.secondary.label"
        }
      ],
      "default": "secondary"
    },
    {
      "id": "menu_subs_font_weight",
      "type": "select",
      "label": "t:static_sections.header.settings.menu_typography.menu_font_weight.label",
      "options": [
        {
          "value": "100",
          "label": "t:global.typography.font_weight.100.label"
        },
        {
          "value": "200",
          "label": "t:global.typography.font_weight.200.label"
        },
        {
          "value": "300",
          "label": "t:global.typography.font_weight.300.label"
        },
        {
          "value": "400",
          "label": "t:global.typography.font_weight.400.label"
        },
        {
          "value": "500",
          "label": "t:global.typography.font_weight.500.label"
        },
        {
          "value": "600",
          "label": "t:global.typography.font_weight.600.label"
        },
        {
          "value": "700",
          "label": "t:global.typography.font_weight.700.label"
        },
        {
          "value": "800",
          "label": "t:global.typography.font_weight.800.label"
        },
        {
          "value": "900",
          "label": "t:global.typography.font_weight.900.label"
        }
      ],
      "default": "400"
    },
    {
      "id": "enable_menu_subs_font_size",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.menu_subs_typography.enable_menu_subs_font_size.label"
    },
    {
      "id": "menu_subs_font_size",
      "type": "range",
      "label": "t:static_sections.header.settings.menu_typography.menu_font_size.label",
      "unit": "px",
      "min": 10,
      "max": 20,
      "step": 1,
      "default": 14,
      "visible_if": "{{ section.settings.enable_menu_subs_font_size }}"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.megamenu.header"
    },
    {
      "id": "custom_menu_items_per_columns",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.megamenu.custom_menu_items_per_columns.label",
      "info": "t:static_sections.header.settings.megamenu.custom_menu_items_per_columns.info"
    },
    {
      "id": "menu_items_limit",
      "type": "range",
      "label": "t:static_sections.header.settings.megamenu.menu_items_limit.label",
      "info": "t:static_sections.header.settings.megamenu.menu_items_limit.info",
      "min": 1,
      "max": 15,
      "step": 1,
      "default": 5,
      "visible_if": "{{ section.settings.custom_menu_items_per_columns }}"
    },
    {
      "id": "submenu_items_limit",
      "type": "range",
      "label": "t:static_sections.header.settings.megamenu.submenu_items_limit.label",
      "info": "t:static_sections.header.settings.megamenu.submenu_items_limit.info",
      "min": 1,
      "max": 15,
      "step": 1,
      "default": 5
    },
    {
      "id": "main_menu_show_lines",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.megamenu.main_menu_show_lines.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.socials.header"
    },
    {
      "id": "enable_socials",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.socials.enable_socials.label",
      "info": "t:static_sections.header.settings.socials.enable_socials.info",
      "default": true
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.trustmark.header"
    },
    {
      "id": "extra_image",
      "type": "image_picker",
      "label": "t:static_sections.header.settings.trustmark.extra_image.label",
      "info": "t:static_sections.header.settings.trustmark.extra_image.info"
    },
    {
      "id": "extra_image_width",
      "type": "range",
      "label": "t:static_sections.header.settings.trustmark.extra_image_width.label",
      "min": 50,
      "max": 300,
      "step": 5,
      "unit": "px",
      "default": 130
    },
    {
      "id": "extra_image_link",
      "type": "url",
      "label": "t:static_sections.header.settings.trustmark.extra_image_link.label"
    },
    {
      "type": "header",
      "content": "t:global.button.header"
    },
    {
      "id": "enable_button",
      "type": "checkbox",
      "label": "t:global.button.show_link.label",
      "info": "t:static_sections.header.settings.button.enable_button.info"
    },
    {
      "id": "button_text",
      "type": "text",
      "label": "t:global.button.link_text.label",
      "default": "Dealer login",
      "visible_if": "{{ section.settings.enable_button }}"
    },
    {
      "id": "button_url",
      "type": "url",
      "label": "t:static_sections.header.settings.button.button_url.label",
      "info": "t:static_sections.header.settings.button.button_url.info",
      "visible_if": "{{ section.settings.enable_button }}"
    },
    {
      "type": "select",
      "id": "button_style",
      "label": "t:global.button.button_style.label",
      "options": [
        {
          "value": "primary plain",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "secondary plain",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "tertiary plain",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "positive plain",
          "label": "t:global.button.button_style.positive.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "buy_button plain",
          "label": "t:global.button.button_style.buy_button.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "dynamic_buy_button plain",
          "label": "t:global.button.button_style.dynamic_buy_button.label",
          "group": "t:global.button.button_style.group.plain"
        },
        {
          "value": "primary inv",
          "label": "t:global.button.button_style.primary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "secondary inv",
          "label": "t:global.button.button_style.secondary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "tertiary inv",
          "label": "t:global.button.button_style.tertiary.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "positive inv",
          "label": "t:global.button.button_style.positive.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "buy_button inv",
          "label": "t:global.button.button_style.buy_button.label",
          "group": "t:global.button.button_style.group.inv"
        },
        {
          "value": "dynamic_buy_button inv",
          "label": "t:global.button.button_style.dynamic_buy_button.label",
          "group": "t:global.button.button_style.group.inv"
        }
      ],
      "default": "primary plain",
      "visible_if": "{{ section.settings.enable_button }}"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.online_store.header"
    },
    {
      "id": "disable_online_store",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.online_store.disable_online_store.label",
      "default": false
    },
    {
      "id": "disable_account_dropdown",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.online_store.disable_account_dropdown.label",
      "default": false
    },
    {
      "id": "header_icons",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.header_icons.label",
      "info": "t:static_sections.header.settings.header_icons.info"
    },
    {
      "type": "header",
      "content": "t:static_sections.header.settings.mobile.header"
    },
    {
      "type": "select",
      "id": "layout_mobile",
      "label": "t:static_sections.header.settings.mobile.layout_mobile.label",
      "options": [
        {
          "value": "logo_left",
          "label": "t:static_sections.header.settings.mobile.layout_mobile.options__1.label"
        },
        {
          "value": "logo_center",
          "label": "t:static_sections.header.settings.mobile.layout_mobile.options__2.label"
        },
        {
          "value": "logo_right",
          "label": "t:static_sections.header.settings.mobile.layout_mobile.options__3.label"
        }
      ],
      "default": "logo_center"
    },
    {
      "type": "color_scheme",
      "id": "mobile_menu_color",
      "label": "t:static_sections.header.settings.mobile.mobile_menu_color.label",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "menu_icon",
      "label": "t:static_sections.header.settings.mobile.menu_icon.label",
      "options": [
        {
          "value": "menu|e922",
          "label": "t:static_sections.header.settings.mobile.menu_icon.options__1.label"
        },
        {
          "value": "menu-left-wide|e99a",
          "label": "t:static_sections.header.settings.mobile.menu_icon.options__2.label"
        },
        {
          "value": "menu-left|e97f",
          "label": "t:static_sections.header.settings.mobile.menu_icon.options__3.label"
        }
      ],
      "default": "menu-left|e97f"
    },
    {
      "id": "search_compact_mobile",
      "type": "checkbox",
      "label": "t:static_sections.header.settings.mobile.search_compact_mobile.label"
    },
    {
      "type": "select",
      "id": "search_mobile_bd",
      "label": "t:static_sections.header.settings.mobile.search_mobile_bd.label",
      "options": [
        {
          "value": "no-bd",
          "label": "t:static_sections.header.settings.mobile.search_mobile_bd.options__1.label"
        },
        {
          "value": "bd-t",
          "label": "t:static_sections.header.settings.mobile.search_mobile_bd.options__2.label"
        },
        {
          "value": "bd-b",
          "label": "t:static_sections.header.settings.mobile.search_mobile_bd.options__3.label"
        },
        {
          "value": "bd-t bd-b",
          "label": "t:static_sections.header.settings.mobile.search_mobile_bd.options__4.label"
        }
      ],
      "default": "bd-b",
      "visible_if": "{{ section.settings.search_compact_mobile == false }}"
    },
    {
      "type": "range",
      "id": "searchbar_height_mobile",
      "label": "t:static_sections.header.settings.mobile.searchbar_height_mobile.label",
      "min": 35,
      "max": 85,
      "step": 1,
      "unit": "px",
      "default": 45
    },
    {
      "id": "menu_font_size_mobile",
      "type": "range",
      "label": "t:static_sections.header.settings.menu_typography.menu_font_size.label",
      "unit": "px",
      "min": 10,
      "max": 20,
      "step": 1,
      "default": 14
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_desktop.label",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 25
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "info": "t:static_sections.header.settings.mobile.spacing_mobile.info",
      "min": 0,
      "max": 50,
      "step": 1,
      "unit": "px",
      "default": 20
    },
    {
      "type": "header",
      "content": "Header Customization"
    },
    {
      "id": "hide_search",
      "type": "checkbox",
      "label": "Hide search bar",
      "info": "Hide the search bar from the header",
      "default": false
    },
    {
      "id": "show_default_logo",
      "type": "checkbox",
      "label": "Show default logo when no logo is uploaded",
      "info": "Display a default SVG logo when no custom logo is set",
      "default": true
    },
    {
      "id": "default_logo_svg",
      "type": "textarea",
      "label": "Default logo SVG code",
      "info": "SVG code for the default logo",
      "default": "<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"96\" height=\"44\" viewBox=\"0 0 96 44\" fill=\"none\"><path d=\"M79.2911 39.9346H67.7209C67.1593 39.9346 66.7119 40.359 66.7119 40.8917C66.7119 41.4605 67.1593 41.8849 67.7209 41.8849H79.2911C79.8527 41.8849 80.3001 41.4605 80.3001 40.8917C80.3001 40.359 79.8574 39.9346 79.2911 39.9346Z\" fill=\"#00334C\"/><path d=\"M29.3867 21.7598H23.3585C22.7926 21.7598 22.3418 22.2145 22.3418 22.8241C22.3418 23.395 22.7926 23.8497 23.3585 23.8497H29.3867C31.2331 23.8497 32.8924 25.3301 32.8924 27.4201C32.8924 29.3214 31.4249 30.9518 29.3867 30.9518H23.3585C22.7926 30.9518 22.3418 31.4065 22.3418 32.0161V41.1742C22.3418 41.7789 22.7926 42.2385 23.3585 42.2385C23.9628 42.2385 24.4136 41.7838 24.4136 41.1742V33.0466H29.3484C32.926 33.0466 34.9594 30.1196 34.9594 27.4249C34.9642 24.3045 32.4368 21.7598 29.3867 21.7598Z\" fill=\"#00334C\"/><path d=\"M43.6973 30.9598H42.7481V30.9646H42.2687C40.2361 30.9646 38.7643 29.329 38.7643 27.4273C38.7643 25.3368 40.423 23.8561 42.2687 23.8561H48.2946C48.8603 23.8561 49.3109 23.4012 49.3109 22.8302C49.3109 22.2253 48.8555 21.7656 48.2946 21.7656H42.2687C39.2198 21.7656 36.6934 24.311 36.6934 27.4273C36.6934 30.1275 38.726 33.0502 42.3022 33.0502H43.75C45.773 33.0599 47.2304 34.6907 47.2304 36.5827C47.2304 38.6732 45.5717 40.1539 43.726 40.1539H37.7097C37.144 40.1539 36.6934 40.6088 36.6934 41.1798C36.6934 41.7847 37.144 42.2444 37.7097 42.2444H43.7356C46.7845 42.2444 49.3109 39.6991 49.3109 36.5827C49.3109 33.8874 47.2735 30.9598 43.6973 30.9598Z\" fill=\"#00334C\"/><path d=\"M89.7441 21.7598H83.7159C83.15 21.7598 82.6992 22.2145 82.6992 22.8241C82.6992 23.395 83.15 23.8497 83.7159 23.8497H89.7441C91.5905 23.8497 93.2498 25.3301 93.2498 27.4201C93.2498 29.3214 91.7823 30.9518 89.7441 30.9518H83.7159C83.15 30.9518 82.6992 31.4065 82.6992 32.0161V41.1742C82.6992 41.7789 83.15 42.2385 83.7159 42.2385C84.3202 42.2385 84.771 41.7838 84.771 41.1742V33.0466H89.7058C93.2834 33.0466 95.3168 30.1196 95.3168 27.4249C95.3216 24.3045 92.7942 21.7598 89.7441 21.7598Z\" fill=\"#00334C\"/><path d=\"M56.7676 41.2685C56.7676 41.8413 57.1901 42.2975 57.7564 42.2975C58.2867 42.2975 58.7092 41.8413 58.7092 41.2685V22.7939H56.7676V41.2685Z\" fill=\"#00334C\"/><path d=\"M63.6354 21.7598H58.3649H57.3365H52.066C51.5042 21.7598 51.0566 22.1842 51.0566 22.7169C51.0566 23.2857 51.5042 23.7101 52.066 23.7101H57.3365H58.3649H63.6354C64.1972 23.7101 64.6448 23.2857 64.6448 22.7169C64.6448 22.1842 64.1972 21.7598 63.6354 21.7598Z\" fill=\"#00334C\"/><path d=\"M79.2911 21.7598H67.7209C67.1593 21.7598 66.7119 22.1842 66.7119 22.7169C66.7119 23.2857 67.1593 23.7101 67.7209 23.7101H79.2911C79.8527 23.7101 80.3001 23.2857 80.3001 22.7169C80.3001 22.1842 79.8574 21.7598 79.2911 21.7598Z\" fill=\"#00334C\"/><path d=\"M79.2911 30.8477H67.7209C67.1593 30.8477 66.7119 31.272 66.7119 31.8048C66.7119 32.3736 67.1593 32.798 67.7209 32.798H79.2911C79.8527 32.798 80.3001 32.3736 80.3001 31.8048C80.3001 31.272 79.8574 30.8477 79.2911 30.8477Z\" fill=\"#00334C\"/><path d=\"M12.3531 44.0006C10.0283 44.0006 7.55678 42.8657 5.92329 40.0188C4.31821 37.2204 4.31347 34.0825 4.31347 31.0464C4.31347 30.6002 4.31347 30.1492 4.30874 29.7078C4.28033 27.4914 3.89208 25.3914 3.04929 22.8937C2.86937 22.365 3.14399 21.783 3.66481 21.6036C4.18563 21.4193 4.74907 21.7006 4.92899 22.2341C5.8428 24.9452 6.26419 27.2441 6.2926 29.6836C6.29734 30.1346 6.29734 30.5905 6.29734 31.0464C6.29734 33.8109 6.29734 36.6675 7.63254 38.9857C9.31812 41.92 12.1637 42.4195 14.2423 41.6096C16.103 40.8869 18.0348 38.8063 17.3672 35.3289C17.0831 33.8497 16.4818 32.4626 15.8474 30.9979C15.194 29.4944 14.5216 27.9425 14.1949 26.2401C13.7688 24.0092 14.2044 22.0061 14.6921 20.3475C14.8483 19.8091 15.4023 19.5036 15.9278 19.6636C16.4534 19.8237 16.7517 20.3911 16.5954 20.9295C16.1788 22.3553 15.8 24.0528 16.1456 25.8521C16.4297 27.3265 17.031 28.7087 17.6655 30.1734C18.3189 31.6769 18.996 33.2337 19.3227 34.9361C20.1702 39.3543 17.7413 42.4244 14.9572 43.5107C14.1428 43.8308 13.2574 44.0006 12.3531 44.0006Z\" fill=\"#00334C\"/><path d=\"M2.59271 18.5284C2.12033 18.5284 1.70098 18.2049 1.60458 17.7408C1.22379 15.8985 0.886379 13.3342 0.900839 11.4825C0.92494 8.43064 1.34429 4.07556 4.76177 1.48783C7.64421 -0.696734 12.4017 -0.443587 15.154 2.03632C18.6148 5.15847 18.7112 11.9372 18.0509 16.1376C17.9689 16.672 17.458 17.0423 16.8989 16.9626C16.3494 16.8783 15.9686 16.3813 16.0505 15.8422C16.6048 12.3075 16.6627 6.0773 13.7802 3.4802C11.751 1.64722 8.11658 1.44564 6.00054 3.04422C3.31091 5.07878 2.94458 8.63222 2.92048 11.4965C2.91083 13.2123 3.22896 15.6407 3.58083 17.3517C3.6917 17.8862 3.33501 18.4018 2.79033 18.5097C2.72285 18.5237 2.65537 18.5284 2.59271 18.5284Z\" fill=\"#4A9EDA\"/></svg>",
      "visible_if": "{{ section.settings.show_default_logo }}"
    },
    {
      "id": "header_background_color",
      "type": "color",
      "label": "Header background color",
      "info": "Custom background color for the header",
      "default": "#FFFFFF"
    },
    {
      "id": "hide_account_button",
      "type": "checkbox",
      "label": "Hide account button",
      "info": "Hide the account/login button from the header",
      "default": false
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "liquid",
      "name": "t:static_sections.header.blocks.liquid.name",
      "limit": 2,
      "settings": [
        {
          "id": "liquid",
          "type": "liquid",
          "label": "t:static_sections.header.blocks.liquid.settings.liquid.label",
          "info": "t:static_sections.header.blocks.liquid.settings.liquid.info"
        },
        {
          "type": "select",
          "id": "location",
          "label": "t:static_sections.header.blocks.liquid.settings.location.label",
          "options": [
            {
              "value": "top",
              "label": "t:static_sections.header.blocks.liquid.settings.location.options__1.label"
            },
            {
              "value": "main",
              "label": "t:static_sections.header.blocks.liquid.settings.location.options__2.label"
            }
          ],
          "default": "main"
        },
        {
          "id": "show_mobile",
          "type": "checkbox",
          "label": "t:static_sections.header.blocks.liquid.settings.show_mobile.label"
        }
      ]
    },
    {
      "type": "usp",
      "name": "t:static_sections.header.blocks.usp.name",
      "limit": 3,
      "settings": [
        {
          "id": "usp",
          "type": "richtext",
          "label": "t:static_sections.header.blocks.usp.settings.usp.label",
          "info": "t:static_sections.header.blocks.usp.settings.usp.info",
          "default": "<p>Give your customers more details about your online store</p>"
        },
        {
          "id": "icon",
          "type": "select",
          "label": "t:global.icon.label",
          "info": "t:global.icon.info",
          "options": [
            {
              "value": "none",
              "label": "t:global.icon.options__1.label"
            },
            {
              "value": "default",
              "label": "t:global.icon.options__default_check.label"
            },
            {
              "value": "group",
              "label": "t:global.icon.options__2.label"
            },
            {
              "value": "notification",
              "label": "t:global.icon.options__3.label"
            },
            {
              "value": "cloud_data",
              "label": "t:global.icon.options__4.label"
            },
            {
              "value": "verified",
              "label": "t:global.icon.options__5.label"
            },
            {
              "value": "truck",
              "label": "t:global.icon.options__6.label"
            },
            {
              "value": "image_placeholder",
              "label": "t:global.icon.options__7.label"
            },
            {
              "value": "help_call",
              "label": "t:global.icon.options__8.label"
            },
            {
              "value": "filters",
              "label": "t:global.icon.options__9.label"
            },
            {
              "value": "shopping_bag",
              "label": "t:global.icon.options__10.label"
            },
            {
              "value": "global_shipping",
              "label": "t:global.icon.options__11.label"
            },
            {
              "value": "barcode",
              "label": "t:global.icon.options__12.label"
            },
            {
              "value": "delivery_box_1",
              "label": "t:global.icon.options__13.label"
            },
            {
              "value": "delivery_box_2",
              "label": "t:global.icon.options__14.label"
            },
            {
              "value": "statistic",
              "label": "t:global.icon.options__15.label"
            },
            {
              "value": "review",
              "label": "t:global.icon.options__16.label"
            },
            {
              "value": "email",
              "label": "t:global.icon.options__17.label"
            },
            {
              "value": "coin",
              "label": "t:global.icon.options__18.label"
            },
            {
              "value": "24_hour_clock",
              "label": "t:global.icon.options__19.label"
            },
            {
              "value": "question",
              "label": "t:global.icon.options__20.label"
            },
            {
              "value": "24_7_call",
              "label": "t:global.icon.options__21.label"
            },
            {
              "value": "speech_bubbles",
              "label": "t:global.icon.options__22.label"
            },
            {
              "value": "coupon",
              "label": "t:global.icon.options__23.label"
            },
            {
              "value": "mobile_payment",
              "label": "t:global.icon.options__24.label"
            },
            {
              "value": "calculator",
              "label": "t:global.icon.options__25.label"
            },
            {
              "value": "secure",
              "label": "t:global.icon.options__26.label"
            }
          ],
          "default": "default"
        },
        {
          "id": "image",
          "type": "image_picker",
          "label": "t:static_sections.header.blocks.usp.settings.image.label"
        },
        {
          "type": "range",
          "id": "image_width",
          "label": "t:static_sections.header.blocks.usp.settings.image_width.label",
          "min": 5,
          "max": 180,
          "step": 5,
          "unit": "px",
          "default": 55
        }
      ]
    },
    {
      "type": "button",
      "name": "t:static_sections.header.blocks.button_menu.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "show_mobile",
          "label": "t:static_sections.header.blocks.button_menu.settings.show_mobile.label",
          "default": true
        },
        {
          "type": "link_list",
          "id": "button_menu",
          "label": "t:static_sections.header.blocks.button_menu.settings.button_menu.label",
          "default": "main-menu"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "positive plain",
              "label": "t:global.button.button_style.positive.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "buy_button plain",
              "label": "t:global.button.button_style.buy_button.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "dynamic_buy_button plain",
              "label": "t:global.button.button_style.dynamic_buy_button.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "positive inv",
              "label": "t:global.button.button_style.positive.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "buy_button inv",
              "label": "t:global.button.button_style.buy_button.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "dynamic_buy_button inv",
              "label": "t:global.button.button_style.dynamic_buy_button.label",
              "group": "t:global.button.button_style.group.inv"
            }
          ],
          "default": "primary plain"
        }
      ]
    },
    {
      "type": "promo_banners",
      "name": "t:static_sections.header.blocks.promo_banners.name",
      "settings": [
        {
          "type": "text",
          "id": "menu_item",
          "label": "t:static_sections.header.blocks.promo_banners.settings.menu_item.label",
          "info": "t:static_sections.header.blocks.promo_banners.settings.menu_item.info"
        },
        {
          "type": "select",
          "id": "height",
          "label": "t:global.layout.height.label_banner",
          "options": [
            {
              "value": "32/9",
              "label": "t:global.layout.height.16_9.label"
            },
            {
              "value": "21/9",
              "label": "t:global.layout.height.21_9.label"
            },
            {
              "value": "16/9",
              "label": "t:global.layout.height.16_9.label"
            },
            {
              "value": "1/1",
              "label": "t:global.layout.height.1_1.label"
            },
            {
              "value": "4/5",
              "label": "t:global.layout.height.4_5.label"
            },
            {
              "value": "adapt",
              "label": "t:global.layout.height.adapt_first.label"
            }
          ],
          "default": "1/1"
        },
        {
          "id": "image_zoom",
          "type": "checkbox",
          "label": "t:sections.gallery.settings.image_zoom.label",
          "default": false
        },
        {
          "id": "show_content_below",
          "type": "checkbox",
          "label": "t:sections.gallery.settings.show_content_below.label",
          "default": false
        },
        {
          "type": "select",
          "id": "layout",
          "label": "t:static_sections.header.blocks.promo_banners.settings.layout.label",
          "info": "t:static_sections.header.blocks.promo_banners.settings.layout.info",
          "options": [
            {
              "value": "columns",
              "label": "t:static_sections.header.blocks.promo_banners.settings.layout.options__1.label"
            },
            {
              "value": "rows",
              "label": "t:static_sections.header.blocks.promo_banners.settings.layout.options__2.label"
            }
          ],
          "default": "columns"
        },
        {
          "type": "header",
          "content": "t:static_sections.header.blocks.promo_banners.settings.banner_1.header"
        },
        {
          "id": "enable_banner_1",
          "type": "checkbox",
          "label": "t:static_sections.header.blocks.promo_banners.settings.banner_1.enable_banner_1.label",
          "default": true
        },
        {
          "id": "image_1",
          "type": "image_picker",
          "label": "t:sections.gallery.blocks.image.settings.image.label",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "video",
          "id": "video_1",
          "label": "t:sections.gallery.blocks.image.settings.video.label",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "fill_images_1",
          "type": "checkbox",
          "label": "t:sections.gallery.blocks.image.settings.fill_images.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "range",
          "id": "overlay_opacity_1",
          "label": "t:sections.gallery.blocks.image.settings.overlay_opacity.label",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0,
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "color_scheme",
          "id": "color_palette_1",
          "label": "t:global.color_palette.label",
          "default": "scheme-1",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "text_position_1",
          "type": "select",
          "label": "t:global.typography.text_position.label",
          "options": [
            {
              "value": "start",
              "label": "t:global.typography.text_position.top_left.label"
            },
            {
              "value": "start center",
              "label": "t:global.typography.text_position.top_center.label"
            },
            {
              "value": "start end",
              "label": "t:global.typography.text_position.top_right.label"
            },
            {
              "value": "center start",
              "label": "t:global.typography.text_position.center_left.label"
            },
            {
              "value": "center",
              "label": "t:global.typography.text_position.center_center.label"
            },
            {
              "value": "center end",
              "label": "t:global.typography.text_position.center_right.label"
            },
            {
              "value": "end start",
              "label": "t:global.typography.text_position.bottom_left.label"
            },
            {
              "value": "end center",
              "label": "t:global.typography.text_position.bottom_center.label"
            },
            {
              "value": "end",
              "label": "t:global.typography.text_position.bottom_right.label"
            }
          ],
          "default": "center",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "header",
          "content": "t:global.typography.title.label",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "richtext",
          "id": "title_1",
          "label": "t:global.typography.title.label",
          "info": "t:global.typography.title.info",
          "default": "<h3>Image banner</h3>",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "select",
          "id": "title_1_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ],
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "header",
          "content": "t:global.typography.section_text.header",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "text_1",
          "type": "richtext",
          "label": "t:sections.gallery.blocks.image.settings.text.label",
          "default": "<p>Give customers details about the banner image(s)</p>",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "header",
          "content": "t:global.button.header",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "show_link_1",
          "type": "checkbox",
          "label": "t:global.button.show_link.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "link_text_1",
          "type": "text",
          "label": "t:global.button.link_text.label",
          "default": "Button",
          "visible_if": "{{ block.settings.show_link_1 and block.settings.enable_banner_1 }}"
        },
        {
          "id": "link_url_1",
          "type": "url",
          "label": "t:global.button.link_url.label",
          "default": "/",
          "visible_if": "{{ block.settings.show_link_1 and block.settings.enable_banner_1 }}"
        },
        {
          "type": "select",
          "id": "button_style_1",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "primary link",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "secondary link",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "tertiary link",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.link"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_link_1 and block.settings.enable_banner_1 }}"
        },
        {
          "type": "header",
          "content": "t:global.overlay.header",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "show_overlay_link_1",
          "type": "checkbox",
          "label": "t:global.overlay.show_overlay_link.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "overlay_url_1",
          "type": "url",
          "label": "t:global.overlay.overlay_url.label",
          "visible_if": "{{ block.settings.enable_banner_1 and block.settings.show_overlay_link_1 }}"
        },
        {
          "type": "header",
          "content": "t:sections.gallery.blocks.image.settings.mobile.header",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "id": "image_mobile_1",
          "type": "image_picker",
          "label": "t:sections.gallery.blocks.image.settings.mobile.image_mobile.label",
          "visible_if": "{{ block.settings.enable_banner_1 }}"
        },
        {
          "type": "header",
          "content": "t:static_sections.header.blocks.promo_banners.settings.banner_2.header"
        },
        {
          "id": "enable_banner_2",
          "type": "checkbox",
          "label": "t:static_sections.header.blocks.promo_banners.settings.banner_2.enable_banner_2.label"
        },
        {
          "id": "image_2",
          "type": "image_picker",
          "label": "t:sections.gallery.blocks.image.settings.image.label",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "video",
          "id": "video_2",
          "label": "t:sections.gallery.blocks.image.settings.video.label",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "fill_images_2",
          "type": "checkbox",
          "label": "t:sections.gallery.blocks.image.settings.fill_images.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "range",
          "id": "overlay_opacity_2",
          "label": "t:sections.gallery.blocks.image.settings.overlay_opacity.label",
          "min": 0,
          "max": 100,
          "step": 5,
          "unit": "%",
          "default": 0,
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "color_scheme",
          "id": "color_palette_2",
          "label": "t:global.color_palette.label",
          "default": "scheme-1",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "text_position_2",
          "type": "select",
          "label": "t:global.typography.text_position.label",
          "options": [
            {
              "value": "start",
              "label": "t:global.typography.text_position.top_left.label"
            },
            {
              "value": "start center",
              "label": "t:global.typography.text_position.top_center.label"
            },
            {
              "value": "start end",
              "label": "t:global.typography.text_position.top_right.label"
            },
            {
              "value": "center start",
              "label": "t:global.typography.text_position.center_left.label"
            },
            {
              "value": "center",
              "label": "t:global.typography.text_position.center_center.label"
            },
            {
              "value": "center end",
              "label": "t:global.typography.text_position.center_right.label"
            },
            {
              "value": "end start",
              "label": "t:global.typography.text_position.bottom_left.label"
            },
            {
              "value": "end center",
              "label": "t:global.typography.text_position.bottom_center.label"
            },
            {
              "value": "end",
              "label": "t:global.typography.text_position.bottom_right.label"
            }
          ],
          "default": "center",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "header",
          "content": "t:global.typography.title.label",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "richtext",
          "id": "title_2",
          "label": "t:global.typography.title.label",
          "info": "t:global.typography.title.info",
          "default": "<h3>Image banner</h3>",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "select",
          "id": "title_2_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ],
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "header",
          "content": "t:global.typography.section_text.header",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "text_2",
          "type": "richtext",
          "label": "t:sections.gallery.blocks.image.settings.text.label",
          "default": "<p>Give customers details about the banner image(s)</p>",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "header",
          "content": "t:global.button.header",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "show_link_2",
          "type": "checkbox",
          "label": "t:global.button.show_link.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "link_text_2",
          "type": "text",
          "label": "t:global.button.link_text.label",
          "default": "Button",
          "visible_if": "{{ block.settings.show_link_2 and block.settings.enable_banner_2 }}"
        },
        {
          "id": "link_url_2",
          "type": "url",
          "label": "t:global.button.link_url.label",
          "default": "/",
          "visible_if": "{{ block.settings.show_link_2 and block.settings.enable_banner_2 }}"
        },
        {
          "type": "select",
          "id": "button_style_2",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "primary link",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "secondary link",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "tertiary link",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.link"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_link_2 and block.settings.enable_banner_2 }}"
        },
        {
          "type": "header",
          "content": "t:global.overlay.header",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "show_overlay_link_2",
          "type": "checkbox",
          "label": "t:global.overlay.show_overlay_link.label",
          "default": true,
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "overlay_url_2",
          "type": "url",
          "label": "t:global.overlay.overlay_url.label",
          "visible_if": "{{ block.settings.enable_banner_2 and block.settings.show_overlay_link_2 }}"
        },
        {
          "type": "header",
          "content": "t:sections.gallery.blocks.image.settings.mobile.header",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "id": "image_mobile_2",
          "type": "image_picker",
          "label": "t:sections.gallery.blocks.image.settings.mobile.image_mobile.label",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "select",
          "id": "layout_mobile",
          "label": "t:sections.gallery.settings.mobile.layout_mobile.label",
          "options": [
            {
              "value": "rows",
              "label": "t:sections.gallery.settings.mobile.layout_mobile.options__1.label"
            },
            {
              "value": "compact",
              "label": "t:sections.gallery.settings.mobile.layout_mobile.options__2.label"
            }
          ],
          "default": "rows",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        },
        {
          "type": "select",
          "id": "height_mobile",
          "label": "t:global.layout.height.label_banner",
          "options": [
            {
              "value": "32/9",
              "label": "t:global.layout.height.32_9.label"
            },
            {
              "value": "21/9",
              "label": "t:global.layout.height.21_9.label"
            },
            {
              "value": "16/9",
              "label": "t:global.layout.height.16_9.label"
            },
            {
              "value": "1/1",
              "label": "t:global.layout.height.1_1.label"
            },
            {
              "value": "4/5",
              "label": "t:global.layout.height.4_5.label"
            },
            {
              "value": "adapt",
              "label": "t:global.layout.height.adapt_first.label"
            }
          ],
          "default": "16/9",
          "visible_if": "{{ block.settings.enable_banner_2 }}"
        }
      ]
    },
    {
      "type": "promo_collection_list",
      "name": "t:static_sections.header.blocks.promo_collection_list.name",
      "settings": [
        {
          "type": "text",
          "id": "menu_item",
          "label": "t:static_sections.header.blocks.promo_collection_list.settings.menu_item.label",
          "info": "t:static_sections.header.blocks.promo_collection_list.settings.menu_item.info"
        },
        {
          "type": "collection_list",
          "id": "collections",
          "label": "t:sections.collection_list.settings.collections.label"
        },
        {
          "id": "image_ratio",
          "type": "select",
          "label": "t:sections.collection_list.settings.image_ratio.label",
          "options": [
            {
              "value": "portrait",
              "label": "t:sections.collection_list.settings.image_ratio.options__1.label"
            },
            {
              "value": "square",
              "label": "t:sections.collection_list.settings.image_ratio.options__2.label"
            },
            {
              "value": "landscape",
              "label": "t:sections.collection_list.settings.image_ratio.options__3.label"
            }
          ],
          "default": "square"
        },
        {
          "id": "fill_images",
          "type": "checkbox",
          "label": "t:sections.collection_list.settings.fill_images.label",
          "default": true
        },
        {
          "type": "select",
          "id": "layout",
          "label": "t:sections.collection_list.settings.layout.label",
          "options": [
            {
              "value": "slider",
              "label": "t:sections.collection_list.settings.layout.options__1.label"
            },
            {
              "value": "grid",
              "label": "t:sections.collection_list.settings.layout.options__2.label"
            }
          ],
          "default": "slider"
        },
        {
          "type": "range",
          "id": "number_of_items",
          "label": "t:sections.collection_list.settings.number_of_items.label",
          "min": 2,
          "max": 8,
          "step": 1,
          "default": 5
        },
        {
          "type": "select",
          "id": "text_alignment",
          "label": "t:sections.collection_list.settings.text_alignment.label",
          "options": [
            {
              "value": "start",
              "label": "t:sections.collection_list.settings.text_alignment.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.collection_list.settings.text_alignment.options__2.label"
            }
          ],
          "default": "start"
        },
        {
          "type": "richtext",
          "id": "title",
          "label": "t:global.typography.title.label",
          "info": "t:global.typography.title.info",
          "default": "<h3>Collection list</h3>"
        },
        {
          "type": "select",
          "id": "title_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ]
        },
        {
          "type": "header",
          "content": "t:global.button.header"
        },
        {
          "id": "show_link",
          "type": "checkbox",
          "label": "t:global.button.show_link.label",
          "default": true
        },
        {
          "id": "link_text",
          "type": "text",
          "label": "t:global.button.link_text.label",
          "default": "View all",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "id": "link_url",
          "type": "url",
          "label": "t:global.button.link_url.label",
          "default": "/collections",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "type": "select",
          "id": "button_style",
          "label": "t:global.button.button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "primary link",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "secondary link",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.link"
            },
            {
              "value": "tertiary link",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.link"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_link }}"
        },
        {
          "type": "header",
          "content": "t:sections.collection_list.settings.collection_heading.header"
        },
        {
          "id": "show_collection_titles",
          "type": "checkbox",
          "label": "t:sections.collection_list.settings.show_collection_titles.label",
          "default": true
        },
        {
          "type": "select",
          "id": "collection_title_position",
          "label": "t:sections.collection_list.settings.collection_title_position.label",
          "options": [
            {
              "value": "start",
              "label": "t:sections.collection_list.settings.collection_title_position.options__1.label"
            },
            {
              "value": "center",
              "label": "t:sections.collection_list.settings.collection_title_position.options__2.label"
            }
          ],
          "default": "center",
          "visible_if": "{{ block.settings.show_collection_titles }}"
        },
        {
          "type": "select",
          "id": "collection_title_size",
          "label": "t:sections.collection_list.settings.collection_title_size.label",
          "options": [
            {
              "value": "h1",
              "label": "t:global.typography.title_size.h1.label"
            },
            {
              "value": "h2",
              "label": "t:global.typography.title_size.h2.label"
            },
            {
              "value": "h3",
              "label": "t:global.typography.title_size.h3.label"
            },
            {
              "value": "h4",
              "label": "t:global.typography.title_size.h4.label"
            },
            {
              "value": "h5",
              "label": "t:global.typography.title_size.h5.label"
            }
          ],
          "default": "h5",
          "visible_if": "{{ block.settings.show_collection_titles }}"
        },
        {
          "type": "header",
          "content": "t:sections.collection_list.settings.mobile.header"
        },
        {
          "type": "select",
          "id": "mobile_layout",
          "label": "t:sections.collection_list.settings.layout.label",
          "options": [
            {
              "value": "slider",
              "label": "t:sections.collection_list.settings.layout.options__1.label"
            },
            {
              "value": "grid",
              "label": "t:sections.collection_list.settings.layout.options__2.label"
            }
          ],
          "default": "slider"
        }
      ]
    },
    {
      "type": "custom_megamenu",
      "name": "Custom Megamenu",
      "settings": [
        {
          "type": "text",
          "id": "menu_item",
          "label": "Menu Item",
          "info": "Enter the exact menu item name to attach this custom megamenu to"
        },
        {
          "type": "header",
          "content": "Left Column - Collections"
        },
        {
          "type": "collection_list",
          "id": "collections",
          "label": "Collections"
        },
        {
          "type": "checkbox",
          "id": "show_left_button",
          "label": "Show Left Column Button",
          "default": true
        },
        {
          "type": "text",
          "id": "left_button_text",
          "label": "Left Button Text",
          "default": "Shop All Everyday",
          "visible_if": "{{ block.settings.show_left_button }}"
        },
        {
          "type": "url",
          "id": "left_button_url",
          "label": "Left Button URL",
          "visible_if": "{{ block.settings.show_left_button }}"
        },
        {
          "type": "select",
          "id": "left_button_style",
          "label": "Left Button Style",
          "options": [
            {
              "value": "primary plain",
              "label": "Primary Plain"
            },
            {
              "value": "secondary plain",
              "label": "Secondary Plain"
            },
            {
              "value": "primary inv",
              "label": "Primary Inverse"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_left_button }}"
        },
        {
          "type": "header",
          "content": "Right Column Settings"
        },
        {
          "type": "richtext",
          "id": "right_title",
          "label": "Right Column Title",
          "default": "<h3>Featured product</h3>"
        },
        {
          "type": "product",
          "id": "featured_product",
          "label": "Featured Product"
        },
        {
          "type": "image_picker",
          "id": "featured_image",
          "label": "Featured Image",
          "info": "If no product is selected, this image will be used"
        },
        {
          "type": "text",
          "id": "featured_link_text",
          "label": "Featured Link Text",
          "default": "On my feet all day"
        },
        {
          "type": "url",
          "id": "featured_link_url",
          "label": "Featured Link URL"
        },
        {
          "type": "textarea",
          "id": "featured_description",
          "label": "Featured Description",
          "default": "For hard working feet - 14 H a day"
        }
      ]
    }
  ]
}
{% endschema %}
