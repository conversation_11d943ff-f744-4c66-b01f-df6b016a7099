{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="about-ten-toes-section" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="about-ten-toes-content">
    <div class="ten-toes-layout">
      <!-- Left Column - Image -->
      <div class="ten-toes-image-column">
        {% if section.settings.image != blank %}
          <div class="ten-toes-image-wrapper">
            <img src="{{ section.settings.image | image_url: width: 600 }}" 
                 alt="{{ section.settings.image.alt | default: section.settings.title }}"
                 loading="lazy">
          </div>
        {% else %}
          <div class="ten-toes-image-placeholder">
            <div class="placeholder-content">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 19 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z" fill="#cccccc"/>
              </svg>
              <p>Add Image</p>
            </div>
          </div>
        {% endif %}
      </div>

      <!-- Right Column - Text Content -->
      <div class="ten-toes-text-column">
        <!-- Decorative Clouds -->
        <div class="ten-toes-clouds">
          <div class="cloud-left">
            <svg xmlns="http://www.w3.org/2000/svg" width="83" height="75" viewBox="0 0 83 75" fill="none">
              <mask id="a" mask-type="alpha" maskUnits="userSpaceOnUse" x="20" y="1" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M20.622 1H71.83v51.208H20.622z" fill="#fff"/>
              </mask>
              <g mask="url(#a)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M71.83 26.604c0 14.14-11.464 25.604-25.605 25.604S20.622 40.744 20.622 26.604C20.622 12.463 32.085 1 46.225 1S71.83 12.463 71.83 26.604" fill="#ECECEC"/>
              </g>
              <mask id="b" mask-type="alpha" maskUnits="userSpaceOnUse" x="61" y="25" width="22" height="22">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M61 25h22v22H61z" fill="#fff"/>
              </mask>
              <g mask="url(#b)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M83 36c0 6.075-4.925 11-11 11s-11-4.925-11-11 4.925-11 11-11 11 4.925 11 11" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M34.183 20.353c0 7.49-6.071 13.561-13.561 13.561s-13.56-6.072-13.56-13.56c0-7.49 6.07-13.56 13.56-13.56s13.56 6.07 13.56 13.56" fill="#ECECEC"/>
              <mask id="c" mask-type="alpha" maskUnits="userSpaceOnUse" x="26" y="23" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26.64 23.426h51.208v51.208H26.64z" fill="#fff"/>
              </mask>
              <g mask="url(#c)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M73.207 38.604c0 14.141-11.463 25.604-25.604 25.604S22 52.745 22 38.604 33.463 13 47.603 13s25.604 11.464 25.604 25.604" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M41.244 40.957c0 11.39-9.232 20.623-20.622 20.623S0 52.347 0 40.957s9.232-20.622 20.622-20.622 20.622 9.233 20.622 20.622" fill="#ECECEC"/>
            </svg>
          </div>
          <div class="cloud-right">
            <svg xmlns="http://www.w3.org/2000/svg" width="96" height="75" viewBox="0 0 96 75" fill="none">
              <mask id="a2" mask-type="alpha" maskUnits="userSpaceOnUse" x="20" y="1" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M20.622 1H71.83v51.208H20.622z" fill="#fff"/>
              </mask>
              <g mask="url(#a2)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M71.83 26.604c0 14.14-11.464 25.604-25.605 25.604S20.622 40.744 20.622 26.604C20.622 12.463 32.085 1 46.225 1S71.83 12.463 71.83 26.604" fill="#ECECEC"/>
              </g>
              <mask id="b2" mask-type="alpha" maskUnits="userSpaceOnUse" x="60" y="18" width="36" height="36">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M60.99 18.539h34.97v34.969H60.99z" fill="#fff"/>
              </mask>
              <g mask="url(#b2)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M95.96 36.023c0 9.657-7.828 17.485-17.484 17.485-9.658 0-17.486-7.828-17.486-17.485s7.828-17.484 17.486-17.484c9.656 0 17.484 7.828 17.484 17.484" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M34.183 20.353c0 7.49-6.071 13.561-13.561 13.561s-13.56-6.072-13.56-13.56c0-7.49 6.07-13.56 13.56-13.56s13.56 6.07 13.56 13.56" fill="#ECECEC"/>
              <mask id="c2" mask-type="alpha" maskUnits="userSpaceOnUse" x="26" y="23" width="52" height="52">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M26.64 23.426h51.208v51.208H26.64z" fill="#fff"/>
              </mask>
              <g mask="url(#c2)">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M77.848 49.03c0 14.141-11.463 25.604-25.604 25.604S26.64 63.171 26.64 49.03s11.463-25.604 25.603-25.604S77.848 34.89 77.848 49.03" fill="#ECECEC"/>
              </g>
              <path fill-rule="evenodd" clip-rule="evenodd" d="M41.244 40.957c0 11.39-9.232 20.623-20.622 20.623S0 52.347 0 40.957s9.232-20.622 20.622-20.622 20.622 9.233 20.622 20.622" fill="#ECECEC"/>
            </svg>
          </div>
        </div>

        {% if section.settings.title != blank %}
          <h2 class="ten-toes-title">{{ section.settings.title }}</h2>
        {% endif %}

        {% if section.settings.description != blank %}
          <div class="ten-toes-description">
            {{ section.settings.description }}
          </div>
        {% endif %}

        {% if section.settings.features != blank %}
          <div class="ten-toes-features">
            {{ section.settings.features }}
          </div>
        {% endif %}

        {% if section.settings.highlight_text != blank %}
          <div class="ten-toes-highlight">
            {{ section.settings.highlight_text }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
/* Ten Toes Service Section */
.about-ten-toes-section {
  background-color: #fff;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.about-ten-toes-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.ten-toes-layout {
  display: flex;
  align-items: flex-start;
  gap: 100px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Left Column - Image */
.ten-toes-image-column {
  flex: 1;
  max-width: 557px;
}

.ten-toes-image-wrapper {
  width: 100%;
  height: 534px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.ten-toes-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.ten-toes-image-placeholder {
  width: 100%;
  height: 534px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.placeholder-content {
  text-align: center;
  color: #999999;
}

.placeholder-content p {
  margin: 10px 0 0 0;
  font-size: 14px;
  font-weight: 500;
}

/* Right Column - Text */
.ten-toes-text-column {
  padding-top: 90px;
  flex: 1;
  padding-left: 10px;
  max-width: 485px;
  position: relative;
}

.ten-toes-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 20px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: left;
  width: 100%;
}

.ten-toes-description {
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-bottom: 20px;
}

.ten-toes-description p {
  margin: 0 0 16px 0;
}

.ten-toes-description p:last-child {
  margin-bottom: 0;
}

.ten-toes-features {
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-bottom: 20px;
}

.ten-toes-features p {
  margin: 0 0 16px 0;
}

.ten-toes-features p:last-child {
  margin-bottom: 0;
}

.ten-toes-highlight {
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  font-weight: 400;
}

.ten-toes-highlight p {
  margin: 0;
}

.ten-toes-highlight strong {
  font-weight: 600;
}

/* Decorative Clouds */
.ten-toes-clouds {
  position: absolute;
  top: -40px;
  left: 0;
  right: 0;
  height: 75px;
  pointer-events: none;
  z-index: 1;
}

.cloud-left {
  position: absolute;
  top: 0;
  left: -20px;
  opacity: 0.8;
}

.cloud-left svg {
  width: 60px;
  height: auto;
}

.cloud-right {
  position: absolute;
  top: 10px;
  right: -30px;
  opacity: 0.8;
}

.cloud-right svg {
  width: 70px;
  height: auto;
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .about-ten-toes-section {
    padding-top: 0px !important;
    padding-bottom: 60px !important;
  }

  .ten-toes-layout {
    flex-direction: column;
    gap: 30px;
  }

  .ten-toes-image-column {
    max-width: 100%;
  }

  .ten-toes-image-wrapper,
  .ten-toes-image-placeholder {
    height: 300px;
  }

  .ten-toes-text-column {
    padding-left: 0;
    max-width: 100%;
    padding-top: 30px;
  }

  .ten-toes-clouds {
    top: -20px;
    height: 50px;
  }

  .cloud-left svg {
    width: 40px;
  }

  .cloud-right svg {
    width: 50px;
  }

  .cloud-left {
    left: -10px;
  }

  .cloud-right {
    right: -15px;
    top: 5px;
  }

  .ten-toes-title {
    font-size: 28px;
    line-height: 39.2px;
    margin-bottom: 16px;
  }

  .ten-toes-description,
  .ten-toes-features,
  .ten-toes-highlight {
    font-size: 14px;
    line-height: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .about-ten-toes-content {
    padding: 0 15px;
  }

  .ten-toes-title {
    font-size: 24px;
    line-height: 33.6px;
  }

  .ten-toes-image-wrapper,
  .ten-toes-image-placeholder {
    height: 250px;
  }
}
</style>

{% schema %}
{
  "name": "About Ten Toes Service",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "A ten toes service"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Service Image"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>Upstep specializes in the design and manufacture of custom foot orthotics and foot care solutions.</p><p>We provide the best custom orthotics available and do so at the most affordable price, in the fastest time, with excellent customer support.</p>"
    },
    {
      "type": "richtext",
      "id": "features",
      "label": "Features Text",
      "default": "<p>Our in-house professional Podiatrist designs each custom orthotic.</p>"
    },
    {
      "type": "richtext",
      "id": "highlight_text",
      "label": "Highlight Text",
      "default": "<p><strong>Upstep Everyday</strong>, are custom designed for daily activities, to relieve your foot pain, and enhance every type of shoe.</p>"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "label": "Desktop Spacing",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "label": "Mobile Spacing",
      "min": 0,
      "max": 100,
      "step": 10,
      "unit": "px",
      "default": 60
    }
  ]
}
{% endschema %}
