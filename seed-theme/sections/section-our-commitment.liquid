{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="our-commitment-section" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="our-commitment-content">
    <div class="commitment-layout">
      <!-- Left Column - Text Content -->
      <div class="commitment-text-column">
        {% if section.settings.title != blank %}
          <h2 class="commitment-title">{{ section.settings.title }}</h2>
        {% endif %}

        {% if section.settings.guarantee_title != blank or section.settings.guarantee_text != blank %}
          <div class="commitment-section">
            {% if section.settings.guarantee_title != blank %}
              <h3 class="commitment-subtitle">{{ section.settings.guarantee_title }}</h3>
            {% endif %}
            {% if section.settings.guarantee_text != blank %}
              <div class="commitment-text">{{ section.settings.guarantee_text }}</div>
            {% endif %}
          </div>
        {% endif %}

        {% if section.settings.purpose_title != blank or section.settings.purpose_text != blank %}
          <div class="commitment-section">
            {% if section.settings.purpose_title != blank %}
              <h3 class="commitment-subtitle">{{ section.settings.purpose_title }}</h3>
            {% endif %}
            {% if section.settings.purpose_text != blank %}
              <div class="commitment-text">{{ section.settings.purpose_text }}</div>
            {% endif %}
          </div>
        {% endif %}

        {% if section.settings.support_title != blank or section.settings.support_text != blank %}
          <div class="commitment-section">
            {% if section.settings.support_title != blank %}
              <h3 class="commitment-subtitle">{{ section.settings.support_title }}</h3>
            {% endif %}
            {% if section.settings.support_text != blank %}
              <div class="commitment-text">{{ section.settings.support_text }}</div>
            {% endif %}
          </div>
        {% endif %}

        {% if section.settings.quality_title != blank or section.settings.quality_text != blank %}
          <div class="commitment-section">
            {% if section.settings.quality_title != blank %}
              <h3 class="commitment-subtitle">{{ section.settings.quality_title }}</h3>
            {% endif %}
            {% if section.settings.quality_text != blank %}
              <div class="commitment-text">{{ section.settings.quality_text }}</div>
            {% endif %}
          </div>
        {% endif %}
      </div>

      <!-- Right Column - Image -->
      <div class="commitment-image-column">
        {% if section.settings.image != blank %}
          <div class="commitment-image-wrapper">
            <img src="{{ section.settings.image | image_url: width: 600 }}"
                 alt="{{ section.settings.image.alt | default: section.settings.title }}"
                 width="549"
                 height="356"
                 loading="lazy">
          </div>
        {% else %}
          <div class="commitment-image-placeholder">
            <div class="placeholder-content">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M21 19V5C21 3.9 20.1 3 19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 19 20.1 21 19ZM8.5 13.5L11 16.51L14.5 12L19 18H5L8.5 13.5Z" fill="#cccccc"/>
              </svg>
              <p>Add Image</p>
            </div>
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
/* Our Commitment Section */
.our-commitment-section {
  background-color: #fff;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.our-commitment-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.commitment-layout {
  display: flex;
  align-items: flex-start;
  gap: 100px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Left Column - Text */
.commitment-text-column {
  flex: 1;
  max-width: 485px;
  padding-right: 20px;
}

.commitment-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 20px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: left;
  width: 485px;
}

.commitment-section {
  margin-bottom: 20px;
}

.commitment-section:last-child {
  margin-bottom: 0;
}

.commitment-subtitle {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: inline;
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 700;
  height: auto;
  line-height: 22.4px;
  width: auto;
  margin-bottom: 8px;
  display: block;
}

.commitment-text {
  background-color: rgba(0, 0, 0, 0);
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: inline;
  font-family: Lato, sans-serif;
  font-size: 16px;
  height: auto;
  line-height: 22.4px;
  width: auto;
}

.commitment-text p {
  margin: 0 0 8px 0;
}

.commitment-text p:last-child {
  margin-bottom: 0;
}

/* Right Column - Image */
.commitment-image-column {
  flex: 1;
  max-width: 590px;
  position: relative;
}

.commitment-image-wrapper {
  width: 549px;
  height: 356px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  margin: 20.5px;
  transition: opacity 0.3s ease;
  vertical-align: middle;
}

.commitment-image-wrapper img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
}

.commitment-image-placeholder {
  width: 549px;
  height: 356px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: 20.5px;
}

.placeholder-content {
  text-align: center;
  color: #999999;
}

.placeholder-content p {
  margin: 10px 0 0 0;
  font-size: 14px;
  font-weight: 500;
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .our-commitment-section {
    padding-top: 60px !important;
    padding-bottom: 60px !important;
  }

  .commitment-layout {
    flex-direction: column;
    gap: 30px;
  }

  .commitment-text-column {
    max-width: 100%;
    padding-right: 0;
  }

  .commitment-image-column {
    max-width: 100%;
  }

  .commitment-image-wrapper,
  .commitment-image-placeholder {
    width: 100%;
    height: 280px;
    margin: 0;
  }

  .commitment-title {
    font-size: 28px;
    line-height: 39.2px;
    margin-bottom: 16px;
    width: 100%;
  }

  .commitment-subtitle {
    font-size: 14px;
    line-height: 20px;
  }

  .commitment-text {
    font-size: 14px;
    line-height: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .our-commitment-content {
    padding: 0 15px;
  }

  .commitment-title {
    font-size: 24px;
    line-height: 33.6px;
  }

  .commitment-image-wrapper,
  .commitment-image-placeholder {
    height: 220px;
  }
}
</style>

{% schema %}
{
  "name": "Our Commitment",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "Our Commitment"
    },
    {
      "type": "text",
      "id": "guarantee_title",
      "label": "Our Guarantee Title",
      "default": "Our Guarantee"
    },
    {
      "type": "richtext",
      "id": "guarantee_text",
      "label": "Our Guarantee Text",
      "default": "<p>If you're not in love with your custom orthotics within 180 days, we'll refund your order - no questions asked! *180-day money-back guarantee only applies to custom orthotics.</p>"
    },
    {
      "type": "text",
      "id": "purpose_title",
      "label": "Purpose & Vision Title",
      "default": "Purpose & Vision"
    },
    {
      "type": "richtext",
      "id": "purpose_text",
      "label": "Purpose & Vision Text",
      "default": "<p>To change the way you step by providing state-of-the-art custom orthotics with quick delivery at an affordable price.</p>"
    },
    {
      "type": "text",
      "id": "support_title",
      "label": "Custom Support Title",
      "default": "Custom support. Custom Care"
    },
    {
      "type": "richtext",
      "id": "support_text",
      "label": "Custom Support Text",
      "default": "<p>Excellent service is our mission. We strive to deliver exceptional value and service.</p>"
    },
    {
      "type": "text",
      "id": "quality_title",
      "label": "Quality Standard Title",
      "default": "Quality Standard"
    },
    {
      "type": "richtext",
      "id": "quality_text",
      "label": "Quality Standard Text",
      "default": "<p>Our custom orthotics are designed specifically for your needs and to fit your shoes - any shoes.</p>"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Section Image"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Desktop Spacing",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Mobile Spacing",
      "default": 60
    }
  ]
}
{% endschema %}
