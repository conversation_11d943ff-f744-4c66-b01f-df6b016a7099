{%- liquid
  assign newsletter_block = section.blocks | where: 'type', 'socials_newsletter' | first
  assign customer_service_block = section.blocks | where: 'type', 'customer_support' | first
  assign menu_block = section.blocks | where: 'type', 'menu' | first
  if menu_block and menu_block.settings.split_menu
    assign split_menu = true
  endif
  if newsletter_block
    assign button_color = newsletter_block.settings.footer_button_style | split: ' ' | first
    assign button_style = newsletter_block.settings.footer_button_style | split: ' ' | last
  endif
-%}
<nav id="shopify-section-footer" class="palette-{{ section.settings.footer_color }} module-color-palette" style="--main_h_small:var(--main_{{ section.settings.title_size_blocks }});">
  {%- for block in section.blocks -%}
    {%- case block.type -%}
      {%- when '@app' -%}
        {% render block %}
      {%- when 'text' %}
        {%- if block.settings.title != empty or block.settings.text != empty -%}
        <div class="
          {% if block.settings.title_underline_style != 'none' %}
            title-underline-none
            {% if block.settings.title_underline_style contains 'accent' %}
              title-underline-accent
            {% elsif block.settings.title_underline_style contains 'gradient' %}
              title-underline-gradient
            {% endif %}
            {% if block.settings.title_underline_style contains 'secondary_font' %}
              title-underline-secondary-font
            {% endif %}
          {% endif %}
          " {{ block.shopify_attributes }}>
            <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}">
            {{ block.settings.title }}
            </{{ section.settings.title_size_blocks }}>
            {{ block.settings.text }}
          </div>
        {%- endif -%}
      {%- when 'html' %}
        {%- if block.settings.html != empty -%}
          <div class="strong" {{ block.shopify_attributes }}>
            {{ block.settings.html }}
          </div>
        {%- endif -%}
      {%- when 'logo' -%}
        <div class="footer-logo-block" {{ block.shopify_attributes }}>
          {%- if block.settings.logo_link != blank -%}
            <a href="{{ block.settings.logo_link }}">
          {%- endif -%}

          {%- if block.settings.logo_image -%}
            <img
              src="{{ block.settings.logo_image | image_url: width: block.settings.logo_width }}"
              srcset="{% render 'image-srcset', image: block.settings.logo_image, max_width: block.settings.logo_width %}"
              width="{{ block.settings.logo_width }}"
              height="auto"
              alt="{{ block.settings.logo_image.alt | default: shop.name | escape }}"
              loading="lazy"
              class="footer-logo-image"
              style="
                width: {{ block.settings.logo_width }}px;
                transform: translate({{ block.settings.logo_offset_x }}px, {{ block.settings.logo_offset_y }}px);
              "
            >
          {%- else -%}
            <div class="footer-logo-placeholder" style="
              width: {{ block.settings.logo_width }}px;
              height: {{ block.settings.logo_height }}px;
              background-color: {{ block.settings.placeholder_color }};
              transform: translate({{ block.settings.logo_offset_x }}px, {{ block.settings.logo_offset_y }}px);
              display: flex;
              align-items: center;
              justify-content: center;
              color: #666;
              font-size: 14px;
            ">
              {{ shop.name }}
            </div>
          {%- endif -%}

          {%- if block.settings.logo_link != blank -%}
            </a>
          {%- endif -%}
        </div>
      {%- when 'customer_support' -%}
        <div class="m6cn{% unless block.settings.customer_support_img %} mobile-no-img{% endunless %}" {{ block.shopify_attributes }}>
          {%- if block.settings.title != empty -%}
            <div class="
              {% if block.settings.title_underline_style != 'none' %}
                title-underline-none
                {% if block.settings.title_underline_style contains 'accent' %}
                  title-underline-accent
                {% elsif block.settings.title_underline_style contains 'gradient' %}
                  title-underline-gradient
                {% endif %}
                {% if block.settings.title_underline_style contains 'secondary_font' %}
                  title-underline-secondary-font
                {% endif %}
              {% endif %}
            ">
              <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}">
              {{ block.settings.title }}
              </{{ section.settings.title_size_blocks }}>
            </div>
          {%- endif -%}
          {{ block.settings.extra_customer_support }}
          <ul class="l4cn">
            {% if shop.phone != empty and shop.phone != blank and block.settings.show_phone_link %}
              <li><a href="tel:{{ shop.phone }}"><i aria-hidden="true" class="icon-phone"></i> {{ shop.phone }}</a></li>
            {% endif %}
            {% if shop.email != empty and shop.email != blank and block.settings.show_mail_link %}
              <li><a href="mailto:{{ shop.email }}" class="email"><i aria-hidden="true" class="icon-envelope"></i> {{ shop.email }}</a></li>
            {% endif %}
            {% if settings.whatsapp != empty and settings.whatsapp != 0 and block.settings.show_whatsapp_link %}
              <li><a href="https://wa.me/{{ settings.whatsapp }}"><i aria-hidden="true" class="icon-whatsapp-overlay"></i> {{ 'footer.whatsapp_html' | t }}</a></li>
            {% endif %}
          </ul>
          {%- if block.settings.customer_support_img -%}
            {%- assign default_alt = 'footer.customer_service_img_default_alt' | t %}
            <figure>
              <picture class="static">
                <img
                        src="{{- block.settings.customer_support_img | image_url: width: block.settings.customer_support_img_width -}}"
                        srcset="{% render 'image-srcset', image: block.settings.customer_support_img %}"
                        sizes="
                    (min-width: 1100) {{ block.settings.customer_support_img_width }}px
                    (min-width: 760px) 0
                    (min-width: 400px) 200px
                    0
                  "
                        width="{{ block.settings.customer_support_img_width }}"
                        height="217"
                        alt="{{ block.settings.customer_support_img.alt | default: default_alt | escape }}"
                        loading="lazy"
                >
              </picture>
            </figure>
          {%- endif -%}
        </div>
      {%- when 'socials_newsletter' -%}
        <div class="strong" {{ block.shopify_attributes }}>
          {% if block.settings.show_socials %}
            {%- capture 'socials' -%}
              {%- if settings.social_instagram -%}instagram|{{settings.social_instagram}}|{{ settings.social_instagram_name }}->{%- endif -%}
              {%- if settings.social_pinterest -%}pinterest|{{settings.social_pinterest}}|{{ settings.social_pinterest_name }}->{%- endif -%}
              {%- if settings.social_youtube -%}youtube|{{settings.social_youtube}}|{{ settings.social_youtube_name }}->{%- endif -%}
              {%- if settings.social_facebook -%}facebook|{{settings.social_facebook}}|{{ settings.social_facebook_name }}->{%- endif -%}
              {%- if settings.social_twitter -%}twitter|{{settings.social_twitter}}|{{ settings.social_twitter_name }}->{%- endif -%}
              {%- if settings.social_tiktok -%}tiktok|{{settings.social_tiktok}}|{{ settings.social_tiktok_name }}->{%- endif -%}
              {%- if settings.social_tumblr -%}tumblr|{{settings.social_tumblr}}|{{ settings.social_tumblr_name }}->{%- endif -%}
              {%- if settings.social_snapchat -%}snapchat|{{settings.social_snapchat}}|{{ settings.social_snapchat_name }}{%- endif -%}
            {%- endcapture -%}
            {%- assign socials = socials | split: "->" -%}
            {%- if block.settings.title != empty -%}
              <div class="
                {% if block.settings.title_underline_style != 'none' %}
                  title-underline-none
                  {% if block.settings.title_underline_style contains 'accent' %}
                    title-underline-accent
                  {% elsif block.settings.title_underline_style contains 'gradient' %}
                    title-underline-gradient
                  {% endif %}
                  {% if block.settings.title_underline_style contains 'secondary_font' %}
                    title-underline-secondary-font
                  {% endif %}
                {% endif %}
              ">
                  <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}">
                  {{ block.settings.title }}
                </{{ section.settings.title_size_blocks }}>
              </div>
            {%- endif -%}
            {%- if socials.size > 0 -%}
              <ul class="l4sc">
                {%- for social in socials -%}
                  {%- assign social_info = social | split: '|' -%}
                  {%- assign social_translation = 'socials.' | append: social_info[0] -%}
                  <li><a aria-label="{{ social_translation | t }}" href="{{ social_info[1] }}" rel="external noopener" target="external"><i aria-hidden="true" class="icon-{{ social_info[0] }}"></i><span>{{ social_translation | t }}</span></a></li>
                {%- endfor -%}
              </ul>
            {%- else -%}
              {%- if shop.brand.metafields.social_links.size > 0 -%}
                <ul class="l4sc">
                  {%- for social in shop.brand.metafields.social_links -%}
                    {%- assign social_translation = 'socials.' | append: social[0] -%}
                    <li><a aria-label="{{ social_translation | t }}" href="{{ social[1] }}" rel="external noopener" target="external"><i aria-hidden="true" class="icon-{{ social[0] }}"></i><span>{{ social_translation | t }}</span></a></li>
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            {%- endif -%}
          {%- endif -%}
          {%- if shop.features.follow_on_shop? and block.settings.enable_follow_on_shop -%}
            <p class="follow-on-shop">
              {% comment %} TODO: enable theme-check once `login_button` is accepted as valid filter {% endcomment %}
              {% # theme-check-disable %}
              {{ shop | login_button: action: 'follow' }}
              {% # theme-check-enable %}
            </p>
          {%- endif -%}
          {%- if block.settings.show_newsletter -%}
            {%- form 'customer', id: 'newsletter-footer' -%}
              <input type="hidden" name="contact[tags]" value="newsletter">
            {%- if form.errors -%}
              <script>
                document.addEventListener('DOMContentLoaded', function () {
                  var alertAttributes = { message: "{{ 'newsletter.form.email_placeholder' | t }} {{ form.errors.messages['email'] }}", type: "error", origin: "newsletter" },
                          showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                  window.dispatchEvent(showAlertEvent);
                });
              </script>
            {%- elsif form.posted_successfully? -%}
              <script>
                document.addEventListener('DOMContentLoaded', function () {
                  var alertAttributes = { message: "{{ 'newsletter.form.success' | t }}", type: "success", origin: "newsletter" },
                          showAlertEvent = new CustomEvent("showAlert", {detail: alertAttributes});
                  window.dispatchEvent(showAlertEvent);
                });
              </script>
            {%- endif -%}
              <fieldset>
                <legend>{{ 'newsletter.title_html' | t }}</legend>
                <p>
                  <label for="email-{{ section.id }}">{{ 'newsletter.subtitle_html' | t }}</label>
                  <input type="email" name="contact[email]" id="email-{{ section.id }}" aria-label="{{ 'newsletter.form.email_placeholder' | t }}" placeholder="{{ 'newsletter.form.email_placeholder' | t }}" required>
                  <button type="submit" class="overlay-{{ button_color }} mobile-only{% if button_style == 'inv' %} inv{% endif %}">{{ 'newsletter.form.submit_button' | t }}&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i></button>
                </p>
                {%- if block.settings.enable_newsletter_terms_checkbox and block.settings.newsletter_terms_text != empty -%}
                  <p class="check size-12">
                    <input type="checkbox" id="newsletter_check-{{ section.id }}" name="newsletter_check-{{ section.id }}" required>
                    <label for="newsletter_check-{{ section.id }}">{{ block.settings.newsletter_terms_text | remove: '<p>' | remove: '</p>' }}</label>
                  </p>
                {%- endif -%}
                <p class="submit mobile-hide"><button type="submit" class="overlay-{{ button_color }} {% if button_style == 'inv' %} inv{% endif %}">{{ 'newsletter.form.submit_button' | t }}&nbsp;<i aria-hidden="true" class="icon-chevron-right"></i></button></p>
              </fieldset>
            {%- endform -%}
          {%- endif -%}
        </div>
      {%- when 'menu' -%}
        {%- if split_menu -%}
          {%- liquid
            assign first_menu_items_limit = 5 | minus: section.blocks.size | abs
            if first_menu_items_limit == 4
              assign first_menu_items_limit = 6
            endif
          -%}
          {%- capture menu_blocks -%}
            {%- for link in linklists[block.settings.menu].links limit: first_menu_items_limit -%}
              <div class="{% if linklists[block.settings.menu].links.size > first_menu_items_limit %} mobile-hide{% endif %}{% if first_menu_items_limit == 6 %} w16{% endif %}" {{ block.shopify_attributes }}>
                <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}"><a href="{{ link.url }}">{{ link.title }}</a></{{ section.settings.title_size_blocks }}>
                {%- if link.links != blank -%}
                  <ul>
                    {%- for sub in link.links -%}
                      <li><a href="{{ sub.url }}" title="{{ sub.title }}">{{ sub.title }}</a>
                      {%- if sub.links != blank -%}
                        <ul>
                          {%- for subsub in sub.links -%}
                            <li><a href="{{ subsub.url }}" title="{{ subsub.title }}">{{ subsub.title }}</a></li>
                          {%- endfor -%}
                        </ul>
                      {%- endif -%}
                      </li>
                    {%- endfor -%}
                  </ul>
                {%- endif -%}
              </div>
            {%- endfor -%}
          {%- endcapture -%}
          {{ menu_blocks }}
        {%- else -%}
          {%- for link in linklists[block.settings.menu].links -%}
            <div class="" {{ block.shopify_attributes }}>
              <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}"><a href="{{ link.url }}">{{ link.title }}</a></{{ section.settings.title_size_blocks }}>
              {%- if link.links != blank -%}
                <ul>
                  {%- for sub in link.links -%}
                    <li><a href="{{ sub.url }}" title="{{ sub.title }}">{{ sub.title }}</a></li>
                    {%- if sub.links != blank -%}
                      <ul>
                        {%- for subsub in sub.links -%}
                          <li><a href="{{ subsub.url }}" title="{{ subsub.title }}">{{ subsub.title }}</a></li>
                        {%- endfor -%}
                      </ul>
                    {%- endif -%}
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            </div>
          {%- endfor -%}
        {%- endif -%}
    {%- endcase -%}
  {%- endfor -%}
</nav>

{%- if split_menu -%}
  {%- assign menu_block = section.blocks | where: 'type', 'menu' | first -%}
  {%- if menu_block and linklists[menu_block.settings.menu].links.size > first_menu_items_limit -%}
    {%- liquid
      assign number_of_items = linklists[menu_block.settings.menu].links.size | minus: first_menu_items_limit
      assign number = number_of_items | at_most: 6
      case number
        when 1
          assign width_class = 'w100'
        when 2
          assign width_class = 'w25'
        when 3
          assign width_class = 'w33'
        when 4
          assign width_class = 'w25'
        when 5
          assign width_class = 'w20'
        when 6
          assign width_class = 'w16'
      endcase
    -%}
  <nav class="{{ width_class }}">
    {{ menu_blocks | replace: 'mobile-hide', 'mobile-only' }}
    {%- for link in linklists[menu_block.settings.menu].links offset: first_menu_items_limit -%}
      <div>
        <{{ section.settings.title_size_blocks }} class="ff-{{ section.settings.title_font }}"><a href="{{ link.url }}">{{ link.title }}</a></{{ section.settings.title_size_blocks }}>
        {%- if link.links != blank -%}
          <ul>
            {%- for sub in link.links -%}
              <li><a href="{{ sub.url }}" title="{{ sub.title }}">{{ sub.title }}</a></li>
              {%- if sub.links != blank -%}
                <ul>
                  {%- for subsub in sub.links -%}
                    <li><a href="{{ subsub.url }}" title="{{ subsub.title }}">{{ subsub.title }}</a></li>
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            {%- endfor -%}
          </ul>
        {%- endif -%}
      </div>
      {%- assign modulo = forloop.index | modulo: 6 -%}
      {%- if modulo == 0 and forloop.index != number_of_items -%}
        {%- liquid
          assign number = number_of_items | minus: forloop.index | at_most: 6
          case number
            when 1
              assign width_class = 'w100'
            when 2
              assign width_class = 'w25'
            when 3
              assign width_class = 'w33'
            when 4
              assign width_class = 'w25'
            when 5
              assign width_class = 'w20'
            when 6
              assign width_class = 'w16'
          endcase
        -%}
        </nav><nav class="{{ width_class }}">
      {%- endif -%}
    {%- endfor -%}
    </nav>
  {%- endif -%}
{%- endif -%}


<div class="base-font">
  {%- if section.settings.bottom_img -%}
    {%- if section.settings.bottom_img_link != empty -%}<a href="{{ section.settings.bottom_img_link }}">{%- endif -%}
    <figure>
      <img
        src="{{- section.settings.bottom_img | image_url: width: section.settings.bottom_img_width -}}"
        srcset="{% render 'image-srcset', image: section.settings.bottom_img, max_width: section.settings.bottom_img_width %}"
        sizes="
          (min-width: 1100) {{ section.settings.bottom_img_width }}px
          (min-width: 760px) 0
          50px
        "
        width="{{ section.settings.bottom_img_width }}"
        height="36"
        alt="{{ section.settings.bottom_img.alt | default: 'Footer image' | escape }}"
        loading="lazy"
      >
    </figure>
    {%- if section.settings.bottom_img_link != empty -%}</a>{%- endif -%}
  {%- endif -%}
  <p>&copy; <span class="date">{{ 'now' | date: "%Y" }}</span> {{ shop.name }}, {{ powered_by_link }}</p>
  <ul class="l4dr">
    {%- liquid
      if section.settings.enable_country_selector and localization.available_countries.size > 1
        render 'language-country-selector', display: 'country', origin: 'footer'
      endif
      if section.settings.enable_language_selector and localization.available_languages.size > 1
        render 'language-country-selector', display: 'language', origin: 'footer'
      endif
    -%}
  </ul>
  {%- if shop.features.follow_on_shop? and section.settings.enable_follow_on_shop -%}
    <p class="follow-on-shop">
      {% comment %} TODO: enable theme-check once `login_button` is accepted as valid filter {% endcomment %}
      {% # theme-check-disable %}
      {{ shop | login_button: action: 'follow' }}
      {% # theme-check-enable %}
    </p>
  {%- endif -%}
  {%- if section.settings.show_payment_methods -%}
    <ul class="l4pm box">
      {%- for payment_method in shop.enabled_payment_types -%}
        <li>{{ payment_method | payment_type_svg_tag }}</li>
      {%- endfor -%}
    </ul>
  {%- endif -%}
</div>

{% style %}
  #shopify-section-{{ section.id }} {
  {% comment %} main {% endcomment %}
  --custom_footer_bg:               var(--{{ section.settings.footer_color }}_bg);
  --custom_footer_fg:               var(--{{ section.settings.footer_color }}_fg);
  --custom_footer_fg_hover:         var(--{{ section.settings.footer_color }}_btn_bg);
  {% if newsletter_block %}
    --custom_footer_link_bg:    var(--{{ section.settings.footer_color }}_{{ button_color }}_btn_bg);
    --custom_footer_link_dark:  var(--{{ section.settings.footer_color }}_{{ button_color }}_btn_bg_dark);
    --custom_footer_link_text:  var(--{{ section.settings.footer_color }}_{{ button_color }}_btn_fg);
  {% endif %}
  {% comment %} bottom {% endcomment %}
  --custom_footer_bg_bottom:        var(--{{ section.settings.bottom_color }}_bg);
  --custom_footer_fg_bottom:        var(--{{ section.settings.bottom_color }}_fg);
  --custom_footer_fg_bottom_hover:  var(--{{ section.settings.bottom_color }}_btn_bg);
  --custom_footer_bd_bottom:        var(--{{ section.settings.bottom_color }}_bd);
  }
  {% if customer_service_block %}
    @media only screen and (min-width: 47.5em) {
    #shopify-section-{{ section.id }} nav .m6cn figure {
    bottom: calc({{ customer_service_block.settings.customer_support_offset_bottom }}px - 27px);
    }
    }
    @media only screen and (min-width: 62.5em) {
    html[dir="ltr"] #shopify-section-{{ section.id }} nav .m6cn figure {
    right: calc({{ customer_service_block.settings.customer_support_offset_right }}px - 74px);
    }
    html[dir="rtl"] #shopify-section-{{ section.id }} nav .m6cn figure {
    left: calc({{ customer_service_block.settings.customer_support_offset_right }}px - 74px);
    }
    }
    @media only screen and (min-width: 1100px) and (max-width: 1200px) {
    html[dir="ltr"] #shopify-section-{{ section.id }} nav .m6cn figure {
    right: calc({{ customer_service_block.settings.customer_support_offset_right }}px - 55px);
    }
    html[dir="rtl"] #shopify-section-{{ section.id }} nav .m6cn figure {
    left: calc({{ customer_service_block.settings.customer_support_offset_right }}px - 55px);
    }
    }
    @media only screen and (max-width: 47.5em) {
    #shopify-section-{{ section.id }} nav .m6cn figure {
    bottom: calc({{ customer_service_block.settings.customer_support_offset_bottom_mobile }}px + 1px);
    }
    html[dir="ltr"] #shopify-section-{{ section.id }} nav .m6cn figure {
    right: calc({{ customer_service_block.settings.customer_support_offset_right_mobile }}px - 20px);
    }
    html[dir="rtl"] #shopify-section-{{ section.id }} nav .m6cn figure {
    left: calc({{ customer_service_block.settings.customer_support_offset_right_mobile }}px - 20px);
    }
    }
  {% endif %}
  @media only screen and (min-width: 47.5em) {
    #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_desktop }}px; }
  }
  @media only screen and (max-width: 47.5em) {
    #shopify-section-{{ section.id }} { margin-bottom: {{ section.settings.spacing_mobile }}px; }
  }

  /* Footer Logo Styles */
  .footer-logo-block {
    display: flex;
    align-items: flex-start;
    margin-bottom: 20px;
  }

  .footer-logo-block a {
    display: inline-block;
    text-decoration: none;
  }

  .footer-logo-image {
    max-width: 100%;
    height: auto;
    display: block;
  }

  .footer-logo-placeholder {
    border-radius: 4px;
    border: 1px solid #ddd;
  }

  @media only screen and (max-width: 47.5em) {
    .footer-logo-block {
      margin-bottom: 15px;
    }
  }
{% endstyle %}

{% schema %}
{
  "name": "t:static_sections.footer.name",
  "class": "shopify-section-footer",
  "tag": "footer",
  "settings": [
    {
      "type": "color_scheme",
      "id": "footer_color",
      "label": "t:static_sections.footer.settings.footer_color.label",
      "default": "scheme-1"
    },
    {
      "type": "select",
      "id": "title_font",
      "label": "t:global.typography.title_font.label",
      "options": [
        {
          "value": "primary",
          "label": "t:global.typography.title_font.primary.label"
        },
        {
          "value": "secondary",
          "label": "t:global.typography.title_font.secondary.label"
        }
      ],
      "default": "primary"
    },
    {
      "type": "select",
      "id": "title_size_blocks",
      "label": "t:global.typography.title_size.label",
      "options": [
        {
          "value": "h1",
          "label": "t:global.typography.title_size.h1.label"
        },
        {
          "value": "h2",
          "label": "t:global.typography.title_size.h2.label"
        },
        {
          "value": "h3",
          "label": "t:global.typography.title_size.h3.label"
        },
        {
          "value": "h4",
          "label": "t:global.typography.title_size.h4.label"
        },
        {
          "value": "h5",
          "label": "t:global.typography.title_size.h5.label"
        }
      ],
      "default": "h4"
    },
    {
      "type": "header",
      "content": "t:static_sections.footer.settings.bottom_bar.header"
    },
    {
      "type": "color_scheme",
      "id": "bottom_color",
      "label": "t:global.color_palette.label",
      "default": "scheme-6"
    },
    {
      "id": "bottom_img",
      "type": "image_picker",
      "label": "t:static_sections.footer.settings.bottom_bar.bottom_img.label",
      "info": "t:static_sections.footer.settings.bottom_bar.bottom_img.info"
    },
    {
      "id": "bottom_img_width",
      "type": "range",
      "label": "t:static_sections.footer.settings.bottom_bar.bottom_img_width.label",
      "min": 50,
      "max": 200,
      "step": 5,
      "unit": "px",
      "default": 125
    },
    {
      "id": "bottom_img_link",
      "type": "url",
      "label": "t:static_sections.footer.settings.bottom_bar.bottom_img_link.label"
    },
    {
      "id": "enable_country_selector",
      "type": "checkbox",
      "label": "t:static_sections.footer.settings.bottom_bar.enable_country_selector.label",
      "info": "t:static_sections.footer.settings.bottom_bar.enable_country_selector.info",
      "default": false
    },
    {
      "id": "enable_language_selector",
      "type": "checkbox",
      "label": "t:static_sections.footer.settings.bottom_bar.enable_language_selector.label",
      "info": "t:static_sections.footer.settings.bottom_bar.enable_language_selector.info",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "enable_follow_on_shop",
      "label": "t:static_sections.footer.settings.bottom_bar.enable_follow_on_shop.label",
      "info": "t:static_sections.footer.settings.bottom_bar.enable_follow_on_shop.info"
    },
    {
      "type": "header",
      "content": "t:static_sections.footer.settings.payment_methods.header"
    },
    {
      "type": "checkbox",
      "id": "show_payment_methods",
      "label": "t:static_sections.footer.settings.payment_methods.show_payment_methods.label",
      "default": true
    },
    {
      "type": "header",
      "content": "t:global.spacing.header"
    },
    {
      "id": "spacing_desktop",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 0
    },
    {
      "id": "spacing_mobile",
      "type": "range",
      "label": "t:global.spacing.spacing_mobile.label",
      "min": -250,
      "max": 250,
      "step": 5,
      "unit": "px",
      "default": 0
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "logo",
      "name": "Logo",
      "limit": 1,
      "settings": [
        {
          "type": "image_picker",
          "id": "logo_image",
          "label": "Logo Image"
        },
        {
          "type": "url",
          "id": "logo_link",
          "label": "Logo Link"
        },
        {
          "type": "range",
          "id": "logo_width",
          "label": "Logo Width",
          "min": 50,
          "max": 300,
          "step": 10,
          "unit": "px",
          "default": 120
        },
        {
          "type": "range",
          "id": "logo_height",
          "label": "Placeholder Height",
          "min": 30,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 60,
          "info": "Height for placeholder when no image is selected"
        },
        {
          "type": "range",
          "id": "logo_offset_x",
          "label": "Horizontal Position",
          "min": -100,
          "max": 100,
          "step": 5,
          "unit": "px",
          "default": 0,
          "info": "Move logo left (-) or right (+)"
        },
        {
          "type": "range",
          "id": "logo_offset_y",
          "label": "Vertical Position",
          "min": -50,
          "max": 50,
          "step": 5,
          "unit": "px",
          "default": 0,
          "info": "Move logo up (-) or down (+)"
        },
        {
          "type": "color",
          "id": "placeholder_color",
          "label": "Placeholder Background Color",
          "default": "#f0f0f0",
          "info": "Background color when no image is selected"
        }
      ]
    },
    {
      "type": "menu",
      "name": "t:static_sections.footer.blocks.menu.name",
      "limit": 1,
      "settings": [
        {
          "type": "link_list",
          "id": "menu",
          "label": "t:static_sections.footer.blocks.menu.settings.menu.label",
          "default": "footer"
        },
        {
          "type": "checkbox",
          "id": "split_menu",
          "label": "t:static_sections.footer.blocks.menu.settings.split_menu.label",
          "info": "t:static_sections.footer.blocks.menu.settings.split_menu.info"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:static_sections.footer.blocks.text.name",
      "settings": [
        {
          "id": "title",
          "type": "inline_richtext",
          "label": "t:global.typography.title.label",
          "info": "t:global.typography.title.info",
          "default": "Footer information"
        },
        {
          "type": "select",
          "id": "title_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ]
        },
        {
          "id": "text",
          "type": "richtext",
          "label": "t:static_sections.footer.blocks.text.settings.text.label",
          "default": "<p>Give your customers details about your online store.</p>"
        }
      ]
    },
    {
      "type": "html",
      "name": "t:static_sections.footer.blocks.html.name",
      "settings": [
        {
          "id": "html",
          "type": "html",
          "label": "t:static_sections.footer.blocks.html.settings.html.label"
        }
      ]
    },
    {
      "type": "customer_support",
      "name": "t:static_sections.footer.blocks.customer_support.name",
      "limit": 1,
      "settings": [
        {
          "id": "title",
          "type": "inline_richtext",
          "label": "t:global.typography.title.label",
          "default": "Customer service"
        },
        {
          "type": "select",
          "id": "title_underline_style",
          "label": "t:global.typography.title_underline_style.label",
          "options": [
            {
              "value": "none",
              "label": "t:global.typography.title_underline_style.none.label"
            },
            {
              "value": "secondary_font",
              "label": "t:global.typography.title_underline_style.secondary_font.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_accent",
              "label": "t:global.typography.title_underline_style.secondary_font_accent.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "secondary_font_gradient",
              "label": "t:global.typography.title_underline_style.secondary_font_gradient.label",
              "group": "t:global.typography.title_underline_style.group.fonts"
            },
            {
              "value": "accent",
              "label": "t:global.typography.title_underline_style.accent.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            },
            {
              "value": "gradient",
              "label": "t:global.typography.title_underline_style.gradient.label",
              "group": "t:global.typography.title_underline_style.group.colors"
            }
          ]
        },
        {
          "id": "customer_support_img",
          "type": "image_picker",
          "label": "t:static_sections.footer.blocks.customer_support.settings.customer_support_img.label",
          "info": "t:static_sections.footer.blocks.customer_support.settings.customer_support_img.info"
        },
        {
          "id": "customer_support_img_width",
          "type": "range",
          "label": "t:static_sections.footer.blocks.customer_support.settings.customer_support_img_width.label",
          "min": 50,
          "max": 320,
          "step": 5,
          "unit": "px",
          "default": 200
        },
        {
          "id": "customer_support_offset_right",
          "type": "range",
          "label": "t:static_sections.footer.blocks.customer_support.settings.customer_support_offset_right.label",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 0
        },
        {
          "id": "customer_support_offset_bottom",
          "type": "range",
          "label": "t:static_sections.footer.blocks.customer_support.settings.customer_support_offset_bottom.label",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 0
        },
        {
          "type": "richtext",
          "id": "extra_customer_support",
          "label": "t:static_sections.footer.blocks.customer_support.settings.extra_customer_support.label",
          "info": "t:static_sections.footer.blocks.customer_support.settings.extra_customer_support.info",
          "default": "<p>Give your customers more details about your online store.</p>"
        },
        {
          "type": "checkbox",
          "id": "show_phone_link",
          "label": "t:static_sections.footer.blocks.customer_support.settings.show_phone_link.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_mail_link",
          "label": "t:static_sections.footer.blocks.customer_support.settings.show_mail_link.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "show_whatsapp_link",
          "label": "t:static_sections.footer.blocks.customer_support.settings.show_whatsapp_link.label",
          "default": true
        },
        {
          "type": "header",
          "content": "t:static_sections.footer.blocks.customer_support.settings.mobile.header"
        },
        {
          "id": "customer_support_offset_right_mobile",
          "type": "range",
          "label": "t:static_sections.footer.blocks.customer_support.settings.mobile.customer_support_offset_right_mobile.label",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 0
        },
        {
          "id": "customer_support_offset_bottom_mobile",
          "type": "range",
          "label": "t:static_sections.footer.blocks.customer_support.settings.mobile.customer_support_offset_bottom_mobile.label",
          "min": 0,
          "max": 150,
          "step": 5,
          "unit": "px",
          "default": 0
        }
      ]
    },
    {
      "type": "socials_newsletter",
      "name": "t:static_sections.footer.blocks.socials_newsletter.name",
      "limit": 1,
      "settings": [
        {
          "id": "title",
          "type": "text",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.title.label",
          "default": "Follow us"
        },
        {
          "type": "checkbox",
          "id": "show_socials",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.show_socials.label",
          "default": true
        },
        {
          "type": "checkbox",
          "id": "enable_follow_on_shop",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.enable_follow_on_shop.label",
          "info": "t:static_sections.footer.blocks.socials_newsletter.settings.enable_follow_on_shop.info"
        },
        {
          "type": "header",
          "content": "t:static_sections.footer.blocks.socials_newsletter.settings.newsletter.header",
        },
        {
          "type": "checkbox",
          "id": "show_newsletter",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.show_newsletter.label",
          "default": true
        },
        {
          "type": "select",
          "id": "footer_button_style",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.footer_button_style.label",
          "options": [
            {
              "value": "primary plain",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "secondary plain",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "tertiary plain",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.plain"
            },
            {
              "value": "primary inv",
              "label": "t:global.button.button_style.primary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "secondary inv",
              "label": "t:global.button.button_style.secondary.label",
              "group": "t:global.button.button_style.group.inv"
            },
            {
              "value": "tertiary inv",
              "label": "t:global.button.button_style.tertiary.label",
              "group": "t:global.button.button_style.group.inv"
            }
          ],
          "default": "primary plain",
          "visible_if": "{{ block.settings.show_newsletter }}"
        },
        {
          "type": "header",
          "content": "t:static_sections.footer.blocks.socials_newsletter.settings.checkbox.header",
          "visible_if": "{{ block.settings.show_newsletter }}"
        },
        {
          "id": "enable_newsletter_terms_checkbox",
          "type": "checkbox",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.checkbox.enable_newsletter_terms_checkbox.label",
          "visible_if": "{{ block.settings.show_newsletter }}"
        },
        {
          "id": "newsletter_terms_text",
          "type": "richtext",
          "label": "t:static_sections.footer.blocks.socials_newsletter.settings.checkbox.newsletter_terms_text.label",
          "info": "t:static_sections.footer.blocks.socials_newsletter.settings.checkbox.newsletter_terms_text.info",
          "visible_if": "{{ block.settings.show_newsletter and block.settings.enable_newsletter_terms_checkbox }}"
        }
      ]
    }
  ]
}
{% endschema %}
