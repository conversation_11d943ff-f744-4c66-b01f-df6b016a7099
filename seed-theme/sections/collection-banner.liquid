{%- liquid
  assign show_banner = true
  if section.settings.image == blank and section.settings.title == blank and section.settings.description == blank
    assign show_banner = false
  endif
-%}

{%- if show_banner -%}

<section class="collection-banner-section">
  <!-- Desktop Version -->
  <div class="collection-banner-wrapper desktop-only">
    {%- if section.settings.image -%}
      <div class="collection-banner-image">
        <img
          src="{{ section.settings.image | image_url: width: 1920 }}"
          srcset="
            {{ section.settings.image | image_url: width: 375 }} 375w,
            {{ section.settings.image | image_url: width: 750 }} 750w,
            {{ section.settings.image | image_url: width: 1100 }} 1100w,
            {{ section.settings.image | image_url: width: 1500 }} 1500w,
            {{ section.settings.image | image_url: width: 1920 }} 1920w
          "
          sizes="100vw"
          alt="{{ section.settings.image.alt | default: section.settings.title | escape }}"
          width="1920"
          height="400"
          loading="lazy"
        >
      </div>
    {%- else -%}
      <div class="collection-banner-placeholder"></div>
    {%- endif -%}
    
    <div class="collection-banner-content">
      <div class="collection-banner-text">
        {%- if section.settings.show_breadcrumbs -%}
          <nav class="collection-breadcrumbs" aria-label="breadcrumb">
            <a href="{{ routes.root_url }}">{{ 'general.breadcrumbs.home' | t }}</a>
            <span>/</span>
            <a href="{{ routes.collections_url }}">{{ 'general.breadcrumbs.collections' | t }}</a>
            <span>/</span>
            <span>{{ collection.title | default: 'Collection' }}</span>
          </nav>
        {%- endif -%}
        
        {%- if section.settings.title != blank -%}
          <h1 class="collection-banner-title">{{ section.settings.title }}</h1>
        {%- endif -%}
        
        {%- if section.settings.description != blank -%}
          <div class="collection-banner-description">{{ section.settings.description }}</div>
        {%- endif -%}
      </div>
    </div>
    
    {%- if section.blocks.size > 0 -%}
      <div class="collection-features-card">
        <div class="collection-features-grid">
          {%- for block in section.blocks -%}
            {%- case block.type -%}
              {%- when 'feature' -%}
                <div class="collection-feature-item" {{ block.shopify_attributes }}>
                  <div class="collection-feature-icon">
                    {%- if block.settings.icon -%}
                      <img src="{{ block.settings.icon | image_url: width: 70 }}" alt="{{ block.settings.title | escape }}" width="70" height="70">
                    {%- elsif block.settings.custom_svg != blank -%}
                      <div class="custom-svg-icon">{{ block.settings.custom_svg }}</div>
                    {%- elsif block.settings.svg_icon != blank -%}
                      {%- case block.settings.svg_icon -%}
                        {%- when 'money-back' -%}
                          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="20" fill="#E1F2FA"/>
                            <path d="M20 10C14.48 10 10 14.48 10 20C10 25.52 14.48 30 20 30C25.52 30 30 25.52 30 20C30 14.48 25.52 10 20 10ZM18 25L13 20L14.41 18.59L18 22.17L25.59 14.58L27 16L18 25Z" fill="#0066CC"/>
                          </svg>
                        {%- when 'happy-feet' -%}
                          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="20" fill="#E1F2FA"/>
                            <path d="M20 10C14.48 10 10 14.48 10 20C10 25.52 14.48 30 20 30C25.52 30 30 25.52 30 20C30 14.48 25.52 10 20 10ZM15.5 18C16.33 18 17 17.33 17 16.5C17 15.67 16.33 15 15.5 15C14.67 15 14 15.67 14 16.5C14 17.33 14.67 18 15.5 18ZM24.5 18C25.33 18 26 17.33 26 16.5C26 15.67 25.33 15 24.5 15C23.67 15 23 15.67 23 16.5C23 17.33 23.67 18 24.5 18ZM20 26C17.24 26 14.89 24.24 14.26 21.78L15.74 21.22C16.2 22.95 17.97 24 20 24C22.03 24 23.8 22.95 24.26 21.22L25.74 21.78C25.11 24.24 22.76 26 20 26Z" fill="#0066CC"/>
                          </svg>
                        {%- when 'fsa-hsa' -%}
                          <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="20" cy="20" r="20" fill="#E1F2FA"/>
                            <path d="M20 10C14.48 10 10 14.48 10 20C10 25.52 14.48 30 20 30C25.52 30 30 25.52 30 20C30 14.48 25.52 10 20 10ZM22 25H18V23H22V25ZM22 21H18V15H22V21Z" fill="#0066CC"/>
                          </svg>
                      {%- endcase -%}
                    {%- else -%}
                      <div class="collection-feature-icon-placeholder"></div>
                    {%- endif -%}
                  </div>
                  {%- if block.settings.title != blank -%}
                    <h3 class="collection-feature-title">{{ block.settings.title }}</h3>
                  {%- endif -%}
                  {%- if block.settings.description != blank -%}
                    <p class="collection-feature-description">{{ block.settings.description }}</p>
                  {%- endif -%}
                </div>
            {%- endcase -%}
          {%- endfor -%}
        </div>
      </div>
    {%- endif -%}
  </div>

  <!-- Mobile Version -->
  <div class="collection-banner-mobile mobile-only">
    {%- if section.settings.mobile_image -%}
      <div class="collection-banner-mobile-image">
        <img
          src="{{ section.settings.mobile_image | image_url: width: 390 }}"
          srcset="
            {{ section.settings.mobile_image | image_url: width: 390 }} 390w,
            {{ section.settings.mobile_image | image_url: width: 780 }} 780w
          "
          sizes="100vw"
          alt="{{ section.settings.mobile_image.alt | default: section.settings.mobile_title | escape }}"
          width="390"
          height="500"
          loading="lazy"
        >
      </div>
    {%- else -%}
      <div class="collection-banner-mobile-placeholder"></div>
    {%- endif -%}

    <div class="collection-banner-mobile-card">
      {%- if section.settings.show_breadcrumbs -%}
        <nav class="collection-breadcrumbs mobile-breadcrumbs" aria-label="breadcrumb">
          <a href="{{ routes.root_url }}">Home</a>
          <span>/</span>
          <a href="{{ routes.collections_url }}">Shop</a>
          <span>/</span>
          <span>{{ collection.title | default: 'Upsteps For Everyday' }}</span>
        </nav>
      {%- endif -%}

      {%- if section.settings.mobile_title != blank -%}
        <h1 class="collection-banner-mobile-title">{{ section.settings.mobile_title }}</h1>
      {%- endif -%}

      {%- if section.settings.mobile_description != blank -%}
        <div class="collection-banner-mobile-description">{{ section.settings.mobile_description }}</div>
      {%- endif -%}
    </div>
  </div>
</section>

<style>
.collection-banner-section {
  position: relative;
  width: 100%;
  min-height: 400px;
  padding-bottom: 120px;
  overflow: visible;
}

/* Desktop/Mobile visibility */
.desktop-only {
  display: block;
}

.mobile-only {
  display: none;
}

.collection-banner-wrapper {
  position: relative;
  width: 100vw;
  height: 440px;
  margin-left: calc(-50vw + 50%);
}

.collection-banner-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400px;
  z-index: 1;
}

.collection-banner-image img {
  width: 100%;
  height: 400px;
  object-fit: cover;
  object-position: center;
}

.collection-banner-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 400px;
  background: #E1F2FA;
  z-index: 1;
}

.collection-banner-content {
  position: relative;
  z-index: 2;
  padding: 60px 20px 120px;
  max-width: 1200px;
  margin: 0 auto;
}

.collection-banner-text {
  max-width: 500px;
}

.collection-breadcrumbs {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgba(74, 74, 74, 0.71);
  margin-bottom: 20px;
  display: inline-block;
}

.collection-breadcrumbs a {
  color: rgba(74, 74, 74, 0.71);
  text-decoration: none;
}

.collection-breadcrumbs a:hover {
  text-decoration: underline;
}

.collection-breadcrumbs span {
  margin: 0 4px;
}

.collection-banner-title {
  font-family: 'Merriweather', serif;
  font-size: 30px;
  font-weight: 400;
  line-height: 42px;
  letter-spacing: 0.12px;
  color: rgb(74, 74, 74);
  margin: 20px 0 10px 0;
  max-width: 345px;
}

.collection-banner-description {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-top: 20px;
  max-width: 345px;
}

.collection-features-card {
  position: absolute;
  bottom: -83px;
  left: 50%;
  transform: translateX(-50%);
  background: rgb(255, 255, 255);
  border-radius: 20px;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 0px 24px 0px;
  padding: 15px;
  max-width: 1180px;
  width: calc(100% - 40px);
  z-index: 3;
}

.collection-features-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
}

.collection-feature-item {
  flex: 1;
  min-width: 200px;
  max-width: 300px;
  text-align: center;
  position: relative;
}

.collection-feature-icon {
  position: relative;
  margin: 0 auto 0px;
  width: 70px;
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collection-feature-icon img,
.collection-feature-svg {
  width: 70px;
  height: 70px;
  object-fit: contain;
}

.collection-feature-icon-placeholder {
  width: 40px;
  height: 40px;
  background: #E1F2FA;
  border-radius: 50%;
  margin: 15px auto;
}

.collection-feature-icon img {
  width: 40px;
  height: 40px;
  object-fit: contain;
  margin: 15px auto;
  display: block;
}

.custom-svg-icon {
  margin: 15px auto;
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-svg-icon svg {
  max-width: 70px;
  max-height: 70px;
  width: auto;
  height: auto;
}

.collection-feature-icon svg {
  display: block;
}

/* Mobile Banner Styles */
.collection-banner-mobile {
  position: relative;
  width: 100%;
  height: 350px;
  overflow: visible;
  margin-bottom: 80px;
}

.collection-banner-mobile-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.collection-banner-mobile-image img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  object-position: center;
}

/* Переопределяем глобальное правило темы для нашего баннера */
.collection-banner-mobile .collection-banner-mobile-image img {
  height: 100% !important;
}

.collection-banner-mobile-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #E1F2FA;
  z-index: 1;
}

.collection-banner-mobile-card {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 350px;
  max-width: calc(100% - 40px);
  background: white;
  border-radius: 4px;
  box-shadow: rgba(0, 0, 0, 0.25) 0px 0px 24px 0px;
  padding: 10px 15px;
  text-align: center;
  z-index: 2;
  height: auto;
  min-height: 160px;
}

.mobile-breadcrumbs {
  font-size: 14px;
  color: rgba(74, 74, 74, 0.71);
  margin-bottom: 10px;
}

.mobile-breadcrumbs a {
  color: rgba(74, 74, 74, 0.71);
  text-decoration: none;
}

.collection-banner-mobile-title {
  font-family: Merriweather, serif;
  font-size: 20px;
  font-weight: 400;
  line-height: 28px;
  letter-spacing: 0.12px;
  color: rgb(74, 74, 74);
  margin: 0 0 10px 0;
  text-align: center;
}

.collection-banner-mobile-description {
  font-size: 15px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  text-align: center;
}

.collection-banner-mobile-description p{
  margin-bottom: 0px;
}

.collection-feature-title {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgba(74, 74, 74, 0.8);
  margin: 0 0 5px 0;
  font-weight: 600;
}

.collection-feature-description {
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  line-height: 19.2px;
  color: rgba(74, 74, 74, 0.6);
  margin: 0;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .collection-banner-section {
    padding-bottom: 100px;
  }

  .collection-banner-content {
    padding: 40px 20px 80px;
  }

  .collection-banner-title {
    font-size: 24px;
    line-height: 32px;
  }

  .collection-features-card {
    bottom: -60px;
    width: calc(100% - 20px);
    padding: 10px;
  }

  .collection-features-grid {
    flex-direction: column;
    gap: 20px;
  }

  .collection-feature-item {
    max-width: 100%;
  }
}

@media (max-width: 480px) {
  .collection-banner-section {
    padding-bottom: 80px;
  }

  .collection-banner-content {
    padding: 30px 15px 60px;
  }

  .collection-banner-title {
    font-size: 20px;
    line-height: 28px;
  }

  .collection-breadcrumbs {
    font-size: 14px;
  }

  .collection-features-card {
    bottom: -50px;
  }
}

/* Add margin to next section to account for floating card */
.collection-banner-section + * {
  margin-top: 100px;
}

@media (max-width: 768px) {
  .collection-banner-section + * {
    margin-top: 80px;
  }
}

@media (max-width: 480px) {
  .collection-banner-section + * {
    margin-top: 70px;
  }
}

/* Mobile responsive */
@media (max-width: 768px) {
  .desktop-only {
    display: none;
  }

  .mobile-only {
    display: block;
  }

  .collection-banner-section {
    padding-bottom: 0;
    min-height: auto;
  }

  .collection-banner-mobile-card {
    width: calc(100% - 40px);
    max-width: 350px;
  }
}
</style>
{%- endif -%}

{% schema %}
{
  "name": "Collection Banner",
  "settings": [
    {
      "type": "header",
      "content": "Banner Settings"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Background Image (Desktop)",
      "info": "Recommended size: 1920x400px"
    },
    {
      "type": "image_picker",
      "id": "mobile_image",
      "label": "Background Image (Mobile)",
      "info": "Recommended size: 390x500px or larger for full screen coverage"
    },
    {
      "type": "checkbox",
      "id": "show_breadcrumbs",
      "label": "Show Breadcrumbs",
      "default": true
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "Up to 70% OFF | BACK TO SCHOOL"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>60% less expensive than any other foot specialist with a 100% Satisfaction Guarantee - or your money back!</p>"
    },
    {
      "type": "header",
      "content": "Mobile Settings"
    },
    {
      "type": "text",
      "id": "mobile_title",
      "label": "Mobile Title",
      "default": "Up to 70% OFF | BACK TO SCHOOL"
    },
    {
      "type": "richtext",
      "id": "mobile_description",
      "label": "Mobile Description",
      "default": "<p>60% less expensive than any other foot specialist with a 100% Satisfaction Guarantee - or your money back!</p>"
    }
  ],
  "blocks": [
    {
      "type": "feature",
      "name": "Feature",
      "settings": [
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon Image",
          "info": "Recommended size: 70x70px. If both icon image and SVG icon are set, image will be used."
        },
        {
          "type": "select",
          "id": "svg_icon",
          "label": "SVG Icon",
          "options": [
            {
              "value": "",
              "label": "None"
            },
            {
              "value": "money-back",
              "label": "Money Back Guarantee"
            },
            {
              "value": "happy-feet",
              "label": "Happy Feet"
            },
            {
              "value": "fsa-hsa",
              "label": "FSA/HSA Eligible"
            }
          ],
          "default": ""
        },
        {
          "type": "html",
          "id": "custom_svg",
          "label": "Custom SVG Code",
          "info": "Paste your SVG code here. This will override the SVG icon selection above if provided."
        },
        {
          "type": "text",
          "id": "title",
          "label": "Title",
          "default": "180-day money-back guarantee*"
        },
        {
          "type": "textarea",
          "id": "description",
          "label": "Description",
          "default": "We're so sure they'll work, we're giving you 6 months to try them - no strings attached."
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Collection Banner",
      "blocks": [
        {
          "type": "feature",
          "settings": {
            "svg_icon": "",
            "title": "180-day money-back guarantee*",
            "description": "We're so sure they'll work, we're giving you 6 months to try them - no strings attached."
          }
        },
        {
          "type": "feature",
          "settings": {
            "svg_icon": "",
            "title": "Over 200k happy feet",
            "description": "That's 1 billion daily steps walked painlessly and comfortably thanks to Upsteps!"
          }
        },
        {
          "type": "feature",
          "settings": {
            "svg_icon": "",
            "title": "FSA/HSA eligible",
            "description": "Use your FSA and HSA dollars before they expire, you can use them to buy custom orthotics at Upstep."
          }
        }
      ]
    }
  ]
}
{% endschema %}
