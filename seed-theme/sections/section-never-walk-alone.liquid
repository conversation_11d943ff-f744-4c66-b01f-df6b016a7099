{{ 'about-us-styles.css' | asset_url | stylesheet_tag }}

<div class="never-walk-alone-section" 
     style="padding-top: {{ section.settings.spacing_desktop }}px; padding-bottom: {{ section.settings.spacing_desktop }}px;">
  <div class="never-walk-alone-content">
    <div class="never-walk-alone-layout">
      <!-- Left Column - Image -->
      <div class="never-walk-alone-image-column">
        {% if section.settings.image != blank %}
          <div class="never-walk-alone-image-wrapper">
            <img src="{{ section.settings.image | image_url: width: 600 }}" 
                 alt="{{ section.settings.image.alt | default: section.settings.title }}"
                 width="471"
                 height="336"
                 loading="lazy">
          </div>
        {% else %}
          <div class="never-walk-alone-image-placeholder">
            <div class="placeholder-content">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="#cccccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 17L12 22L22 17" stroke="#cccccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                <path d="M2 12L12 17L22 12" stroke="#cccccc" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
              <p>Add an image</p>
            </div>
          </div>
        {% endif %}
      </div>

      <!-- Right Column - Text Content -->
      <div class="never-walk-alone-text-column">
        {% if section.settings.title != blank %}
          <h2 class="never-walk-alone-title">{{ section.settings.title }}</h2>
        {% endif %}

        {% if section.settings.subtitle != blank %}
          <div class="never-walk-alone-subtitle">
            {{ section.settings.subtitle }}
          </div>
        {% endif %}

        {% if section.settings.description != blank %}
          <div class="never-walk-alone-description">
            {{ section.settings.description }}
          </div>
        {% endif %}

        {% if section.settings.additional_text != blank %}
          <div class="never-walk-alone-additional">
            {{ section.settings.additional_text }}
          </div>
        {% endif %}
      </div>
    </div>
  </div>
</div>

<style>
/* Never Walk Alone Section */
.never-walk-alone-section {
  background-color: #fff;
  position: relative;
  z-index: 1;
  width: 100vw;
  margin-left: calc(-50vw + 50%);
}

.never-walk-alone-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 20px;
}

.never-walk-alone-layout {
  display: flex;
  align-items: flex-start;
  gap: 100px;
  max-width: 1200px;
  margin: 0 auto;
}

/* Left Column - Image */
.never-walk-alone-image-column {
  flex: 1;
  max-width: 557px;
}

.never-walk-alone-image-wrapper {
  width: 100%;
  height: 534px;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  padding-top: 65px;
}

.never-walk-alone-image-wrapper img {
  width: 90%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  transition: opacity 0.3s ease;
  vertical-align: middle;
}

.never-walk-alone-image-placeholder {
  width: 100%;
  height: 534px;
  background-color: #f5f5f5;
  border: 2px dashed #cccccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.placeholder-content {
  text-align: center;
  color: #999999;
}

.placeholder-content p {
  margin: 10px 0 0 0;
  font-size: 14px;
  font-weight: 500;
}

/* Right Column - Text */
.never-walk-alone-text-column {
  flex: 1;
  padding-left: 10px;
  max-width: 485px;
  position: relative;
}

.never-walk-alone-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 36px;
  font-weight: 700;
  height: auto;
  letter-spacing: 0.12px;
  line-height: 50.4px;
  margin-bottom: 20px;
  margin-left: 0px;
  margin-right: 0px;
  margin-top: 0px;
  text-align: left;
  width: 485px;
}

.never-walk-alone-subtitle {
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-bottom: 20px;
}

.never-walk-alone-description {
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-bottom: 20px;
}

.never-walk-alone-additional {
  font-family: Lato, sans-serif;
  font-size: 16px;
  line-height: 22.4px;
  color: rgb(74, 74, 74);
  margin-bottom: 0;
}

.never-walk-alone-subtitle p,
.never-walk-alone-description p,
.never-walk-alone-additional p {
  margin: 0 0 16px 0;
}

.never-walk-alone-subtitle p:last-child,
.never-walk-alone-description p:last-child,
.never-walk-alone-additional p:last-child {
  margin-bottom: 0;
}

/* Mobile Responsive */
@media only screen and (max-width: 768px) {
  .never-walk-alone-section {
    padding-top: 30px !important;
    padding-bottom: 60px !important;
  }

  .never-walk-alone-layout {
    flex-direction: column;
    gap: 0px;
  }

  .never-walk-alone-image-column {
    max-width: 100%;
  }

  .never-walk-alone-image-wrapper,
  .never-walk-alone-image-placeholder {
    height: 300px;
    padding-top: 0;
  }

  .never-walk-alone-text-column {
    padding-left: 0;
    max-width: 100%;
    padding-top: 10px;
  }

  .never-walk-alone-title {
    font-size: 28px;
    line-height: 39.2px;
    margin-bottom: 16px;
    width: 100%;
  }

  .never-walk-alone-subtitle,
  .never-walk-alone-description,
  .never-walk-alone-additional {
    font-size: 14px;
    line-height: 20px;
  }
}

@media only screen and (max-width: 480px) {
  .never-walk-alone-content {
    padding: 0 15px;
  }

  .never-walk-alone-title {
    font-size: 24px;
    line-height: 33.6px;
  }

  .never-walk-alone-image-wrapper,
  .never-walk-alone-image-placeholder {
    height: 250px;
  }
}
</style>

{% schema %}
{
  "name": "Never Walk Alone",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Section Title",
      "default": "You will never walk alone"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Section Image"
    },
    {
      "type": "richtext",
      "id": "subtitle",
      "label": "Subtitle",
      "default": "<p>We don't just sell custom orthotics.</p>"
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "Description",
      "default": "<p>We're with you until you lose your foot pain. If your custom orthotics don't feel right, we'll remake them until they fit perfectly and if you're not in love with them within 180 days, we'll refund your order - no questions asked!</p><p>*180-day money-back guarantee only applies to custom orthotics.</p>"
    },
    {
      "type": "richtext",
      "id": "additional_text",
      "label": "Additional Text",
      "default": "<p>Once you purchase Upstep orthotics, you're a part of our community.</p><p>You'll start to receive tailored exercise regimens that are designed to build strength and alleviate foot pain.</p><p>Our team of podiatrists will not only diagnose your foot pain but are also always available to answer any questions you have - at no additional cost.</p>"
    },
    {
      "type": "range",
      "id": "spacing_desktop",
      "min": 0,
      "max": 200,
      "step": 10,
      "unit": "px",
      "label": "Desktop Spacing",
      "default": 80
    },
    {
      "type": "range",
      "id": "spacing_mobile",
      "min": 0,
      "max": 150,
      "step": 10,
      "unit": "px",
      "label": "Mobile Spacing",
      "default": 60
    }
  ]
}
{% endschema %}
