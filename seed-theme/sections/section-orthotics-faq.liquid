{% comment %}
  Orthotics FAQ Section with Accordion
  Displays frequently asked questions with expandable answers
{% endcomment %}

<div class="orthotics-faq-section">
  <div class="orthotics-faq-container">

    {% if section.settings.main_title != blank %}
      <div class="orthotics-faq-main-title">
        {{ section.settings.main_title }}
      </div>
    {% endif %}

    {% for block in section.blocks %}
      {% if block.type == 'faq_group' %}
        <div class="orthotics-faq-group" {{ block.shopify_attributes }}>
          
          {% if block.settings.group_title != blank %}
            <div class="orthotics-faq-group-title">
              {{ block.settings.group_title }}
            </div>
          {% endif %}

          <div class="orthotics-faq-accordion">
            {% for faq_block in section.blocks %}
              {% if faq_block.type == 'faq_item' and faq_block.settings.group_id == block.settings.group_id %}
                <div class="orthotics-faq-item" data-faq-item {{ faq_block.shopify_attributes }}>
                  
                  <div class="orthotics-faq-question" data-faq-trigger>
                    <span class="orthotics-faq-question-text">{{ faq_block.settings.question }}</span>
                    <div class="orthotics-faq-arrow">
                      <svg xmlns="http://www.w3.org/2000/svg" width="14" height="9" viewBox="0 0 14 9" fill="none">
                        <path d="M7.59961 0.929395L13.1996 6.6294C13.5996 7.0294 13.5996 7.6294 13.1996 8.0294C12.7996 8.4294 12.1996 8.4294 11.7996 8.0294L6.79961 3.1294L1.79961 8.0294C1.39961 8.4294 0.799609 8.4294 0.399609 8.0294C0.199609 7.8294 0.0996091 7.6294 0.0996091 7.3294C0.0996091 7.0294 0.199609 6.8294 0.399609 6.6294L5.99961 0.929395C6.49961 0.529395 7.09961 0.529395 7.59961 0.929395C7.49961 0.929395 7.49961 0.929395 7.59961 0.929395Z" fill="#7B8D9D"/>
                      </svg>
                    </div>
                  </div>

                  <div class="orthotics-faq-answer" data-faq-content>
                    <div class="orthotics-faq-answer-inner">
                      {{ faq_block.settings.answer }}
                    </div>
                  </div>

                </div>
              {% endif %}
            {% endfor %}
          </div>

        </div>
      {% endif %}
    {% endfor %}

  </div>
</div>

<style>
  .orthotics-faq-section {
    background: #FFFFFF;
    padding: 60px 0;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    position: relative;
  }

  .orthotics-faq-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
  }

  .orthotics-faq-main-title {
    color: #4A4A4A !important;
    text-align: center !important;
    font-family: Merriweather, serif !important;
    font-size: 36px !important;
    font-style: normal !important;
    font-weight: 700 !important;
    line-height: 55px !important;
    letter-spacing: 0.2px !important;
    margin: 0 0 50px 0 !important;
  }

  .orthotics-faq-main-title *,
  .orthotics-faq-main-title p,
  .orthotics-faq-main-title h1,
  .orthotics-faq-main-title h2,
  .orthotics-faq-main-title h3,
  .orthotics-faq-main-title h4,
  .orthotics-faq-main-title h5,
  .orthotics-faq-main-title h6 {
    color: #4A4A4A !important;
    text-align: center !important;
    font-family: Merriweather, serif !important;
    font-size: 36px !important;
    font-style: normal !important;
    font-weight: 700 !important;
    line-height: 55px !important;
    letter-spacing: 0.2px !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* ID селектор для максимальной специфичности */
  #shopify-section-{{ section.id }} .orthotics-faq-main-title,
  #shopify-section-{{ section.id }} .orthotics-faq-main-title * {
    color: #4A4A4A !important;
    text-align: center !important;
    font-family: Merriweather, serif !important;
    font-size: 36px !important;
    font-weight: 700 !important;
    line-height: 55px !important;
  }

  .orthotics-faq-group {
    margin-bottom: 40px;
  }

  .orthotics-faq-group-title {
    color: #4a4a4a !important;
    font-family: Lato, sans-serif !important;
    font-size: 18px !important;
    font-style: normal !important;
    font-weight: 700 !important;
    line-height: 22px !important;
    letter-spacing: 0.12px !important;
    margin: 0 0 24px 0 !important;
  }

  .orthotics-faq-group-title *,
  .orthotics-faq-group-title p,
  .orthotics-faq-group-title h1,
  .orthotics-faq-group-title h2,
  .orthotics-faq-group-title h3,
  .orthotics-faq-group-title h4,
  .orthotics-faq-group-title h5,
  .orthotics-faq-group-title h6 {
    color: #4a4a4a !important;
    font-family: Lato, sans-serif !important;
    font-size: 18px !important;
    font-style: normal !important;
    font-weight: 700 !important;
    line-height: 22px !important;
    letter-spacing: 0.12px !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* ID селектор для заголовков групп */
  #shopify-section-{{ section.id }} .orthotics-faq-group-title,
  #shopify-section-{{ section.id }} .orthotics-faq-group-title * {
    color: #4a4a4a !important;
    font-family: Lato, sans-serif !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    line-height: 22px !important;
  }

  .orthotics-faq-accordion {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .orthotics-faq-item {
    display: flex;
    padding: 30px 16px;
    flex-direction: column;
    align-items: flex-start;
    gap: 0;
    border-radius: 9px;
    border: 1px solid rgba(213, 217, 220, 0.32);
    background: #FFF;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.15);
    overflow: hidden;
    cursor: pointer;
    transition: box-shadow 0.2s ease;
  }

  .orthotics-faq-item:hover {
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
  }

  .orthotics-faq-question {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    padding: 0;
    background: none;
    border: none;
    pointer-events: none;
  }

  .orthotics-faq-question-text {
    color: #475562;
    font-family: Lato, sans-serif;
    font-size: 17px;
    font-style: normal;
    font-weight: 700;
    line-height: 24px;
    letter-spacing: 0.12px;
    flex: 1;
    text-align: left;
  }

  .orthotics-faq-arrow {
    flex-shrink: 0;
    margin-left: 16px;
    margin-top: 2px;
    align-self: flex-start;
  }

  .orthotics-faq-arrow svg {
    display: block;
    transform: rotate(180deg);
    transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
  }

  .orthotics-faq-item.active .orthotics-faq-arrow svg {
    transform: rotate(0deg);
  }

  .orthotics-faq-answer {
    width: 100%;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: max-height;
  }

  .orthotics-faq-item.active .orthotics-faq-answer {
    max-height: 1000px;
  }

  /* Класс для мгновенного закрытия без анимации */
  .orthotics-faq-answer.instant-close {
    transition: none !important;
    max-height: 0 !important;
  }

  .orthotics-faq-answer-inner {
    color: #788999;
    font-family: Lato, sans-serif;
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    letter-spacing: 0.08px;
    padding-top: 20px;
  }

  .orthotics-faq-answer-inner p {
    margin: 0 0 16px 0;
  }

  .orthotics-faq-answer-inner p:last-child {
    margin-bottom: 0;
  }

  /* Mobile responsive */
  @media (max-width: 768px) {
    .orthotics-faq-section {
      padding: 40px 0;
    }

    .orthotics-faq-container {
      padding: 0 15px;
    }

    .orthotics-faq-main-title,
    .orthotics-faq-main-title * {
      font-size: 28px !important;
      line-height: 36px !important;
      margin-bottom: 32px !important;
    }

    .orthotics-faq-group {
      margin-bottom: 32px;
    }

    .orthotics-faq-group-title,
    .orthotics-faq-group-title * {
      font-size: 16px !important;
      line-height: 20px !important;
      margin-bottom: 20px !important;
    }

    .orthotics-faq-item {
      padding: 20px 16px;
    }

    .orthotics-faq-question-text {
      font-size: 16px;
      line-height: 22px;
    }

    .orthotics-faq-answer-inner {
      font-size: 15px;
      line-height: 24px;
      padding-top: 16px;
    }

    .orthotics-faq-item.active .orthotics-faq-answer {
      max-height: 800px;
    }
  }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const faqItems = document.querySelectorAll('.orthotics-faq-item[data-faq-item]');

  // Функция для очистки всех стилей и классов
  function resetItem(item) {
    const answer = item.querySelector('.orthotics-faq-answer');
    if (answer) {
      // Убираем все служебные классы
      answer.classList.remove('instant-close');
      // Очищаем inline стили
      answer.style.transition = '';
      answer.style.maxHeight = '';
    }
  }

  // Функция для мгновенного закрытия элемента
  function closeItemInstantly(item) {
    if (item.classList.contains('active')) {
      const answer = item.querySelector('.orthotics-faq-answer');
      if (answer) {
        // Добавляем класс для мгновенного закрытия
        answer.classList.add('instant-close');
        // Убираем active класс
        item.classList.remove('active');
        // Убираем instant-close класс через микротаск
        requestAnimationFrame(() => {
          answer.classList.remove('instant-close');
        });
      }
    }
  }

  // Функция для открытия элемента
  function openItem(item) {
    resetItem(item);
    item.classList.add('active');
  }

  // Функция для закрытия элемента
  function closeItem(item) {
    resetItem(item);
    item.classList.remove('active');
  }

  // Инициализация - очищаем все элементы
  faqItems.forEach(function(item) {
    resetItem(item);
  });

  faqItems.forEach(function(item) {
    // Делаем весь элемент кликабельным
    item.addEventListener('click', function(e) {
      e.preventDefault();

      const isActive = item.classList.contains('active');

      // Мгновенно закрываем все остальные элементы
      faqItems.forEach(function(otherItem) {
        if (otherItem !== item) {
          closeItemInstantly(otherItem);
        }
      });

      // Переключаем текущий элемент
      if (isActive) {
        closeItem(item);
      } else {
        openItem(item);
      }
    });
  });
});
</script>

{% schema %}
{
  "name": "Orthotics FAQ",
  "settings": [
    {
      "type": "richtext",
      "id": "main_title",
      "label": "Main Title",
      "default": "<p>Frequently Asked Questions</p>"
    }
  ],
  "blocks": [
    {
      "type": "faq_group",
      "name": "FAQ Group",
      "settings": [
        {
          "type": "text",
          "id": "group_id",
          "label": "Group ID",
          "info": "Unique identifier for this group (e.g., 'popular', 'returning')"
        },
        {
          "type": "text",
          "id": "group_title",
          "label": "Group Title",
          "default": "Most Popular"
        }
      ]
    },
    {
      "type": "faq_item",
      "name": "FAQ Item",
      "settings": [
        {
          "type": "text",
          "id": "group_id",
          "label": "Group ID",
          "info": "Must match the Group ID above (e.g., 'popular', 'returning')"
        },
        {
          "type": "text",
          "id": "question",
          "label": "Question",
          "default": "Does insurance cover your insoles?"
        },
        {
          "type": "richtext",
          "id": "answer",
          "label": "Answer",
          "default": "<p>We accept FSA/HSA cards and all major credit cards like ApplePay, GooglePay, and Klarna. Unfortunately, not many insurance companies cover custom-made insoles, so we don't currently work with any insurance companies. We try to make payments as convenient as possible and accept all major credit cards, such as ApplePay, GooglePay, Klarna, and FSA/HSA cards.</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Orthotics FAQ",
      "blocks": [
        {
          "type": "faq_group",
          "settings": {
            "group_id": "popular",
            "group_title": "Most Popular"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "group_id": "popular",
            "question": "Does insurance cover your insoles?",
            "answer": "<p>We accept FSA/HSA cards and all major credit cards like ApplePay, GooglePay, and Klarna. Unfortunately, not many insurance companies cover custom-made insoles, so we don't currently work with any insurance companies. We try to make payments as convenient as possible and accept all major credit cards, such as ApplePay, GooglePay, Klarna, and FSA/HSA cards.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "group_id": "popular",
            "question": "Are they truly custom-made insoles?",
            "answer": "<p>Yes, every pair of Upstep orthotics is completely custom-made based on your individual foot scan and specific needs assessment.</p>"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "group_id": "popular",
            "question": "Will Upstep help my condition?",
            "answer": "<p>Our custom orthotics are designed to address a wide range of foot conditions and provide personalized support for your specific needs.</p>"
          }
        },
        {
          "type": "faq_group",
          "settings": {
            "group_id": "returning",
            "group_title": "Returning Customers"
          }
        },
        {
          "type": "faq_item",
          "settings": {
            "group_id": "returning",
            "question": "Do you keep my information for future orders?",
            "answer": "<p>Yes, we securely store your foot scan and preferences to make future orders quick and easy.</p>"
          }
        }
      ]
    }
  ]
}
{% endschema %}
