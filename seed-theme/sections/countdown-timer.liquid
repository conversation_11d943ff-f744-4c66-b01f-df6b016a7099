{%- liquid
  assign section_id = 'countdown-timer-' | append: section.id
  assign end_date = section.settings.end_date
  assign end_time = section.settings.end_time | default: '23:59'
  assign timezone_offset = section.settings.timezone_offset | default: 0
  
  if end_date != blank
    assign full_end_datetime = end_date | append: 'T' | append: end_time | append: ':00'
  endif
-%}

{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}

<style>
  #{{ section_id }} {
    background: transparent;
    padding: {{ section.settings.padding_top }}px 0 {{ section.settings.padding_bottom }}px;
    text-align: center;
  }
  
  #{{ section_id }} .countdown-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  #{{ section_id }} .countdown-text {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }
  
  #{{ section_id }} .flash-sale {
    color: {{ section.settings.text_color }};
    font-family: {{ section.settings.font_family }}, sans-serif;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.37px;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .main-offer {
    color: {{ section.settings.text_color }};
    font-family: {{ section.settings.font_family }}, sans-serif;
    font-size: 22px;
    font-weight: 900;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .guarantee {
    color: {{ section.settings.text_color }};
    font-family: {{ section.settings.font_family }}, sans-serif;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.37px;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .countdown-timer {
    display: flex;
    gap: 3px;
    align-items: center;
  }
  
  #{{ section_id }} .time-unit {
    background: {{ section.settings.timer_bg_color }};
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 4px 8px 0px, rgba(0, 0, 0, 0.06) 0px 0px 2px 0px, rgba(0, 0, 0, 0.04) 0px 0px 1px 0px;
    width: 56px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 10px;
  }
  
  #{{ section_id }} .time-number {
    color: {{ section.settings.timer_text_color }};
    font-family: {{ section.settings.font_family }}, sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
  }
  
  #{{ section_id }} .time-label {
    position: absolute;
    bottom: 9px;
    left: 50%;
    transform: translateX(-50%);
    color: {{ section.settings.timer_text_color }};
    font-family: {{ section.settings.font_family }}, sans-serif;
    font-size: 8px;
    letter-spacing: 0.1px;
    line-height: 1.3;
    text-transform: uppercase;
    white-space: nowrap;
  }
  
  #{{ section_id }}.countdown-ended {
    display: none;
  }

  #{{ section_id }} .countdown-timer.no-animations .time-unit {
    transition: none;
  }

  #{{ section_id }} .countdown-timer.no-animations .time-unit:hover {
    transform: none;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 4px 8px 0px, rgba(0, 0, 0, 0.06) 0px 0px 2px 0px, rgba(0, 0, 0, 0.04) 0px 0px 1px 0px;
  }

  #{{ section_id }} .countdown-timer.no-animations .time-number {
    transition: none;
  }
  
  @media (max-width: 768px) {
    #{{ section_id }} .countdown-container {
      flex-direction: column;
      gap: 15px;
    }
    
    #{{ section_id }} .countdown-text {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
    
    #{{ section_id }} .flash-sale,
    #{{ section_id }} .main-offer,
    #{{ section_id }} .guarantee {
      font-size: 14px;
      white-space: normal;
    }
    
    #{{ section_id }} .main-offer {
      font-size: 18px;
    }
    
    #{{ section_id }} .time-unit {
      width: 48px;
      height: 47px;
      margin: 0 5px;
    }
    
    #{{ section_id }} .time-number {
      font-size: 18px;
    }
    
    #{{ section_id }} .time-label {
      font-size: 7px;
      bottom: 7px;
    }
  }
  
  @media (max-width: 480px) {
    #{{ section_id }} .countdown-timer {
      gap: 2px;
    }
    
    #{{ section_id }} .time-unit {
      width: 40px;
      height: 40px;
      margin: 0 3px;
    }
    
    #{{ section_id }} .time-number {
      font-size: 16px;
    }
    
    #{{ section_id }} .time-label {
      font-size: 6px;
      bottom: 5px;
    }
  }
</style>

<section id="{{ section_id }}" class="countdown-section">
  <div class="countdown-container">
    <div class="countdown-text">
      {%- if section.settings.flash_sale_text != blank -%}
        <span class="flash-sale">{{ section.settings.flash_sale_text }}</span>
      {%- endif -%}
      
      {%- if section.settings.main_offer_text != blank -%}
        <span class="main-offer">{{ section.settings.main_offer_text }}</span>
      {%- endif -%}
      
      {%- if section.settings.guarantee_text != blank -%}
        <span class="guarantee">{{ section.settings.guarantee_text }}</span>
      {%- endif -%}
    </div>
    
    {%- if end_date != blank -%}
      <div class="countdown-timer{% unless section.settings.enable_animations %} no-animations{% endunless %}" data-end-date="{{ full_end_datetime }}" data-timezone-offset="{{ timezone_offset }}" data-auto-hide="{{ section.settings.auto_hide_on_end }}" data-reset-type="{{ section.settings.reset_type }}" data-reset-time="{{ section.settings.reset_time }}">
        {%- if section.settings.show_days -%}
          <div class="time-unit">
            <span class="time-number" data-time="days">00</span>
            <span class="time-label">{{ section.settings.days_label | default: 'Days' }}</span>
          </div>
        {%- endif -%}
        {%- if section.settings.show_hours -%}
          <div class="time-unit">
            <span class="time-number" data-time="hours">00</span>
            <span class="time-label">{{ section.settings.hours_label | default: 'Hours' }}</span>
          </div>
        {%- endif -%}
        {%- if section.settings.show_minutes -%}
          <div class="time-unit">
            <span class="time-number" data-time="minutes">00</span>
            <span class="time-label">{{ section.settings.minutes_label | default: 'Min' }}</span>
          </div>
        {%- endif -%}
        {%- if section.settings.show_seconds -%}
          <div class="time-unit">
            <span class="time-number" data-time="seconds">00</span>
            <span class="time-label">{{ section.settings.seconds_label | default: 'Sec' }}</span>
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const countdownTimer = document.querySelector('#{{ section_id }} .countdown-timer');
    if (!countdownTimer) return;

    const endDate = countdownTimer.dataset.endDate;
    const timezoneOffset = parseInt(countdownTimer.dataset.timezoneOffset) || 0;
    const autoHide = countdownTimer.dataset.autoHide === 'true';
    const resetType = countdownTimer.dataset.resetType;
    const resetTime = countdownTimer.dataset.resetTime;
    const sectionId = '{{ section_id }}';

    if (!endDate) return;

    let targetDate = new Date(endDate);
    targetDate.setHours(targetDate.getHours() + timezoneOffset);

    // Check for saved target date in localStorage for reset functionality
    const storageKey = `countdown-${sectionId}-target`;
    const savedTarget = localStorage.getItem(storageKey);

    if (savedTarget && resetType !== 'none') {
      const savedDate = new Date(savedTarget);
      if (savedDate > new Date()) {
        targetDate = savedDate;
      }
    }

    let countdownInterval;

    function updateCountdown() {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      if (distance < 0) {
        handleCountdownEnd();
        return;
      }

      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);

      const daysEl = countdownTimer.querySelector('[data-time="days"]');
      const hoursEl = countdownTimer.querySelector('[data-time="hours"]');
      const minutesEl = countdownTimer.querySelector('[data-time="minutes"]');
      const secondsEl = countdownTimer.querySelector('[data-time="seconds"]');

      // Add flip animation when numbers change
      updateTimeElement(daysEl, days);
      updateTimeElement(hoursEl, hours);
      updateTimeElement(minutesEl, minutes);
      updateTimeElement(secondsEl, seconds);

      // Add urgent class when less than 24 hours remain
      const sectionEl = document.getElementById(sectionId);
      if (distance < (24 * 60 * 60 * 1000)) {
        sectionEl.classList.add('urgent');
      } else {
        sectionEl.classList.remove('urgent');
      }
    }

    function updateTimeElement(element, newValue) {
      if (!element) return;

      const formattedValue = newValue.toString().padStart(2, '0');
      const currentValue = element.textContent;

      if (currentValue !== formattedValue) {
        element.dataset.prev = currentValue;
        element.classList.add('flip-animation');

        setTimeout(() => {
          element.textContent = formattedValue;
          element.classList.remove('flip-animation');
        }, 300);
      }
    }

    function handleCountdownEnd() {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }

      if (autoHide) {
        document.getElementById(sectionId).classList.add('countdown-ended');
        localStorage.setItem(`countdown-${sectionId}-hidden`, 'true');
      }

      if (resetType === 'daily' && resetTime) {
        resetCountdownDaily();
      } else if (resetType === 'weekly' && resetTime) {
        resetCountdownWeekly();
      }
    }

    function resetCountdownDaily() {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const [hours, minutes] = (resetTime || '00:00').split(':');
      tomorrow.setHours(parseInt(hours) || 0, parseInt(minutes) || 0, 0, 0);

      targetDate = new Date(tomorrow.getTime() + (timezoneOffset * 60 * 60 * 1000));
      localStorage.setItem(storageKey, targetDate.toISOString());
      localStorage.removeItem(`countdown-${sectionId}-hidden`);
      document.getElementById(sectionId).classList.remove('countdown-ended');

      startCountdown();
    }

    function resetCountdownWeekly() {
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const [hours, minutes] = (resetTime || '00:00').split(':');
      nextWeek.setHours(parseInt(hours) || 0, parseInt(minutes) || 0, 0, 0);

      targetDate = new Date(nextWeek.getTime() + (timezoneOffset * 60 * 60 * 1000));
      localStorage.setItem(storageKey, targetDate.toISOString());
      localStorage.removeItem(`countdown-${sectionId}-hidden`);
      document.getElementById(sectionId).classList.remove('countdown-ended');

      startCountdown();
    }

    function startCountdown() {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
      updateCountdown();
      countdownInterval = setInterval(updateCountdown, 1000);
    }

    // Check if section should be hidden
    if (autoHide && localStorage.getItem(`countdown-${sectionId}-hidden`) === 'true') {
      const now = new Date().getTime();
      if (targetDate.getTime() > now) {
        // Target date has been reset, show section again
        localStorage.removeItem(`countdown-${sectionId}-hidden`);
        document.getElementById(sectionId).classList.remove('countdown-ended');
      } else {
        document.getElementById(sectionId).classList.add('countdown-ended');
        return;
      }
    }

    startCountdown();
  });
</script>

{% schema %}
{
  "name": "Countdown Timer",
  "tag": "section",
  "class": "countdown-timer-section",
  "settings": [
    {
      "type": "header",
      "content": "Content"
    },
    {
      "type": "text",
      "id": "flash_sale_text",
      "label": "Flash Sale Text",
      "default": "FLASH SALE"
    },
    {
      "type": "text",
      "id": "main_offer_text",
      "label": "Main Offer Text",
      "default": "UP TO 70% OFF EVERYTHING"
    },
    {
      "type": "text",
      "id": "guarantee_text",
      "label": "Guarantee Text",
      "default": "180-Day Money-Back & Free Remakes*"
    },
    {
      "type": "header",
      "content": "Timer Settings"
    },
    {
      "type": "text",
      "id": "end_date",
      "label": "End Date",
      "info": "Format: YYYY-MM-DD (e.g., 2024-12-31)",
      "placeholder": "2024-12-31"
    },
    {
      "type": "text",
      "id": "end_time",
      "label": "End Time",
      "info": "Format: HH:MM (24-hour format, e.g., 23:59)",
      "default": "23:59",
      "placeholder": "23:59"
    },
    {
      "type": "range",
      "id": "timezone_offset",
      "label": "Timezone Offset (hours)",
      "info": "Adjust for your timezone. Positive for ahead of UTC, negative for behind",
      "min": -12,
      "max": 12,
      "step": 1,
      "default": 0
    },
    {
      "type": "checkbox",
      "id": "auto_hide_on_end",
      "label": "Auto-hide when countdown ends",
      "default": false
    },
    {
      "type": "select",
      "id": "reset_type",
      "label": "Reset Type",
      "options": [
        {
          "value": "none",
          "label": "No Reset"
        },
        {
          "value": "daily",
          "label": "Reset Daily"
        },
        {
          "value": "weekly",
          "label": "Reset Weekly"
        }
      ],
      "default": "none"
    },
    {
      "type": "text",
      "id": "reset_time",
      "label": "Reset Time",
      "info": "Time to reset countdown (HH:MM format). Only used with daily/weekly reset",
      "default": "00:00",
      "placeholder": "00:00"
    },
    {
      "type": "header",
      "content": "Timer Labels"
    },
    {
      "type": "text",
      "id": "days_label",
      "label": "Days Label",
      "default": "DAYS"
    },
    {
      "type": "text",
      "id": "hours_label",
      "label": "Hours Label",
      "default": "HOURS"
    },
    {
      "type": "text",
      "id": "minutes_label",
      "label": "Minutes Label",
      "default": "MIN"
    },
    {
      "type": "text",
      "id": "seconds_label",
      "label": "Seconds Label",
      "default": "SEC"
    },
    {
      "type": "header",
      "content": "Styling"
    },
    {
      "type": "color",
      "id": "text_color",
      "label": "Text Color",
      "default": "#1e306e"
    },
    {
      "type": "color",
      "id": "timer_bg_color",
      "label": "Timer Background Color",
      "default": "#ffffff"
    },
    {
      "type": "color",
      "id": "timer_text_color",
      "label": "Timer Text Color",
      "default": "#1e306e"
    },
    {
      "type": "select",
      "id": "font_family",
      "label": "Font Family",
      "options": [
        {
          "value": "Lato",
          "label": "Lato"
        },
        {
          "value": "Arial",
          "label": "Arial"
        },
        {
          "value": "Helvetica",
          "label": "Helvetica"
        },
        {
          "value": "Georgia",
          "label": "Georgia"
        },
        {
          "value": "Times New Roman",
          "label": "Times New Roman"
        }
      ],
      "default": "Lato"
    },
    {
      "type": "checkbox",
      "id": "enable_animations",
      "label": "Enable Animations",
      "info": "Enable flip animations and hover effects",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_days",
      "label": "Show Days",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_hours",
      "label": "Show Hours",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_minutes",
      "label": "Show Minutes",
      "default": true
    },
    {
      "type": "checkbox",
      "id": "show_seconds",
      "label": "Show Seconds",
      "default": true
    },
    {
      "type": "header",
      "content": "Spacing"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "Top Padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "default": 20
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "Bottom Padding",
      "min": 0,
      "max": 100,
      "step": 5,
      "unit": "px",
      "default": 20
    }
  ],
  "presets": [
    {
      "name": "Countdown Timer",
      "settings": {
        "flash_sale_text": "FLASH SALE",
        "main_offer_text": "UP TO 70% OFF EVERYTHING",
        "guarantee_text": "180-Day Money-Back & Free Remakes*"
      }
    }
  ]
}
{% endschema %}
