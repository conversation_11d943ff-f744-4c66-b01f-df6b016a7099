/* Klarna Payment Section Styles */
.klarna-payment-section {
  margin: 15px 0;
  font-family: Lato, sans-serif;
}

.klarna-badge-container {
  display: flex;
  align-items: center;
  gap: 15px;
}

.klarna-logo-default {
  background-color: rgb(255, 168, 205);
  padding: 6px 12px;
  border-radius: 6px;
  display: inline-block;
}

.klarna-text {
  color: #000000 !important;
  font-weight: 700 !important;
  font-size: 14px !important;
  font-family: Lato, sans-serif !important;
  line-height: 1 !important;
  text-decoration: none !important;
}

.klarna-payment-text {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.klarna-payment-amount {
  color: #4a4a4a !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  font-family: Lato, sans-serif !important;
  text-decoration: none !important;
}

.klarna-learn-more {
  color: #4a4a4a !important;
  font-size: 16px !important;
  font-weight: 400 !important;
  font-family: Lato, sans-serif !important;
  text-decoration: underline !important;
  cursor: pointer !important;
  border: none !important;
  background: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.klarna-learn-more:hover {
  color: #4a4a4a !important;
  text-decoration: underline !important;
}

/* Popup Styles */
html body .klarna-popup-overlay,
body .klarna-popup-overlay,
.klarna-popup-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 99999999 !important;
  display: none !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 15px !important;
  box-sizing: border-box !important;
}

.klarna-popup-overlay[style*="flex"] {
  display: flex !important;
}

/* Force header z-index down when popup is open */
body.klarna-popup-open .shopify-section-header,
body.klarna-popup-open #header,
body.klarna-popup-open #nav,
body.klarna-popup-open #nav-bar,
body.klarna-popup-open #nav-user,
body.klarna-popup-open #nav-top {
  z-index: 1 !important;
}

html body .klarna-popup,
body .klarna-popup,
.klarna-popup {
  background: #ffffff !important;
  border-radius: 12px;
  width: 100%;
  max-width: 520px;
  height: calc(100vh - 30px);
  max-height: calc(100vh - 100px);
  overflow-y: auto;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  z-index: 99999999 !important;
  margin-top: 5px;
}

.klarna-popup-header {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 10px 24px;
}

.klarna-popup-logo {
  display: flex !important;
  align-items: center !important;
}

.klarna-popup-title-text {
  font-family: Lato, sans-serif !important;
  font-weight: 700 !important;
  font-size: 18px !important;
  color: #000000 !important;
}

html body .klarna-popup .klarna-popup-close,
body .klarna-popup .klarna-popup-close,
.klarna-popup .klarna-popup-close,
.klarna-popup-close {
  all: unset !important;
  background: none !important;
  border: none !important;
  font-size: 28px !important;
  cursor: pointer !important;
  color: #666 !important;
  padding: 0 !important;
  margin: 0 !important;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px !important;
  min-height: 40px !important;
  max-width: 40px !important;
  max-height: 40px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  transition: background-color 0.2s ease !important;
  font-family: Arial, sans-serif !important;
  font-weight: normal !important;
  line-height: 1 !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  box-shadow: none !important;
  outline: none !important;
  position: relative !important;
  z-index: 1000 !important;
}

html body .klarna-popup .klarna-popup-close:hover,
body .klarna-popup .klarna-popup-close:hover,
.klarna-popup .klarna-popup-close:hover,
.klarna-popup-close:hover {
  background-color: #f0f0f0 !important;
  color: #333 !important;
  transform: none !important;
  box-shadow: none !important;
}

html body .klarna-popup .klarna-popup-close:focus,
body .klarna-popup .klarna-popup-close:focus,
.klarna-popup .klarna-popup-close:focus,
.klarna-popup-close:focus {
  background-color: #f0f0f0 !important;
  color: #333 !important;
  outline: none !important;
  box-shadow: none !important;
}

html body .klarna-popup .klarna-popup-close:active,
body .klarna-popup .klarna-popup-close:active,
.klarna-popup .klarna-popup-close:active,
.klarna-popup-close:active {
  background-color: #e0e0e0 !important;
  color: #333 !important;
  transform: none !important;
  box-shadow: none !important;
}

html body .klarna-popup .klarna-popup-close:before,
body .klarna-popup .klarna-popup-close:before,
.klarna-popup .klarna-popup-close:before,
.klarna-popup-close:before {
  display: none !important;
  content: none !important;
}

html body .klarna-popup .klarna-popup-close:after,
body .klarna-popup .klarna-popup-close:after,
.klarna-popup .klarna-popup-close:after,
.klarna-popup-close:after {
  display: none !important;
  content: none !important;
}

.klarna-popup-content {
  padding: 24px !important;
}

.klarna-popup-main-title {
  font-family: Lato, sans-serif !important;
  font-weight: 700 !important;
  font-size: 24px !important;
  color: #000000 !important;
  margin: 0 0 8px 0 !important;
  line-height: 1.3 !important;
}

.klarna-popup-subtitle {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 16px !important;
  color: #666666 !important;
  margin: 0 0 24px 0 !important;
}

.klarna-payment-options {
  display: flex !important;
  flex-direction: column !important;
  gap: 16px !important;
  margin-bottom: 24px !important;
}

.klarna-option {
  background: #f8f9fa !important;
  border-radius: 8px !important;
  padding: 20px !important;
  position: relative !important;
}

.klarna-option-price {
  font-family: Lato, sans-serif !important;
  font-weight: 700 !important;
  font-size: 28px !important;
  color: #000000 !important;
  margin-bottom: 4px !important;
}

.klarna-option-label {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #666666 !important;
  margin-bottom: 8px !important;
}

.klarna-option-badge {
  position: absolute !important;
  top: 16px !important;
  right: 16px !important;
  background: #e8e3ff !important;
  color: #6b46c1 !important;
  padding: 6px 12px !important;
  border-radius: 16px !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  font-family: Lato, sans-serif !important;
}

.klarna-installment-schedule {
  display: flex !important;
  gap: 16px !important;
  margin-top: 16px !important;
  flex-wrap: wrap !important;
}

.klarna-installment {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  flex: 1 !important;
  min-width: 80px !important;
}

.klarna-circle {
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
  margin-bottom: 8px !important;
  position: relative !important;
}

.klarna-circle-1 { background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important; }
.klarna-circle-2 { background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important; }
.klarna-circle-3 { background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%) !important; }
.klarna-circle-4 { background: linear-gradient(135deg, #1d4ed8 0%, #3b82f6 100%) !important; }

.klarna-installment-text {
  text-align: center !important;
}

.klarna-amount {
  font-family: Lato, sans-serif !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #000000 !important;
  margin-bottom: 2px !important;
}

.klarna-timing {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #666666 !important;
}

.klarna-summary {
  display: flex !important;
  justify-content: space-between !important;
  padding: 16px 0 !important;
  border-top: 1px solid #e5e7eb !important;
  border-bottom: 1px solid #e5e7eb !important;
  margin-bottom: 16px !important;
}

.klarna-summary-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  flex: 1 !important;
}

.klarna-summary-label {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #666666 !important;
  margin-bottom: 4px !important;
}

.klarna-summary-value {
  font-family: Lato, sans-serif !important;
  font-weight: 600 !important;
  font-size: 14px !important;
  color: #000000 !important;
}

.klarna-disclaimer {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 12px !important;
  color: #666666 !important;
  margin: 0 0 24px 0 !important;
  line-height: 1.4 !important;
}

.klarna-how-it-works h3 {
  font-family: Lato, sans-serif !important;
  font-weight: 700 !important;
  font-size: 20px !important;
  color: #000000 !important;
  margin: 0 0 16px 0 !important;
}

.klarna-step {
  display: flex !important;
  gap: 12px !important;
  margin-bottom: 16px !important;
  align-items: flex-start !important;
}

.klarna-step-number {
  width: 24px !important;
  height: 24px !important;
  background: #000000 !important;
  color: #ffffff !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  font-family: Lato, sans-serif !important;
  flex-shrink: 0 !important;
  margin-top: 2px !important;
}

.klarna-step-content h4 {
  font-family: Lato, sans-serif !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  color: #000000 !important;
  margin: 0 0 4px 0 !important;
}

.klarna-step-content p {
  font-family: Lato, sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  color: #666666 !important;
  margin: 0 !important;
  line-height: 1.4 !important;
}

html body .klarna-popup .klarna-got-it-btn,
body .klarna-popup .klarna-got-it-btn,
.klarna-popup .klarna-got-it-btn,
.klarna-got-it-btn {
  width: 100% !important;
  background: #1a1a2e !important;
  color: #ffffff !important;
  border: none !important;
  border-radius: 8px !important;
  padding: 16px !important;
  font-family: Lato, sans-serif !important;
  font-weight: 600 !important;
  font-size: 16px !important;
  cursor: pointer !important;
  margin-top: 24px !important;
  margin-bottom: 20px !important;
  transition: background-color 0.2s !important;
  box-sizing: border-box !important;
}

html body .klarna-popup .klarna-got-it-btn:hover,
body .klarna-popup .klarna-got-it-btn:hover,
.klarna-popup .klarna-got-it-btn:hover,
.klarna-got-it-btn:hover {
  background: #16213e !important;
}

/* Тонкий скролл для попапа */
.klarna-popup::-webkit-scrollbar {
  width: 4px;
}

.klarna-popup::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.klarna-popup::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.klarna-popup::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .klarna-badge-container {
    gap: 12px !important;
  }

  .klarna-payment-amount,
  .klarna-learn-more {
    font-size: 15px !important;
  }

  .klarna-popup {
    width: calc(100% - 20px) !important;
    max-width: calc(100% - 20px) !important;
    height: calc(100vh - 20px) !important;
  }

  .klarna-popup-overlay {
    padding: 10px !important;
  }

  .klarna-popup-content {
    padding: 20px !important;
  }

  .klarna-popup-main-title {
    font-size: 22px !important;
  }

  .klarna-installment-schedule {
    gap: 12px !important;
  }

  .klarna-circle {
    width: 36px !important;
    height: 36px !important;
  }

  .klarna-summary {
    flex-direction: column !important;
    gap: 12px !important;
  }

  .klarna-summary-item {
    flex-direction: row !important;
    justify-content: space-between !important;
  }
}

@media (max-width: 480px) {
  .klarna-badge-container {
    flex-direction: column !important;
    align-items: flex-start !important;
    gap: 8px !important;
  }

  .klarna-payment-text {
    gap: 6px !important;
  }

  .klarna-payment-amount,
  .klarna-learn-more {
    font-size: 14px !important;
  }

  .klarna-installment-schedule {
    gap: 8px !important;
  }

  .klarna-installment {
    min-width: 70px !important;
  }

  .klarna-circle {
    width: 32px !important;
    height: 32px !important;
  }

  .klarna-amount {
    font-size: 13px !important;
  }

  .klarna-timing {
    font-size: 11px !important;
  }
}
