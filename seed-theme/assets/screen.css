/* -------------------------------------------

	Name:		Theme+
	Date:		2021/11/01

---------------------------------------------  */
*, *:before, *:after { margin: 0; padding: 0; box-sizing: border-box; outline-color: var(--secondary_bg); transform-origin: center center; }

html { overflow-x: hidden; overflow-y: scroll; width: 100%; min-height: 100%; margin: 0; padding-bottom: 0 !important; font-size: 100.01%; -webkit-tap-highlight-color: transparent; -moz-osx-font-smoothing: grayscale; -webkit-overflow-scrolling: touch; -ms-content-zooming: none; -ms-overflow-style: scrollbar; scroll-behavior: smooth; }
body { overflow-x: hidden; width: 100%; min-height: 100%; background: var(--custom_footer_bg_bottom); font-size: var(--main_fz); -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; direction: ltr; }
@-ms-viewport { width: device-width; }
@viewport { width: device-width; }

/*
BLOG / ARTICLE
list-comments: l4cm

PRODUCT
list-info: l4if
list-reviews: l4rv
module-product-entry: m6pe
module-reviews: m6rv

COLLECTION / SEARCH
list-view: l4vw
module-collection: m6cl
nav-aside: n6as
form-filter: f8fl

CART:
module-cart-extra: m6ca

GIFTCARD / QR / ERROR
module-giftcard: m6gf
module-qr: m6qr
module-error: m6er

LOGIN / REGISTER / ETC
form-login: f8lg // login/register


list-address: l4ad // product
list-alerts: l4al // hidden by default, but can be present on all
list-aside: l4as // product
list-check: l4ch // all
list-cart: l4ca // all
list-collection: l4cl // all
list-comments: l4cm // all
list-contact: l4cn // blog
list-count: l4cu // product, home?
list-drop: l4dr // all
list-featured: l4ft // all
list-info: l4if // collection
list-inline: l4in // all
list-news: l4ne // blog, home?
list-payments: l4pm // all
list-plain: l4pl // all
list-product-slider: l4pr // product, home?
list-reviews: l4rv // product
list-static: l4st // all
list-testimonials: l4ts // all
list-social: l4sc // all
list-total: l4tt // all
list-usp: l4us // all
list-view: l4vw // collection

module-aside: m6as // all
module-aside-compact: m6ac // all
module-box: m6bx // all
module-cart-extra: m6ca // cart
module-collection: m6cl // collection
module-contact: m6cn // collection
module-countdown: m6cu // all
module-error: m6er // error
module-featured: m6fr // all
module-giftcard: m6gf // giftcard
module-panel: m6pn // async, js // all
module-panel-cart: m6pc // async, js // all
module-product: m6pr // product, home?
module-product-entry: m6pe // product
module-qr: m6qr // qr
module-reviews: m6rv // product
module-tabs: m6tb // product
module-wide: m6wd // all

nav-aside: n6as // collection
nav-breadcrumbs: n6br // all
nav-pagination: n6pg // all
rating-a: r6rt // all

scheme-label: s1lb // all
scheme-price: s1pr // all

form-comment: f8cm // blog
form-filter: f8fl // collection
form-login: f8lg // login/register
form-newsletter: f8nw // all
form-product: f8pr // product
form-product-sticky: f8ps // async, js // product
form-sort: f8sr // async, css // collection
form-validate: f8vl // all

menu-active: m2a

theme-no-nav: t1nn
theme-no-top: t1nt 			// to be removed
theme-missing-nav: t1mn 	// to be removed
theme-aside: t1as
theme-plain: t1pl
theme-sticky-nav: t1sn
theme-has-product: t1pr
theme-dark: t1dr
theme-accessible: t1ac
theme-search-compact: t1sr
theme-no-button: t1nb
theme-has-collection: t1cl
theme-cart: t1ca

*/

/*! Variables --------- */
:root {
	--white:        hsl(0, 0%, 100%); 	/* #ffffff*/
	--porcelain:    hsl(0, 0%, 98%); 	/* #fbfbfb*/
	--sand:         hsl(0, 0%, 96%); 	/* #f4f4f4*/
	--gallery:      hsl(0, 0%, 93%);  	/* #ececec*/
	--gallery_st:   hsl(0, 0%, 93%);  	/* #ececec*/
	--alto:         hsl(0, 0%, 86%); 	/* #dcdcdc*/
	--gray:         hsl(0, 0%, 58%); 	/* #959595*/
	--gray_text:    #959595; 			/* #959595*/
	--coal:         hsl(0, 0%, 20%); 	/* #333333*/
	--coal_rgba:    51, 51, 51; 		/* #333333*/
	--coal_text:    var(--white);
	--cod:          hsl(0, 0%, 13%); 	/* #222222*/
	--black:        hsl(0, 0%, 0%); 	/* #000000*/
	--black_static: #000000; 			/* #000000*/

	--wine:         #eb5757;
	--lime:         #95bf47;
	--emerald:      #4caf50;
	--turquoise:    #48cfad;
	--sky:          #88c2ff;
	--amour:        #faf0f3;
	--tan: 			#FEEFEA;
	--pine: 		#F4B63F;
	--rose: 		#E63312;
	--blush:        #eeb8c9;

	--whatsapp:     #08b074;
	--twitter:      #000000;
	--linkedin:     #0072b1;
	--facebook:     #3c599f;
	--pinterest:    #E60023;
	--youtube:      #FF0000;

	--dark: 	    #1A3945;
	--light: 	    #EDF4F4;

	/* Predefined */
	--primary_text: 		var(--coal);
	--primary_text_h: 		var(--coal);
	--primary_text_rgb: 	var(--coal_rgba);
	--primary_bg: 			var(--white);
	--primary_bd: 			rgba(0,0,0,0);

	--secondary_text: 		var(--white);
	--secondary_bg:      	#F4602B;
	--secondary_bg_dark: 	#EE4E14;
	--secondary_bg_fade: 	#F2A88D;

	--secondary_btn_text:    var(--white);
	--secondary_bg_btn:      #48cfad;
	--secondary_bg_btn_dark: #32c7a2;
	--secondary_bg_btn_fade: #99ccbf;

	/* Other */
	--body_bg: 				var(--white);

	--tertiary_text: 		var(--white);
	--tertiary_bg: 			var(--lime);
	--tertiary_bg_dark: 	#82AF2E;
	--tertiary_bg_fade: 	#abc67a;

	--quaternary_text: 		var(--white);
	--quaternary_bg:      	#c64cf4;
	--quaternary_bg_dark: 	#bb32ed;
	--quaternary_bg_fade: 	#e0acf2;

	--quinary_text: 		var(--white);
	--quinary_bg:      		#c64cf4;
	--quinary_bg_dark: 		#bb32ed;
	--quinary_bg_fade: 		#e0acf2;

	--alert_error: 			var(--wine);
	--alert_error_bg: 		var(--amour);
	--alert_valid: 			var(--lime);

	--sale_label_bg: 		var(--tertiary_bg);
	--sale_label_bg_dark: 	var(--tertiary_bg_dark);
	--sale_label_text: 		var(--tertiary_text);

	--theme_bg_gradient:      linear-gradient(135deg, #E84A93 20%, #FBC34A 100%);
	--theme_bg_gradient_dark: linear-gradient(135deg, #FBC34A 20%, #E84A93 100%);

	--custom_alert_bg: var(--secondary_bg);
	--custom_alert_fg: var(--white);

	--custom_top_main_bg: var(--primary_text);				/* Background		- header + mobile menu header */
	--custom_top_main_fg: var(--white); 					/* Text				- header + mobile menu header */
	--custom_top_main_sh: none;

	--custom_top_main_link_bg: var(--secondary_bg);			/* Background		- menu, button */
	--custom_top_main_link_dark: var(--secondary_bg_dark);	/* Background hover	- menu, button */
	--custom_top_main_link_text: var(--secondary_text);		/* Text				- menu, button */

	--custom_top_nav_bg: var(--sand);						/* Background		- menu, main */
	--custom_top_nav_bd: var(--sand);						/* Border			- menu, main */
	--custom_top_nav_bd_op: .25;							/* Border opacity	- menu, main */
	--custom_top_nav_fg: var(--primary_text);				/* Text				- menu, main */
	--custom_top_nav_fg_hover: var(--secondary_bg);			/* Text hover		- menu, main */
	--custom_top_nav_h: calc(var(--custom_top_nav_fz) * 3.1428571429);/* Height			- menu, main */
	--custom_top_nav_ff: inherit;							/* Font family		- menu, main */
	--custom_top_nav_fz: 1em;								/* Font family		- menu, main */
	--custom_top_nav_fz_sub: 1em;							/* Font family		- menu, sub */
	--custom_top_nav_fw: inherit;							/* Font weight		- menu, main */
	--custom_top_nav_fs: inherit;							/* Font style		- menu, main */
	--custom_top_nav_ls: inherit;							/* Letter-spacing	- menu, main */

	--custom_drop_nav_head_bg: var(--white);				/* Background		- menu, dropdowns + mobile + panels */
	--custom_drop_nav_fg: var(--primary_text);  			/* Text				- menu, dropdowns + mobile + panels */
	--custom_drop_nav_bd: var(--custom_bd);  				/* Text				- menu, dropdowns + mobile + panels */
	--custom_drop_nav_fg_hover: var(--secondary_bg);		/* Text hover		- menu, dropdowns + mobile + panels */
	--custom_drop_nav_input_bd: var(--custom_input_bd);
	--custom_drop_nav_input_bg: var(--custom_input_bg);
	--custom_drop_nav_input_fg: var(--custom_input_fg);
	--custom_drop_nav_input_pl: var(--custom_input_pl);

	--custom_top_up_bg: var(--sand);						/* Background		- menu, upper */
	--custom_top_up_fg: var(--primary_text);				/* Text 			- menu, upper */
	--custom_top_up_fg_hover: var(--secondary_bg);			/* Text hover 		- menu, upper */
	--custom_top_up_h: 34px; 								/* Height */

	--custom_top_search_h: calc(var(--btn_fz) * var(--main_lh_h) + min(var(--btn_pv), 20px) * 2);					/* Input height		- searchbox */
	--custom_top_search_bg_cont: var(--sand);				/* Background		- searchbox container */
	--custom_top_search_bg: var(--white);					/* Background		- searchbox */
	--custom_top_search_bd: var(--white);					/* Border			- searchbox */
	--custom_top_search_fg: var(--primary_text);			/* Text				- searchbox */
	--custom_top_search_pl: var(--gray_text);				/* Placeholder		- searchbox */

	--custom_drop_nav_bg: var(--sand); 						/* Background		- megamenu main */

	--custom_footer_bg: var(--sand);						/* Background		- footer */
	--custom_footer_fg: var(--primary_text);				/* Text 			- footer */
	--custom_footer_fg_hover: var(--secondary_bg);			/* Text hover 		- footer */

	--custom_footer_bg_bottom: var(--gallery);				/* Background		- footer, lower */
	--custom_footer_bd_bottom: var(--custom_bd);			/* Border		- footer, lower */
	--custom_footer_fg_bottom: var(--primary_text);			/* Text 			- footer, lower */
	--custom_footer_fg_bottom_hover: var(--secondary_bg);	/* Text hover 		- footer, lower */
	--custom_footer_link_bg: var(--secondary_bg);			/* Background		- footer, button */
	--custom_footer_link_dark: var(--secondary_bg_dark);	/* Background hover	- footer, button */
	--custom_footer_link_text: var(--secondary_text);		/* Text				- footer, button */
	--footer_bottom_p: 20px;

	--custom_input_bg: var(--white);
	--custom_input_bd: var(--gallery);
	--custom_input_fg: var(--primary_text);
	--custom_input_pl: var(--gray_text);

	--custom_bd: var(--custom_input_bd);

	--main_fz: 14px;
	--main_fz_small: calc(var(--main_fz) * 0.8571428571);
	--main_fw: 400;
	--main_fw_strong: 700;
	--main_fw_h: 700;
	--main_fw_h_strong: 700;
	--main_fs: normal;
	--main_fs_h: normal;
	--main_ls: normal;
	--main_ls_h: normal;
	--main_ff: Inter, Noto, 'Noto Sans', -apple-system, system-ui, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantrell, 'Helvetica Neue', Arial, Helvetica, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
	--main_ff_h: Georgia, Times, 'Times New Roman', serif;
	--main_tt: none;
	--main_tt_h: none;
	--main_lh: 1.8571428571;
	--main_lh_l: calc(var(--main_lh) * 0.5 + var(--main_lh_h) * 0.5);
	--main_lh_h: 1.1875;
	--main_lh_hc: 1.1;
	--main_mr: calc(var(--main_lh) * var(--main_fz));
	--main_mr_half: calc(var(--main_mr) * 0.5);
	--main_mr_h: calc(var(--main_mr) * 0.4615384615);

	--main_h_small: var(--size_16_f);
	--main_h1: 32px;
	--main_h2: 24px;
	--main_h3: 21px;
	--main_h4: 18px;
	--main_h5: var(--main_h_small);
	--main_h6: var(--main_h_small);
	--main_lead: 18px;

	--mob_h_small: var(--size_13_f);
	--mob_fz: 14px;
	--mob_h1: 22px;
	--mob_h2: 18px;
	--mob_h3: 17px;
	--mob_h4: 15px;
	--mob_h5: var(--mob_h_small);
	--mob_h6: var(--mob_h_small);
	--mob_lead: 16px;

	--size_8:  8px;
	--size_10: 10px;
	--size_12: 12px;
	--size_13: 13px;
	--size_14: 14px;
	--size_16: 16px;
	--size_18: 18px;
	--size_20: 20px;
	--size_22: 22px;
	--size_24: 24px;
	--size_26: 26px;
	--size_28: 28px;
	--size_30: 30px;
	--size_32: 32px;
	--size_33: 33px;
	--size_36: 36px;
	--size_38: 38px;
	--size_40: 40px;
	--size_46: 46px;
	--size_48: 48px;
	--size_50: 50px;
	--size_52: 52px;
	--size_56: 56px;
	--size_60: 60px;
	--size_70: 70px;
	--size_100: 100px;

	--size_8_f:  var(--size_8);
	--size_10_f: var(--size_10);
	--size_12_f: var(--size_12);
	--size_13_f: var(--size_13);
	--size_14_f: var(--size_14);
	--size_16_f: var(--size_16);
	--size_18_f: var(--size_18);
	--size_20_f: var(--size_20);
	--size_22_f: var(--size_22);
	--size_24_f: var(--size_24);
	--size_26_f: var(--size_26);
	--size_28_f: var(--size_28);
	--size_30_f: var(--size_30);
	--size_32_f: var(--size_32);
	--size_34_f: var(--size_34);
	--size_36_f: var(--size_36);
	--size_38_f: var(--size_38);
	--size_40_f: var(--size_40);
	--size_46_f: var(--size_46);
	--size_48_f: var(--size_48);
	--size_50_f: var(--size_50);
	--size_52_f: var(--size_52);
	--size_56_f: var(--size_56);
	--size_60_f: var(--size_60);
	--size_70_f: var(--size_70);
	--size_100_f: var(--size_100);
	--size_content: var(--size_16);

	--btn_bd: 1px;
	--btn_br: var(--b2r);
	--btn_pv: 14px;
	--btn_ph: min(18px, 5vw);
	--btn_fz: var(--size_14_f);
	--btn_fz_mob: var(--size_14_f);
	--btn_fw: var(--main_fw_strong);
	--btn_ff: inherit;
	--btn_fs: normal;
	--btn_lh: 1.1875;
	--btn_ls: normal;
	--btn_tt: none;
	--btn_sh_inner_c: rgba(0,0,0,.1);
	--btn_sh_inner: inset 2px -2px 0 var(--btn_sh_inner_c);
	--btn_dist: 16px;
	--btn_dist2: 16px;
	--btn_miw: min(100%, 144px); /*calc(var(--btn_ph) * 8)*/
	--btn_circle_size: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh));

	--input_h: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh));

	--sale_label_fz: var(--size_12);
	--breadcrumbs_fz: var(--size_13_f);
	--price_fz: var(--main_fz);
	--price_ff: var(--main_ff);
	--price_fs: var(--main_fs);
	--price_fw: var(--main_fw_strong);
	--price_lh: var(--main_lh_l);
	--price_ls: var(--main_ls);
	--price_color: inherit;
	--price_color_old: var(--primary_text);
	--product_label_bg: var(--secondary_bg);
	--product_label_bg_dark: rgba(0,0,0,.1);
	--product_label_text: var(--secondary_text);
	--product_label_bg_custom: var(--quaternary_bg);
	--product_label_text_custom: var(--quaternary_text);

	/*--popup_h: var(--size_24_f);
	--popup_hm: var(--size_20_f);*/

	--rpp: 30px;		/* Global padding */
	--rpn: -30px;		/* Global padding (negative) */

	--b2r: 4px;			/* Global border-radius */
	--b2i: var(--b2r);	/* Input border-radius */
	--b2p: var(--b2r);	/* Picture border-radius */
	--glw: 1280px; 		/* Global width */
	--ghw: var(--glw); 	/* Header width */

	--l0ra: 0px;
	--lar0: auto;
	--l1ra: 100%;
	--lar1: auto;

	--text_align_start: left;
	--text_align_end: right;

	--mobile_nav_fz: 1em;
	--drop_nav_mah: 75vh;
	--drop_nav_mah_fixed: 75vh;
	--mega_nav_mah: 75vh;
	--mega_nav_mah_fixed: 75vh;
	--sticky_nav_mah: 100vh;
	--sticky_offset: 0px;
	--f8ps_h: 0px;
	--cookie_h: 0px;
	--root_pb: 0px;
	--header_outer_height: 3000px;
	--content_p: 25px;
	--scrollbar_width: 17px;
	--100vw: 110vw /*calc(100vw - var(--scrollbar_width))*/;
	--header_mih: 69px;
	--header_mih_c: var(--header_mih);
	--header_mih_m: 45px;
	--header_height_static: var(--header_mih);
	--logo_h: 130px;
	--logo_h_m: 88px;
	--nav_user_h: max(var(--input_h), var(--logo_h));
	--nav_dist: 24px;
	--nav_top_h: 34px;
	--nav_top_h_m: 0px;
	--justify_content: flex-start;
	--footer_li_dist: 0px;
	/*--nav_user_h: max(var(--input_h), calc(var(--header_mih) - 24px));*/

	--ratio: 1;

	--f8ps_bg: var(--custom_drop_nav_head_bg);
	--f8ps_fg: var(--custom_drop_nav_head_fg);

	--label_dist: 10px;
	--ann_p: 14px;
	--f8pr_submit_dist: 8px;
	--dots_dist: 24px;
	--f8sr_dist: 30px;
	--wishlist_text: var(--primary_text);

	--box_shadow: 0 4px 4px rgba(0,0,0,.1);
	--search_w: 390px;
	--overlay_opacity: .2;
	--pager_w: 75px;
	--pager_p: 10px;
	--pd_f_h: 12.5px;
	--search_mob_pd: 12px;
	--mr_menu: calc(var(--main_mr) * 0.55);
	--placeholder_fz: var(--main_fz);
	--ip_ver: center;
	--ip_hor: center;
}


/*! Defaults --------- */
body, textarea, input, select, option, button { color: var(--primary_text); font-family: var(--main_ff); font-style: var(--main_fs); font-weight: var(--main_fw); line-height: var(--main_lh); text-transform: var(--main_tt); letter-spacing: var(--main_ls); }
li *, dt *, dd *, p *, figure *, th *, td *, legend * { font-size: 1em; }
ul, ol, dl, p, figure, blockquote, table, .table-wrapper, pre, .l4us, .shopify-challenge__container .g-recaptcha, .widget { margin-bottom: var(--main_mr); }

h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, legend { margin: calc(var(--main_mr) * 1.69) 0 var(--main_mr_h); color: var(--primary_text_h); font-weight: var(--main_fw_h); font-style: var(--main_fs_h); font-family: var(--main_ff_h); line-height: var(--main_lh_h); text-transform: var(--main_tt_h); letter-spacing: var(--main_ls_h); }
h1, .h1, .l4cl.category h1, .l4cl .has-text h1, .l4cl .cols h1 { font-size: var(--main_h1); }
h2, .h2, .l4cl.category h2, .l4cl .has-text h2, .l4cl .cols h2 { font-size: var(--main_h2); }
h3, .h3, .l4cl.category h3, .l4cl .has-text h3, .l4cl .cols h3 { font-size: var(--main_h3); }
h4, .h4, .l4cl.category h4, .l4cl .has-text h4, .l4cl .cols h4 { font-size: var(--main_h4); }
h5, .h5, .l4cl.category h5, .l4cl .has-text h5, .l4cl .cols h5 { font-size: var(--main_h5); }
h6, .h6, .l4cl.category h6, .l4cl .has-text h6, .l4cl .cols h6 { font-size: var(--main_h6); }
h1 .small, h2 .small, h3 .small, h4 .small, h5 .small, h6 .small { display: block; margin: 0 0 calc(var(--main_mr) * 0.4615384615); /*color: var(--gray_text);*/ font-weight: var(--main_fw); font-family: var(--main_ff); font-size: var(--main_fz); line-height: var(--main_lh); text-transform: var(--main_tt); letter-spacing: var(--main_ls); opacity: .53; }
h1 .small.overlay-content, h2 .small.overlay-content, h3 .small.overlay-content, h4 .small.overlay-content, h5 .small.overlay-content, h6 .small.overlay-content { opacity: 1; }
h1 img, h2 img, h3 img, h4 img, h5 img, h6 img { vertical-align: baseline; }

a { background: none; color: var(--secondary_bg); text-decoration: underline; cursor: pointer; outline-width: 0; -webkit-text-decoration-skip: objects; } /*---*/ a span { cursor: pointer; }
h1 a, h2 a, h3 a, h4 a, h5 a, h6 a, legend a, .h1 a, .h2 a, .h3 a, .h4 a, .h5 a, .h6 a, .shopify-section-footer a, .l4sc a, .l4cn a, .l4dr a:not([class*="overlay-"]), #root .shopify-section-footer a.header-toggle, .m6fr *:not(.link-btn, .submit) > a:not([class*="overlay-"]), [data-whatintent="mouse"] .m6fr a:hover, .n6pg li.active a, .l4in a, .l4ca p:not(.removed, .link-btn) a, .r6rt a, label span a, .label span a, #root .l4dr li.has-social.toggle > a, #search div ul a, nav .l4in a, .l4ft .link a:not([class*="overlay-"]), .l4ft h1 a, .l4ft h2 a, .l4ft h3 a, .l4ft h4 a, .l4ft h5 a, .l4ft h6 a .m6wd.overlay-content figure.background ~ .l4cu li > span, .l4al a, .l4al[class*="overlay"] li, /*.shopify-section-header .search-compact-toggle,*/ #nav-top .li > a, .m6wd.overlay-content h1, .m6wd.overlay-content h2, .m6wd.overlay-content h3, .m6wd.overlay-content h4, .m6wd.overlay-content h5, .m6wd.overlay-content h6, .m6wd.overlay-theme h1, .m6wd.overlay-theme h2, .m6wd.overlay-theme h3, .m6wd.overlay-theme h4, .m6wd.overlay-theme h5, .m6wd.overlay-theme h6, .m6wd.overlay-black h1, .m6wd.overlay-black h2, .m6wd.overlay-black h3, .m6wd.overlay-black h4, .m6wd.overlay-black h5, .m6wd.overlay-black h6, .f8nw h1, .f8nw h2, .f8nw h3, .f8nw h4, .f8nw h5, .f8nw h6, .m6wd.overlay-tan h1, .m6wd.overlay-tan h2, .m6wd.overlay-tan h3, .m6wd.overlay-tan h4, .m6wd.overlay-tan h5, .m6wd.overlay-tan h6, .m6cu > h1, .m6cu > h2, .m6cu > h3, .m6cu > h4, .m6cu > h5, .m6cu > h6, .m6bx .overlay-content a, .m6bx a.overlay-content, .m6tx a { color: inherit; }
a[rel*="external"]:after { content: "\e93f"; display: inline-block; position: relative; top: .05em; margin-left: 5px; font-weight: 400; font-family: i; font-size: 0.7857142857em; font-style: normal; line-height: 1px; }

ul, ol, dd, blockquote { padding-left: 40px; }


/*! Mixins --------- */
/* cl:b */	.l4ca li:after, .l4cl li:after, .l4cl.aside:after, .l4pr:after, label:after, .label:after, .l4ne.featured:after, form p:not(.link-btn):after { content: ""; display: block; overflow: hidden; clear: both; }
/* fill */	.check.inside label > span:before, .m6fr.wide .media-flexible, picture.s4wi[class*="img-multiply"] .swiper-slide a:before, .l4ft li > .content:before, .m6cu .background, .search-compact a:before, .m6lm.high:before, #totop a:before, .l4cl .link-btn.sticky:before, .l4cl form.sticky:before, .l4cl .link-btn.sticky:after, .l4cl form.sticky:after, a .model-3d:before, figure.img-multiply:before, picture.img-multiply:before, figure.img-multiply-bg:before, picture.img-multiply-bg:before, .s1bx:before, #cookie-bar:before, .l4ca li:before, .shopify-section-footer > nav > *:before, .link-overlay, .l4ft a:after, .l4ft figure a, form.processing .submit:before, .l4pr .swiper-button-nav:after, .img-overlay, .m6wd .background:before, figure .overlay, .form-cart aside .l4pm:before, .l4ft .background, .m6fr article:before, .m6fr article > figure:before, .m6fr:before, .f8nw:before, .f8nw .background, .l4ts.box li:before, .l4cu.box li > span:before, .m6cu:before, .l4al li:after, .l4al li:before, .l4cn a:before, .l4ft li > .main:before, .countdown .simply-amount > span:before, .l4ft figure, #search .l4ca a:before, .m6wd figure.background.overlay:before, .l4cl.hr li:before, .m6as:before, .l4ft figure:before, .l4ft figure, .l4cl.list li:before, .noUi-horizontal .noUi-handle:before, .l4cn.box a:before, .l4cn.box a:after, .l4cn.box li:before, .m6bx:before, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span:before, .m6fr figure .background, .m6fr figure:before, .check.wide:before, .m6tb > nav ul li.active:before, .l4sc.box:before, #nav-user > ul > li > a i span:before, .l4cl li:before, /*.l4cl .li:before,*/ .l4ca footer p a:after, .input-show label a.toggle, .form-cart aside:before, blockquote:before, .l4sc a:after, .spr-pagination > div > * a:before, .n6pg li a:before, .l4ne a:before, .m6fr article a:before, .r6rt .rating > *:before, .r6rt .rating > * .fill, .l4cl a:after, .m6fr article > figure, .m6fr .media-flexible, .link-btn a:before, button:before, .shopify-section-header:before, #nav .l4cn li a:before, #nav-bar .l4cn li a:before, .shopify-section-footer a.header-toggle, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more:before { content: ""; display: block; overflow: hidden; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: -1; margin: 0; text-align: left; text-indent: -3000em; direction: ltr; }
/* wide */	.m6tb .tabs-header:after, .m6bx.wide:before, #nav-top > .overlay-close, .m6cu.wide .background, .m6cu.wide:before, #content #background, .f8nw.wide:before, .f8nw.wide .background, .l4us.wide:before, .m6wd .background, .m6tb > nav:before, .m6wd:before, .shopify-section-footer:before, .shopify-section-footer > div:before, #header > .close, #nav > ul > a.close, #nav-bar > ul > a.close, #search:before, #nav:before, #nav-top:before, #nav-bar:before { content: ""; display: block; position: absolute; left: 50%; right: auto; top: 0; bottom: 0; z-index: -1; /*width: 110vw; margin: 0 0 0 -55vw;*/ width: var(--100vw); margin: 0 0 0 calc(0px - var(--100vw) * 0.5); }
/* icon */ 	.l4cl figure > a.remove i, #header-outer li.show-all:after, #nav > ul > li.sub > a:not(.toggle):before, #nav-bar > ul > li.sub > a:not(.toggle):before, #nav > ul > li.sub-static > a:not(.toggle):before, #nav-bar > ul > li.sub-static > a:not(.toggle):before, #root .link-btn a.circle i, #root button.circle i, .l4hs > li > a:before, #header-inner > .link-btn a:after, #totop a:after, .m6fr .play-pause:before, .l4us .next-item:before, .icon-text-size:after, .recommendation-modal__close-button-container button:after, #search p > a.search-back:before, #search .clear-toggle:before, .l4cl .link-btn.sticky a i, #nav > ul > li > a.toggle-wide:before, .l4al .close:before, .fancybox__container .carousel__button:before, #root .f8nw button i, .l4in.a li:before, /*.l4us .li:before,*/ .accordion-a summary:before, #nav-user > ul > li > form a.toggle:before, #search > a.toggle:before, .m6tb .tabs-header:before, .input-inline button i, .input-inline .link-btn a i, #nav-top > ul > li > a i:after, .l4cn i:after, .l4cl.wide .link-btn a i, .input-show label:before, .input-amount a[role="button"]:before, .swiper-button-nav:before, .n6pg li.prev a:after, .n6pg li.next a:after, .r6rt .rating > *:before, .r6rt .rating > * .fill:before, label a.show:before, .shopify-section-footer button i, #nav > a.close:before, #nav-bar > a.close:before, #nav ul ul a.toggle:before, #nav-bar ul ul a.toggle:before, .shopify-section-footer a.header-toggle:before, .l4dr li.sub > a:before, #nav > ul > li > a.toggle:before, #nav-bar > ul > li > a.toggle:before, #nav-top > ul > li.sub > label:before, #nav-user > ul > li.sub > label:before, #search button:before, #nav-user > ul > li.sub > a:before, #nav-top > ul > li.sub > a:before, .l4us li:before, .n6br li:before, .spr-pagination > div > .spr-pagination-prev a:after, .spr-pagination > div > .spr-pagination-next a:after { display: block; overflow: visible; position: absolute; left: 0; right: 0; top: 50%; bottom: auto; margin: -10px 0 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-weight: 400; font-family: i; font-style: normal; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal; }
/* z:9  */	.link-overlay, #nav-top > .overlay-close, .m6cu > .link-overlay, .m6lm.high:before, a .model-3d:before, .l4ft a:after, .m6fr .swiper-slide-active, figure .img-overlay, .m6wd .background:before, figure .overlay, #background:before, #nav-user > ul > li.toggle, .l4cn a:before, #search .l4ca a:before, .m6wd figure.background.overlay:before, .noUi-horizontal .noUi-handle:before, .l4cn.box a:after, .m6fr figure:before, .m6fr figure .background, .m6tb > nav ul li.active:before, .l4ca footer p a:after, .input-show label a.toggle, .l4sc a:after, .l4ne a:before, .m6fr article a:before, .l4cl a:after, #nav .l4cn li a:before, .shopify-section-footer a.header-toggle, #nav > ul > a.close { z-index: 8; }
/* tr:y */ 	.l4dr ul li img { transform: translateY(-50%); }
/* tr:n */ 	.l4pr .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active:before, .l4cl.list .link-btn, .l4cl.wide .link-btn, #root .l4ft li.inline figure img, #root .l4ft li.inline figure picture, .l4ft div figure img, .l4ft div figure picture { transform: none; }
/* show */	#root .l4dr ul li a i, .swiper-pagination-bullet.swiper-pagination-bullet-active:before { visibility: visible; opacity: 1; }
/* i:wi */	.l4ne-figure-before img, .l4cl-figure-before img, .m6as > figure > picture img, .m6as > figure video, .l4ne img { display: block; position: relative; z-index: 1; width: 100% !important; object-fit: cover; object-position: var(--ip_hor) var(--ip_ver); }
/* i:ct */	.l4pr .swiper-pagination-bullets .swiper-pagination-bullet img, .l4pr img, .l4cl img { align-self: center; /*width: auto; max-height: 100% !important; flex-shrink: 0; object-fit: contain; object-position: center center;*/ }
/* i:bg */	.m6cu .background, .m6cu .background *, .f8nw .background img, .f8nw .background picture, .f8nw .background video, #background img, #background picture, #background video, #root .m6wd .background img, #root .m6wd .background picture, #root .l4ft li:not(.inline) img, #root .l4ft li:not(.inline) video, #root .l4ft li:not(.inline) figure svg, #root .m6fr figure img, #root .m6fr figure video, #root .m6fr figure svg { display: block; width: 100% !important; height: 100% !important; border-radius: 0; object-fit: cover; object-position: var(--ip_hor) var(--ip_ver); }
#root .check.box .cover img, #root img.cover, #root .cover img, #root picture.cover, #root .cover picture, #root video.cover, #root .cover video, #root iframe.cover, #root .cover iframe, #root .l4cl figure.overlay img, #root .l4cl figure.overlay picture { display: block; width: 100% !important; height: 100% !important; object-fit: cover; object-position: var(--ip_hor) var(--ip_ver); }
/* hyph */ 	.l4if li > span:first-child, .l4cl p, #logo, #logo a, #logo .broken-img, a.email, html:not(.no-hyph) h1, html:not(.no-hyph) h2, html:not(.no-hyph) h3, html:not(.no-hyph) h4, html:not(.no-hyph) h5, html:not(.no-hyph) h6, html:not(.no-hyph) .h1, html:not(.no-hyph) .h2, html:not(.no-hyph) .h3, html:not(.no-hyph) .h4, html:not(.no-hyph) .h5, html:not(.no-hyph) .h6, html:not(.no-hyph) legend, html:not(.no-hyph) h1 a, html:not(.no-hyph) h2 a, html:not(.no-hyph) h3 a, html:not(.no-hyph) h4 a, html:not(.no-hyph) h5 a, html:not(.no-hyph) h6 a, .s1bw { overflow-wrap: break-word; -ms-word-break: break-word; word-break: break-word; -webkit-hyphens: auto; /*-webkit-hyphenate-limit-before: 3; -webkit-hyphenate-limit-after: 3; -webkit-hyphenate-limit-chars: 6 3 3; -webkit-hyphenate-limit-lines: 2; -webkit-hyphenate-limit-last: always; -webkit-hyphenate-limit-zone: 8%; -moz-hyphens: auto; -moz-hyphenate-limit-chars: 6 3 3; -moz-hyphenate-limit-lines: 2; -moz-hyphenate-limit-last: always; -moz-hyphenate-limit-zone: 8%;*/ -ms-hyphens: auto; /*-ms-hyphenate-limit-chars: 6 3 3; -ms-hyphenate-limit-lines: 2; -ms-hyphenate-limit-last: always; -ms-hyphenate-limit-zone: 8%;*/ hyphens: auto; /*hyphenate-limit-chars: 6 3 3; hyphenate-limit-lines: 2; hyphenate-limit-last: always; hyphenate-limit-zone: 8%;*/ }
/* td:u */ 	.size-12 a.overlay-content, .shopify-section-footer nav p a, a.overlay-content.text-underline { text-decoration: underline; }
/* td:n */ 	#root .l4cl.category a, #root .l4ne p a, #nav-top .li > a, .l4al a, #root .l4in .active > a, .r6rt a, #root .l4cl a, nav .l4in a, #search div a, .l4dr a:not(.inline), #root .l4ca footer p a, .l4ca p:not(.removed) a, #root .n6pg li.active a, a.strong, a.overlay-content, .n6br a, #root .l4sc a, #root .shopify-section-footer .l4sc a, #root .l4cn a, .shopify-section-footer a, #root .l4dr ul li a, #root .l4sc a, #root .shopify-section-footer .active > a, #nav-top > ul > li > a[rel], #root figure > a, #root .link-btn a, #root .l4dr li.active > a, #root .shopify-section-header .active > a, h1 a, h2 a, h3 a, h4 a, h5 a, h6 a, a.overlay-gray, a.overlay-c, #root .text-no-underline { text-decoration: none; }


/*! Layout --------- */
#root { position: relative; z-index: 13; width: 100%; min-height: 100vh; padding: 0 var(--rpp) var(--root_pb); background: var(--body_bg); scrollbar-width: none; }
#root::-webkit-scrollbar { width: 0; }
#root:after { content: ""; display: block; position: fixed; left: 0; right: 0; top: 0; bottom: 0; visibility: hidden; z-index: 997; background: var(--primary_text); text-indent: -3000em; direction: ltr;  cursor: pointer; opacity: 0; }
.shopify-section-header {
	position: relative; /*left: 50%;*/ top: 0; z-index: 10; max-width: calc(100% + var(--rpp) * 2); margin: 0 var(--rpn); padding: 0 var(--rpp); /*width: var(--glw); margin: 0 0 0 var(--gln); padding: 47px 0 0;*/ color: var(--custom_top_main_fg); --maw_a: 140px; --maw_la: min(25vw, 320px); --logo_w: auto; --dist_main: 32px;
}
#root #header > #header-inner:first-child:last-child { margin-right: 0; }
.shopify-section-header:before { z-index: -2; box-shadow: var(--custom_top_main_sh); border-bottom: 1px solid var(--custom_top_nav_bd); background: var(--custom_top_main_bg); }
.shopify-section-header #header-outer { position: relative; width: 100%; max-width: var(--ghw); margin-left: auto; margin-right: auto; }
.shopify-section-header #header-outer > .close { display: none; }
#header, #header-inner { align-items: center; }
.shopify-section-header #header { position: relative; z-index: 5; width: calc(100% + var(--dist_main)); min-height: var(--header_mih); margin-right: calc(0px - var(--dist_main)); padding-top: 12px; }
#header > .overlay-close { content: ""; display: block; position: absolute; left: 50%; right: auto; top: 0; bottom: 0; z-index: 98; width: var(--100vw); margin: 0 0 0 calc(0px - var(--100vw) * 0.5 - var(--dist_main) * 0.5); visibility: hidden; opacity: 0; }
#root .shopify-section-header #header-inner { max-width: 100%; margin-right: 0; border-right-width: 0; }
.shopify-section-header #header > #distance-counter { display: block; position: absolute; left: -10px; right: auto; bottom: auto; top: 100% !important; z-index: -10; margin: 0; padding: 0; border-width: 0; }
.shopify-section-header #header > *, #root .shopify-section-header #header-inner > * { margin-right: 0; border-right: var(--dist_main) solid rgba(0,0,0,0); }
#logo { min-width: 0; max-width: var(--maw_la); margin: 0 0 12px; color: inherit; font-size: var(--size_22); line-height: var(--main_lh_h); }
#logo .broken-img { max-width: 232px; font-size: var(--size_14_f); }
#logo img:after { content: attr(alt); display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; background: var(--custom_top_main_bg); font-size: var(--size_14_f); line-height: var(--main_lh_h); }
#logo span { display: block; }
#logo a { min-width: 0; color: inherit; font-weight: var(--main_fw_strong); line-height: var(--main_lh_h); text-decoration: none; }
#logo img, #logo picture { display: block; overflow: hidden; position: relative; width: var(--logo_w) !important; height: auto !important; max-height: var(--logo_h); border-radius: 0; object-fit: contain; object-position: 0 center; }
#logo picture { max-height: var(--logo_h) !important; }
#root .shopify-section-header > .hidden, #root #header > .hidden, #root #header-outer > .hidden { display: none; }
#root .shopify-section-header > .wide, #root #header > .wide, #root #header-outer > .wide { max-width: none; margin-left: 0; }
#root #header-inner > .link-btn { min-width: 0; flex-shrink: 0; }
#root #header-inner > .link-btn > * + * { margin-left: var(--dist_main); }
[dir="rtl"] #root #header-inner > .link-btn > * + * { margin-left: 0; margin-right: var(--dist_main); }
#root #header-inner > .link-btn .search-compact { display: none; position: relative; z-index: 2; width: auto; min-width: 0; padding: 0; border-radius: 0; color: var(--custom_top_main_fg); animation: none; }
#header-inner > .link-btn .search-compact:before { display: none; }
#header-inner > .link-btn .search-compact:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; z-index: 5; width: 44px; height: 44px; z-index: 9; margin: -22px 0 0 -12px; }
html[dir="rtl"] #header-inner > .link-btn .search-compact:after { margin-left: calc(0px - var(--dist_main)); }
#header-inner > .link-btn .search-compact i { display: block; top: 0; margin: 0; font-size: var(--size_20_f); line-height: 45px; }
.t1sh #root #header-inner > .link-btn .search-compact { display: flex; }
#header-inner #search.compact-handle ~ nav li.search { display: none; }

@media only screen and (min-width: 1301px) {
	.search-compact-is-centered #header-inner:not(.hide-btn) { --maw_la: min(20vw, 240px); }
	.search-compact-is-centered #root #search { max-width: 500px; }
	#root .shopify-section-header #header-inner > #search.text-center-sticky {
		position: absolute; left: 50%; z-index: 98; width: 100%; margin-left: 0; margin-right: 0;
		transform: translateX(-50%);
	}
	.search-compact-active #root .shopify-section-header #header-inner > #search.text-center-sticky {
		top: 50%;
		transform: translate(-50%, -50%);
	}
	.search-compact-active #root .shopify-section-header #header-inner > #search:not(.compact-mobile).text-center-sticky { z-index: 99; }
	.search-compact-is-centered #header-inner:not(.text-center-logo) #logo:has(span), .search-compact-is-centered #header-inner.logo-text:not(.text-center-logo) #logo { max-width: var(--maw_la); }
}
@media only screen and (min-width: 761px) {
	#header-inner:not(.text-center-logo) #logo:has(span), #header-inner.logo-text:not(.text-center-logo) #logo { max-width: none; flex-shrink: 10; }

	#header-inner.text-center-logo { --maw_a: 150px; --maw_lb: calc(var(--search_w) + var(--dist_main)); --nav_user_h: calc(var(--header_mih) - 24px); }
	#header-inner.text-center-logo #logo { text-align: center; }
	#header-inner.text-center-logo #logo img { object-fit: contain; }
	html:not(.js) #header-inner.text-center-logo #search { opacity: 0; }
	#root .shopify-section-header #header-inner.text-center-logo > #logo { margin-left: auto; margin-right: auto; }
	#header-inner.text-center-logo #search, #header-inner.text-center-logo > .link-btn, #header-inner.text-center-logo > .search-compact:first-child { position: absolute; left: var(--l0ra); right: var(--lar0); top: 12px; bottom: 0; height: auto; }
	.js #search.compact { opacity: 0; }
	html:not(.search-compact-active) #header-inner.text-center-logo #search.compact, html:not(.search-compact-active) #header-inner #search.compact { position: absolute; top: -3000em; bottom: auto; }
	.search-compact-active:not(.search-compact-is-centered) #header-inner.text-center-logo > .link-btn { opacity: 0; }
	#header-inner.text-center-logo #search {
		width: 100%; max-width: var(--maw_lb);
		display: flex;
		align-items: center;
	}
	#header-inner.text-center-logo #search fieldset { position: relative; z-index: 2; }
	#header-inner.text-center-logo #nav-user { position: absolute; left: var(--lar0); right: var(--l0ra); top: 12px; bottom: 0; }
	#header-inner.text-center-logo #nav-outer { position: absolute; left: 0; right: 0; top: 12px; bottom: 0; /*padding-top: 6px;*/ border-left-width: 0; border-right-width: 0; }
	#header-inner.text-center-logo #nav-outer > * { max-width: 40%; }
	#header-inner.text-center-logo #nav-outer > [id*="nav"] { justify-content: flex-start; }

	[dir="ltr"] #search.text-end { margin-left: auto; }
	[dir="ltr"] #search.text-end:not(.compact) ~ * { margin-left: 0; }
	.search-compact-active #search.text-end ~ * li.search { display: none; }

	.t1sr #root #header-inner.text-center-logo > .link-btn .search-compact { display: flex; }

	.search-compact-active:not(.search-compact-is-centered) #header-inner.text-center-logo > .search-compact { display: none; }
	#header-inner.text-center-logo #search:not(.compact) ~ #nav-user li.search { display: none; }
}
@media only screen and (min-width: 761px) and (max-width: 1100px) {
	#header-inner.text-center-logo { --maw_la: 30vw; --maw_lb: min(calc(var(--search_w) + var(--dist_main)), 35vw); }
}
@media only screen and (min-width: 1001px) {
	html:not(.t1sh) #root #header-inner.hide-btn > .link-btn { border-right-width: 0; }
	#header-inner.hide-btn > .link-btn > a:first-child, .search-compact-handle-mobile #header-inner.hide-btn > .link-btn { display: none; }
	html:not(.t1sh, .t1sr) #header-inner.hide-btn > .link-btn { display: none; }
	#root #header-inner.hide-btn > .link-btn > * + * { margin-left: 0; margin-right: 0; }
	#header-inner.text-center-logo.hide-btn > .link-btn ~ #search.compact-handle ~ nav li.search { display: none; }
	#header-inner.hide-btn > .link-btn + .search-compact { display: flex; }
	.t1nn .shopify-section-header #nav-bar, .t1nn .shopify-section-header #nav, #nav-bar.desktop-hide, #nav.desktop-hide { display: none; }
	.has-inside-nav #header-inner.text-center-logo > .link-btn { display: none; }
}
@media only screen and (max-width: 1000px) {
	#header-inner > .link-btn { order: 1; }
	#header-inner > #logo { order: 2; }
	#header-inner > #search { order: 3; }
	#header-inner > #nav-user { order: 4; }
	.t1mn:not(.search-compact-handle) .shopify-section-header #header-inner > .link-btn, html:not(.search-compact-handle) .shopify-section-header #header-inner.t1mn > .link-btn { --dist_main: 0px; }
	.t1mn .shopify-section-header #header-inner > .link-btn > a:first-child, .shopify-section-header #header-inner.t1mn > .link-btn > a:first-child { display: none; }
	.t1mn .shopify-section-header #header-inner > .link-btn > a:first-child + *, .shopify-section-header #header-inner.t1mn > .link-btn > a:first-child + * { margin-left: 0; margin-right: 0; }
	html:not(.t1sh-mobile) #root .shopify-section-header #header-inner.t1mn > .link-btn { margin-right: 0; }
	html[dir="rtl"]:not(.t1sh-mobile) #root .shopify-section-header #header-inner.t1mn > .link-btn { margin-left: 0; }
}
@media only screen and (max-width: 760px) {
	:root {
		--header_mih: var(--header_mih_m);
	}
	.shopify-section-header { --maw_la: 40vw; }
	#header-inner:not(.text-center-mobile ) #logo:has(span), #header-inner.logo-text:not(.text-center-mobile) #logo { max-width: none; flex-shrink: 10; }
	#header-inner.text-center-mobile { --maw_a: 10vw; --nav_user_h: calc(var(--header_mih) - 24px); }
	#root .shopify-section-header #header-inner.text-center-mobile > #logo { margin-left: auto; margin-right: auto; text-align: center; }
	#header-inner.text-center-mobile #logo img { object-fit: contain; }
	.shopify-section-header #header-inner.text-center-mobile > .link-btn { position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; bottom: 0; height: auto; margin-top: 0; margin-bottom: 0; }
	.shopify-section-header #header-inner.text-center-mobile #nav-user { position: absolute; right: 0; left: 0; top: 0; bottom: 0; margin-top: 0; margin-bottom: 0; }
	.t1sh-mobile #root #header-inner > .link-btn .search-compact { display: flex; }
	#root #header-inner > .link-btn a:first-child i { display: none; }
	#header-inner { min-height: 36px; }

	.shopify-section-header:has(#header-inner.mobile-visible-search) { --mob_cl: calc(var(--search_mob_pd) * 2); }
	.shopify-section-header.has-mobile-visible-search { --mob_cl: calc(var(--search_mob_pd) * 2); }

	.shopify-section-header:has(#header-inner.mobile-visible-search) { margin-bottom: calc(var(--custom_top_search_h) + var(--mob_cl)); }
	.shopify-section-header.has-mobile-visible-search { margin-bottom: calc(var(--custom_top_search_h) + var(--mob_cl)); }

	/*.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)) { --mob_cl: var(--search_mob_pd); }
			.shopify-section-header.has-mobile-visible-search.no-bd-m { --mob_cl: var(--search_mob_pd); } 	*/

	.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-pd-t) { --mob_cl: var(--search_mob_pd); }
	.shopify-section-header.has-mobile-visible-search.no-pd-t { --mob_cl: var(--search_mob_pd); }

	/*.shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)):has(#search.no-pd-t) { --mob_cl: 0px; }
			.shopify-section-header.has-mobile-visible-search.no-bd-m.no-pd-t { --mob_cl: 0px; } */


	/*#header-inner.mobile-visible-search #search, .search-compact-active #root #header-inner.mobile-visible-search #search { top: auto; bottom: 0; }*/
	#header-inner.mobile-visible-search #search, .search-compact-active #root #header-inner.mobile-visible-search #search { top: 100%; }
	#root .shopify-section-header #header-inner.mobile-visible-search #nav-user > ul > li.search, #root .shopify-section-header #header-inner.mobile-visible-search > .link-btn .search-compact { display: none; }
	#root #header-inner > .link-btn > .mobile-hide:first-child + * { margin-left: 0; margin-right: 0; }
	/*.shopify-section-header #header-inner.text-center-mobile.mobile-visible-search > .link-btn, .shopify-section-header #header-inner.text-center-mobile.mobile-visible-search #nav-user { bottom: calc(var(--custom_top_search_h) + 24px); }*/
	#header-inner #search.compact-handle-mobile ~ nav li.search { display: none; }
}
@media only screen and (max-width: 400px) {
	html:not(.search-compact-handle, .has-mobile-visible-search) .shopify-section-header { --maw_la: 30vw; }
}
@media only screen and (max-width: 340px) {
	html:not(.search-compact-handle, .has-mobile-visible-search) .shopify-section-header { --maw_la: 25vw; }
}

#skip { position: fixed; top: 0; left: 0; right: 0; z-index: 10001; }
#skip ul { list-style: none; margin: 0; padding: 0; }
#skip a { display: block; position: absolute; left: -3000em; top: 0; min-width: 200px; padding: 14px 22px; background: var(--tertiary_bg_dark); color: var(--white); font-weight: var(--main_fw_strong); line-height: var(--btn_lh); text-align: center; text-decoration: none; white-space: nowrap; outline: none; }
#skip a:focus, #skip a:active { left: 0; }
#nav, #nav-bar {
	display: block; position: relative; z-index: 4; clear: both; width: 100%; min-height: var(--custom_top_nav_h); padding: 0; color: var(--custom_top_nav_fg); font-size: var(--main_fz); line-height: var(--btn_lh); text-align: inherit;
	align-self: flex-end;
}
#nav:not(.text-uppercase), #nav-bar:not(.text-uppercase) { text-transform: none; }
#nav:not(.text-justify, .have-text-justify) ul, #nav-bar:not(.text-justify, .have-text-justify) ul { min-width: 0; }
#nav:before, #nav-bar:before { border: 0 solid var(--custom_top_nav_bd); border-bottom-width: 1px; background: var(--custom_top_nav_bg); }
#nav ul, #nav-bar ul { list-style: none; margin-left: 0; margin-right: 0; margin-bottom: 0; padding: 0; }
#nav > ul, #nav-bar > ul { min-height: var(--custom_top_nav_h); margin: 0 calc(0px - var(--nav_dist)) 0 0; }
#nav > ul > li, #nav-bar > ul > li { position: static; margin: 0 var(--nav_dist) 0 0; }
#nav > ul > li.sub, #nav-bar > ul > li.sub { margin-right: 0; padding-right: var(--nav_dist); }
html:not([dir="rtl"]) #nav > ul > li.text-end, html:not([dir="rtl"]) #nav-bar > ul > li.text-end { margin-left: auto; }
#nav > ul > li:last-child:not(.show-all), #nav-bar > ul > li:last-child:not(.show-all), #nav > ul > li[data-index="1"]:not(.show-all), #nav-bar > ul > li[data-index="1"]:not(.show-all) { padding-right: 1px; }
#nav > ul > li > a, #nav-bar > ul > li > a { display: block; position: relative; z-index: 2; height: min(var(--custom_top_nav_h), 100%); margin: 0; color: var(--custom_top_nav_fg); font-weight: inherit; font-size: 1em; text-decoration: none; white-space: nowrap; cursor: pointer; }
#nav > ul > li > a[class*="overlay-"], #nav-bar > ul > li > a[class*="overlay-"] { --custom_top_nav_fg: var(--product_label_bg); }
#nav > ul > li > a span + *:not(span), #nav-bar > ul > li > a span + *:not(span) { margin-left: 6px; }
#nav > ul > li.disabled > a:not(.toggle), #nav-bar > ul > li.disabled > a:not(.toggle), #nav > ul > li > a.disabled:not(.toggle), #nav-bar > ul > li > a.disabled:not(.toggle) { opacity: .6; cursor: default; -ms-pointer-events: none; pointer-events: none; }
#nav > ul > li.inactive > a:not(.toggle), #nav-bar > ul > li.inactive > a:not(.toggle), #nav > ul > li > a.inactive:not(.toggle), #nav-bar > ul > li > a.inactive:not(.toggle) { cursor: default; -ms-pointer-events: none; pointer-events: none; }
#nav > ul > li > a.toggle, #nav-bar > ul > li > a.toggle { display: block; overflow: hidden; position: relative; left: 16px; top: -3000em; bottom: auto; width: 44px; height: min(var(--custom_top_nav_h), 100%); margin: 0 0 0 -44px; padding: 0; text-indent: -3000em; text-align: left; direction: ltr; }
#nav > ul > li > a.toggle:focus, #nav-bar > ul > li > a.toggle:focus { top: 0; }
#nav > ul > li.active > a, #nav-bar > ul > li.active > a, .shopify-section-header li.overlay-theme > a, #nav-top > ul > li > ul li.active > a { color: var(--custom_top_nav_fg_hover); font-weight: var(--main_fw_strong); }
.shopify-section-header ul ul li.overlay-theme > a { color: var(--custom_drop_nav_fg_hover); }
#nav > ul > li > a > i, #nav-bar > ul > li > a > i { display: block; font-size: 1.3em; }
.shopify-section-header li.search a { overflow: visible; }
#nav > ul > li > ul, #nav-bar > ul > li > ul { display: none; }
#nav > ul > a.close, #nav-bar > ul > a.close { display: none !important; position: absolute; top: 100%; z-index: -20; height: auto; background: var(--coal); opacity: .2; text-align: left; text-indent: -3000em; }
#nav > ul > li.empty-url > a:not(.toggle, .toggle-back), #nav-bar > ul > li.empty-url > a:not(.toggle, .toggle-back) { pointer-events: none; cursor: default; }
#nav .s1bx, #nav-bar .s1bx { line-height: var(--btn_lh); }
#nav-bar { z-index: 3; }
#nav-bar ~ #nav { display: none; }
#nav.plain:before, #nav-bar.plain:before { border-top-width: 1px; }
#nav.no-bd:before, #nav-bar.no-bd:before { border-width: 0; }
#nav.no-wide:before, #nav-bar.no-wide:before { left: 0; right: 0; width: auto; margin-left: 0; margin-right: 0; background: none; }
#nav.no-wide:after, #nav-bar.no-wide:after { content: ""; display: block; position: absolute; left: 50%; right: auto; top: 0; bottom: 0; z-index: -2; width: var(--100vw); margin: 0 0 0 calc(0px - var(--100vw) * 0.5); background: var(--custom_top_nav_bg); }
#header-inner #nav-bar:not(.fixed), #header-inner #nav:not(.fixed) {
	--custom_top_nav_fg: var(--custom_top_main_fg);
	align-self: center;
}
#header-inner #nav-bar:not(.fixed) > ul > li, #header-inner #nav:not(.fixed) > ul > li { border-bottom: 12px solid rgba(0,0,0,0); }
#header-inner #nav-bar:not(.fixed):before, #header-inner #nav:not(.fixed):before { display: none; }
#nav-user {
	display: block; position: relative; top: 0; bottom: 0; z-index: 5; float: right; margin: 0 0 12px auto; font-size: var(--main_fz);
	display: flex;
	align-items: center;
	flex-shrink: 0;
}

#nav-user > ul { list-style: none; margin: 0 -24px 0 0; padding: 0; }
#nav-user > ul > li { position: relative; z-index: 2; margin: 0 24px 0 0; }
#search:not(.compact) ~ #nav-user > ul > li.search { display: none; }
#nav-user > ul > li:has(img) { flex-shrink: 0; }
#nav-user > ul > li.has-img, #nav-user > ul > li > a i, #nav-user > ul > li > label i { flex-shrink: 0; }
#nav-user > ul > li > a { display: block !important; /*overflow: hidden;*/ position: relative; z-index: 2; max-width: var(--maw_a); min-height: 44px; margin: 0; padding: 0; color: inherit; font-weight: inherit; font-size: 1em; line-height: 44px; text-decoration: none; text-overflow: ellipsis; white-space: nowrap; cursor: pointer; }
#nav-user > ul > li > a > span { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
#nav-user > ul > li.link-btn > a { max-width: none; min-height: 0; padding: min(var(--btn_pv), 20px) var(--btn_ph); font-weight: var(--btn_fw); font-size: var(--btn_fz); line-height: var(--btn_lh); }
#nav-user > ul > li > a:after, #nav-top > ul > li > a:after {
	content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 100%; min-width: 44px; height: 44px; margin: -22px 0 0;
	transform: translateX(-50%);
}
#nav-user > ul > li.cart > a { overflow: visible; }
#nav-user > ul > li > a img, #nav-user > ul > li > img {
	display: block; height: 100% !important; max-height: var(--nav_user_h); border-radius: 0;
	object-fit: contain;
}
#nav-user > ul > li.lang > a img { max-height: none; }
#nav-user > ul > li > a ~ a.toggle { display: block; overflow: hidden; position: absolute; right: -5px; top: 0; bottom: 0; z-index: 9; width: 20px; text-align: left; text-indent: -3000em; direction: ltr; }
#nav-user > ul > li > a i, #nav-user > ul > li > label i { display: block; position: relative; float: left; margin: 0 .5px; font-size: /*1.3571428571em*/ var(--size_20_f); line-height: 45px; }
#nav-user > ul > li > a i + span, #nav-user > ul > li > label i + span { padding-left: 7px; }
#nav-user > ul > li > a i span { display: block; position: absolute; left: auto; right: calc(0px - var(--s) * 0.5210084034); top: 50%; z-index: 9; min-width: var(--s); height: var(--s); margin-top: calc(0px - var(--s)); border-radius: var(--s); color: var(--custom_top_main_link_text); font-family: var(--main_ff); font-size: var(--size_12); line-height: var(--s); text-align: center; text-indent: 0; letter-spacing: var(--main_ls); --s: 21px; }
#root #nav-user > ul > li > a i span:before { border-radius: var(--s); }
#root #nav-user > ul > li > a i span i { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; margin: 0; font-size: calc(var(--s) * 0.4); line-height: var(--s); }
#nav-user > ul > li > a i.icon-youtube { font-size: 1em; }
#nav-user > ul > li > a i.icon-facebook { font-size: 1.0714285714em; }
#nav-user > ul > li > a i.icon-user { font-size: var(--size_20_f); }
#nav-user > ul > li > a i[class*="icon-cart"] { font-size: var(--size_22_f); line-height: 43px; }
#nav-user > ul > li.user-login { position: static; }
#nav-user > ul > li > form { display: none; }
#root #nav-user > ul > li > form ~ a.toggle { display: none !important; }
.shopify-section-header > .text-end + .text-end, #header > .text-end + .text-end { margin-left: 0; }
#nav-top { position: relative; top: 0; z-index: 99; width: 100%; max-width: var(--ghw); min-height: var(--custom_top_up_h); margin: 0 auto; padding: calc(var(--main_fz) * 0.2857142857) 0; color: var(--custom_top_up_fg); font-size: var(--main_fz); --cols: 8px; }
#nav-top:before { background: var(--custom_top_up_bg); }
#nav-top > ul { position: relative; z-index: 3; list-style: none; margin: 0 -24px 0 0; padding: 0; font-weight: var(--main_fw); text-align: inherit; }
#root #nav-top > ul.text-start { position: absolute; left: 0; right: auto; top: 0; margin-right: auto; }
#root #nav-top > ul.text-end { position: absolute; right: 0; left: auto; top: 0; margin-left: auto; }
#root #nav-top > ul.text-center {
	position: absolute; left: 50%; right: auto; top: 0; margin-left: auto; margin-right: auto;
	transform: translateX(-50%);
}
#nav-top > ul > li { position: relative; z-index: 2; margin: 0 24px 0 0; white-space: nowrap; }
#nav-top > ul > li > a { display: block; position: relative; z-index: 2; min-height: calc(var(--main_lh) * var(--main_fz)); margin: 0; padding: 0; color: inherit; font-weight: inherit; font-size: 1em; text-decoration: none; text-overflow: ellipsis; white-space: nowrap; cursor: pointer; }
#nav-top > ul.l4us > li > a { display: inline; min-height: 0; text-overflow: inherit; white-space: normal; }
#nav-top > ul > li.sub > a, #nav-user > ul > li.sub > a, .l4dr li.sub > a { padding-right: calc(var(--main_fz) * 1.1428571429); }
#nav-top > ul > li.sub > a:before, #nav-user > ul > li.sub > a:before, .l4dr li.sub > a:before { content: "\e904"; left: auto; font-size: 0.4285714286em; }
#nav-user > ul > li.sub > a:before { padding: 0 1px; }
#nav-top > ul > li.sub.toggle > a:before, #nav-user > ul > li.sub.toggle > a:before, .l4dr li.sub.toggle > a:before { transform: rotate(180deg); }
#nav-top > ul > li > a i, #nav-top > ul > li > a img { display: block; position: relative; z-index: 2; max-width: none !important; font-size: 1; line-height: calc(var(--main_lh) * var(--main_fz)); }
/*#nav-top > ul > li > a i + span, #nav-top > ul > li > a img + span, #nav-top > ul > li svg + span { margin-left: 8px; }*/
#nav-top li svg/*, .l4ch svg*/ { width: auto; height: var(--main_fz); flex-shrink: 0; }
#nav-top > ul > li > a i.icon-star { top: -.12em; margin: 0 2px 0 5px; color: var(--alert_valid); font-size: 1.225em; }
#nav-top > ul > li > a i.icon-text-size { font-size: 1.3076923077em; }
#nav-top > ul > li > a i.icon-envelope { font-size: 0.8461538462em; }
#nav-top > ul > li > a i.icon-twitter { font-size: 1.0769230769em; }
#nav-top > ul > li > a i.icon-instagram, #nav-top > ul > li > a i.icon-pinterest { font-size: 1.2857142857em; }
#nav-top > ul > li > a i.icon-youtube { font-size: 1.1538461538em; }
#nav-top > ul > li > a i.icon-facebook, #nav-top > ul > li > a i.icon-vimeo, #nav-top > ul > li > a i.icon-wechat, #nav-top > ul > li > a i.icon-weibo { font-size: 1.2307692308em; }
#nav-top > ul > li > a img { display: block; position: relative; z-index: 2; /**/ object-fit: contain; }
#nav-top > ul > li > a i.icon-trustpilot, #nav-top > ul > li > a img { top: -.1em; margin: 0 2px 0 5px; border-radius: 0; color: var(--lime); font-size: 1.25em; }
#nav-top > ul > li.lang > a img, #root .l4dr img { width: auto !important; min-width: 17px !important; height: 12px !important; margin: 0; }
#nav-top > ul > li > a img { display: block; top: 0; }
#nav-top > ul > li > a i.icon-trustpilot:after { content: "\e93e"; top: 0; z-index: 2; margin: 0; color: var(--black_static); font-size: 1em; line-height: calc(var(--main_lh) * var(--main_fz)); /*line-height: 35px;*/ opacity: .53; }
#nav-top > ul > li > a i:before { position: relative; z-index: 2; }
#nav-top > ul > li > a i.icon-trustpilot + span { font-size: 0.9166666667em; font-family: "Helvetica Neue", -apple-system, system-ui, BlinkMacSystemFont, Helvetica, Arial, sans-serif; font-weight: var(--main_fw_strong); }
#nav-top path { fill: var(--custom_top_up_fg); }
#nav-top img ~ span, #nav-top picture ~ span, #nav-top figure ~ span, #nav-top svg ~ span, #nav-top i ~ span { border: 0 solid rgba(0,0,0,0); border-left-width: var(--cols); }
#nav-top > ul.text-center { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 2; }
#nav-top > ul > li > ul, #nav-user > ul > li > ul, #nav-top > ul > li > form { display: none; }
#nav-top > ul.l4us { min-width: 0; }
#nav-top > .l4us a { color: var(--custom_top_up_fg_hover); }
#nav-top > ul.l4us li:before { top: 0; line-height: calc(var(--main_lh) * var(--main_fz)); }
#nav-top > ul.l4us em { display: inline; }
#nav-top > ul.l4us li { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
#nav-top .l4us.slider { z-index: 7; max-width: 100%; min-width: 0; height: 100%; margin-right: 24px; margin-left: 24px; }
#root #nav-top .l4us.slider:first-child { margin-left: 0; margin-right: 0; }
#nav-top > .l4us .outer { display: block; position: relative; }
#nav-top > .l4us .outer .inner, #nav-top > .l4us .outer .inner > span, #nav-top > .l4us .outer .inner img ~ span, #nav-top > .l4us .outer .inner svg ~ span, #nav-top > .l4us .outer .inner .cols span { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
#nav-top > .l4us .outer .inner > span.strong { display: inline; }
#nav-top > .l4us .outer .inner-text { display: block; position: absolute; left: 0; top: 0; z-index: -10; white-space: nowrap; opacity: 0; }
#nav-top > .l4us .longer .outer { display: flex; }
#nav-top > .l4us .longer a.linked { display: block; padding-left: 6px; text-overflow: inherit; white-space: nowrap; }
#nav-top > .l4us .outer .inner:has(img), #nav-top > .l4us .outer .inner:has(svg) {
	display: flex;
	align-items: center;
}
#nav-top > .l4us .cols { max-width: 100%; }
#nav-top .l4us img, #nav-top .l4us picture, #nav-top .l4us figure, #nav-top .l4us svg, #nav-top .l4us i,
#nav-top > .l4us .cols > svg, #nav-top > .l4us .cols figure, #nav-top > .l4us .cols img, #nav-top > .l4us .cols i { flex-shrink: 0; }
#nav-top > .l4us .has-img .outer .inner {
	display: flex;
	align-items: center;
}
#nav-top > ul.l4us li:has(img):before, #nav-top > ul.l4us li:has(svg):before { top: 50%; margin-top: calc(0px - var(--main_lh) * var(--main_fz) * 0.5); }
#nav-top > ul.l4us li.has-img:before { top: 50%; margin-top: calc(0px - var(--main_lh) * var(--main_fz) * 0.5); }
#nav-top > ul.l4us ~ ul:not(.l4us) { padding-left: 24px; }
@media only screen and (min-width: 761px) { /* 760 + */
	#nav-top > .l4us.slider:not(.slider-single) .longer a.linked { display: none; }
	#nav-top .l4us.slider-in-header:not(.slider-single) .swiper-slide { width: auto; }
	#nav-top .l4us.slider-in-header:not(.slider-single) .swiper-outer { position: relative; z-index: 2; }
	#nav-top .l4us.slider-in-header:not(.slider-single) .swiper-outer:before { content: ""; display: block; position: absolute; right: 0; top: 0; bottom: 0; z-index: 9; width: 10%; background: linear-gradient(to right, rgba(0,0,0,0) 0%, var(--custom_top_up_bg) 100%); }
}
#nav-top > ul:last-child { margin-left: auto; }
.shopify-section-header > .close, #header > .close { display: none; top: 100%; bottom: auto; height: 100000px; z-index: 3; background: var(--coal); text-align: left; text-indent: -3000em; direction: ltr; opacity: .2; }
#header > .link-btn, #header-inner > .link-btn { margin: 0 0 12px; }
#header > .link-btn a, #header-inner > .link-btn a { min-height: 0; margin: 0; padding-top: min(var(--btn_pv), 20px); padding-bottom: min(var(--btn_pv), 20px); color: var(--custom_top_main_link_text); font-size: var(--btn_fz); /*line-height: 45px;*/ white-space: nowrap; }
#header > .link-btn a.inv, #header-inner > .link-btn a.inv { --secondary_bg_btn: var(--custom_top_main_link_bg); --btn_bc_h: var(--custom_top_main_link_bg); }
#root #nav-user > ul > li > a i span, #header-inner > .link-btn a, #root #nav-user > ul > li.link-btn > a { color: var(--custom_top_main_link_text); }
#header-inner > .link-btn a.inv, #root #nav-user > ul > li.link-btn > a.inv { color: var(--custom_top_main_link_bg); }
#root #nav-user > ul > li > a i span:before, #header-inner > .link-btn a:before, #nav-user > ul > li.link-btn > a:before { border-color: var(--custom_top_main_link_bg); background: var(--custom_top_main_link_bg); }
#header-inner > .link-btn a.inv:before, #nav-user > ul > li.link-btn > a.inv:before { border-color: var(--custom_top_main_link_bg); }
#search {
	position: relative; z-index: 5; max-width: calc(var(--search_w) + var(--dist_main)); margin: 0 0 12px; color: var(--custom_top_search_fg);
	flex-grow: 3;
}
#root .text-justify #header-inner #search, #root .text-end #header-inner #search, #root .text-start #header-inner #search, #root .text-center #header-inner #search { margin-left: 0; margin-right: 0; }
#search input { height: var(--custom_top_search_h); padding-right: 55px; border-color: var(--custom_top_search_bd); background: var(--custom_top_search_bg); color: var(--custom_top_search_fg); }
#search.has-text input { padding-right: 75px; }
#search input::placeholder { color: var(--custom_top_search_pl); opacity: 1; }
#search button { display: block; overflow: hidden; position: absolute; right: 0; bottom: 0; top: 0; width: 55px; min-width: 0; min-height: 0; margin: 0; padding: 0; box-shadow: none; border-radius: 0; background: none; color: var(--custom_top_search_fg); font-size: var(--size_20); text-indent: -3000em; text-align: left; direction: ltr; }
#root #search button:before { content: "\e91d"; box-shadow: none; border: 0; background: none; outline: 0; }
#search.text-start { margin-left: 0; margin-right: 32px; }
#search > div, #search fieldset > div { display: none; }
#search > a.toggle, #search p > a.search-back, #search .clear-toggle { display: none; }
.shopify-section-header.fixed { position: fixed; left: 0; right: 0; top: 0; margin: 0; }
.shopify-section-header.fixed #nav-top { display: none; }
#distance-counter { position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; z-index: -1; width: 10px; height: 10px; }
#header .l4al { visibility: hidden; position: absolute; left: var(--lar0); right: var(--l0ra); top: min(calc(100% + 14px), calc(100% + var(--rpp))); z-index: 9; opacity: 0; }
#background { overflow: hidden; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 0; margin: 0; }
#background { -webkit-clip-path: inset(0); clip-path: inset(0); }
#background img, #background iframe, #background video, #background picture { display: block; position: fixed; left: 0; right: 0; top: 0; bottom: 0; }
#background.static img, #background.static iframe, #background.static video, #background.static picture { position: absolute; }
#content, .shopify-section-footer { position: relative; z-index: 3; width: 100%; max-width: var(--glw); margin: 0 auto; }
#content #background { position: absolute; z-index: -1; margin-bottom: 0; }
#content {
	padding-top: var(--content_p);
	flex-grow: 3;
}
#content > [id*="shopify-section"]:last-child, #content > .shopify-section-footer:last-child { margin-bottom: 0; }
#content[class*="align-center"] > *:last-child, #content[class*="align-center"] > [id*="shopify-section"]:last-child { margin-bottom: var(--main_mr); }
#content > *:last-child, #content > [id*="shopify-section"]:last-child > *:last-child { margin-bottom: 60px; }
#content.fullheight > .shopify-section-footer:last-child, #content.fullheight > .align-center { margin-top: auto; }
#content > .cols:last-child, #content > form:last-child, #content > [id*="shopify-section"]:last-child > .cols:last-child, #content > [id*="shopify-section"]:last-child > form:last-child { margin-bottom: 34px; }
#content > .n6br:first-child, #content > [id*="shopify-section"]:first-child > .n6br:first-child, #content > [class*="shopify-section"]:first-child > .n6br:first-child { margin-top: calc(0px - var(--content_p) + 10px); }
#content > .m6fr.wide:first-child, #content > .m6wd:first-child, #content > [id*="shopify-section"]:first-child > .m6fr.wide:first-child, #content > [id*="shopify-section"]:first-child > .m6wd:first-child, #content > [id*="shopify-section"]:first-child > .l4ft.fullwidth[style*="--dist_a: 0"]:first-child, #content > [id*="shopify-section"]:first-child > article:first-child > .l4ft.fullwidth:first-child, #content > .l4ft.fullwidth:first-child { margin-top: calc(0px - var(--content_p)); }
#content > .f8nw.wide:last-child, #content > [id*="shopify-section"]:last-child > .f8nw.wide:last-child { margin-bottom: 0; }
.shopify-section-footer { position: relative; z-index: 2; margin-top: auto; margin-bottom: var(--main_mr); padding: 0 var(--rpp) .1px; border: 0 solid rgba(0,0,0,0); color: var(--custom_footer_fg); line-height: 1.9285714286; --sp: 30px; --main_mr_f: 16px; }
#root .shopify-section-footer { padding-left: 0; padding-right: 0; }
.shopify-section-footer:before { background: var(--custom_footer_bg); }
.shopify-section-footer > nav {
	margin-left: -40px; padding-top: 57px; padding-bottom: 16px;
	flex-wrap: wrap;
}
.shopify-section-footer > nav > * { position: relative; z-index: 2; width: calc(16.666% - 8px * 3 / 4); max-width: calc(16.666% - 8px * 3 / 4); padding: 0 0 30px; border-left: 40px solid rgba(0,0,0,0); font-weight: var(--main_fw); }
.shopify-section-footer > nav > .align-middle { align-self: center; }
.shopify-section-footer > nav > .m6cn { width: 426px; max-width: 426px; min-width: 326px; margin-left: 0; }
.shopify-section-footer > nav .m6cn figure { position: absolute; left: 0; right: 0; bottom: -16px; z-index: -1; margin-bottom: 0; border-radius: 0; }
.shopify-section-footer > nav .m6cn figure.inline { position: relative; left: 0; right: 0; top: 0; bottom: 0; z-index: auto; margin-bottom: var(--main_mr); }
.shopify-section-footer > nav .m6cn figure.static { left: 0; right: 0; bottom: 0; }
.shopify-section-footer > nav .m6cn[class*="w"] figure { left: -40px; right: -40px; }
.shopify-section-footer > nav .m6cn figure * { border-radius: 0; }
.shopify-section-footer > nav .m6cn figure img { max-width: 320px !important; }
.shopify-section-footer > nav > .strong, .shopify-section-footer > nav > .m6cn { width: calc(33% - 8px * 3 / 4); max-width: calc(33% - 8px * 3 / 4); }
.shopify-section-footer > nav ul { list-style: none; padding: 0; }
.shopify-section-footer > nav ul ul { margin: 0; padding-left: 20px; }
.shopify-section-footer > div { position: relative; z-index: 2; padding: var(--footer_bottom_p) 0 max(0.1px, calc(var(--footer_bottom_p) - var(--main_mr_f))); color: var(--custom_footer_fg_bottom); /*font-size: var(--size_14_f);*/ }
.shopify-section-footer > div:before { border-top: 1px solid var(--custom_footer_bd_bottom); background: var(--custom_footer_bg_bottom); }
.shopify-section-footer > div figure { margin-right: 10px; margin-bottom: var(--main_mr); }
.shopify-section-footer > div figure, .shopify-section-footer > div figure * { border-radius: 0; }
.shopify-section-footer > div p { max-width: 50%; margin-right: var(--sp); }
.shopify-section-footer > div .l4pm { margin-left: auto; padding-left: 10px; --dist: 32px; }
.shopify-section-footer h1, .shopify-section-footer h2, .shopify-section-footer h3, .shopify-section-footer h4, .shopify-section-footer h5, .shopify-section-footer h6 { position: relative; z-index: 2; /*margin: 0 0 13px;*/ color: inherit; }
.shopify-section-footer figure, .shopify-section-footer p, .shopify-section-footer ul, .shopify-section-footer .widget { --main_mr: var(--main_mr_f); }
.shopify-section-footer .widget { min-height: calc(var(--main_fz) * var(--main_lh)); }
.shopify-section-footer figure a { width: auto; }
.shopify-section-footer .l4pm { margin-bottom: max(0px, calc(var(--main_mr) - 10px)); }
.shopify-section-footer nav ul:not(.l4sc, .l4pm, .l4cn) li:not(:last-child) { margin-bottom: var(--footer_li_dist); }
@media only screen and (min-width: 761px) { /* 760 + */
	.shopify-section-footer h1:first-child, .shopify-section-footer h2:first-child, .shopify-section-footer h3:first-child, .shopify-section-footer h4:first-child, .shopify-section-footer h5:first-child, .shopify-section-footer h6:first-child { font-size: var(--main_h_small); }
	.shopify-section-footer > nav .m6cn p { max-width: 260px; }
	.shopify-section-footer > nav.text-center p { margin-left: auto; margin-right: auto; }
	#root .shopify-section-footer > nav.w15 > *, #root .shopify-section-footer > nav > .w15 { width: 15%; min-width: 15%; max-width: 15%; }
	#root .shopify-section-footer > nav.w16 > *, #root .shopify-section-footer > nav > .w16 { width: 16.66666666666%; min-width: 16.66666666666%; max-width: 16.66666666666%; }
	#root .shopify-section-footer > nav.w20 > *, #root .shopify-section-footer > nav > .w20 { width: 20%; min-width: 20%; max-width: 20%; }
	#root .shopify-section-footer > nav.w25 > *, #root .shopify-section-footer > nav > .w25 { width: 25%; min-width: 25%; max-width: 25%; }
	#root .shopify-section-footer > nav.w30 > *, #root .shopify-section-footer > nav > .w30 { width: 30%; min-width: 30%; max-width: 30%; }
	#root .shopify-section-footer > nav.w33 > *, #root .shopify-section-footer > nav > .w33 { width: 33.3333333333%; min-width: 33.3333333333%; max-width: 33.3333333333%; }
	#root .shopify-section-footer > nav.w40 > *, #root .shopify-section-footer > nav > .w40 { width: 40%; min-width: 40%; max-width: 40%; }
	#root .shopify-section-footer > nav.w50 > *, #root .shopify-section-footer > nav > .w50 { width: 50%; min-width: 50%; max-width: 50%; }
	#root .shopify-section-footer > nav.w66 > *, #root .shopify-section-footer > nav > .w66 { width: 66.6666666666%; min-width: 66.6666666666%; max-width: 66.6666666666%; }
	.shopify-section-footer [class*="w"][class*="0"], .shopify-section-footer [class*="w"][class*="5"] { justify-content: flex-start; }
	.shopify-section-footer [class*="w"][class*="0"] form, .shopify-section-footer [class*="w"][class*="5"] form { width: 100%; }

	.shopify-section-footer .l4pm { flex-shrink: 10000; }
}
.shopify-section-footer form { width: 100%; max-width: calc(var(--main_fz) * 21.4285714286); }
.shopify-section-footer .text-center form { margin-left: auto; margin-right: auto; }
.shopify-section-footer form p + .check { margin-top: calc(0px - var(--main_mr) + 8px); }
.shopify-section-footer .check { margin-bottom: calc(var(--main_mr) * 0.75); }
.shopify-section-footer .check label { color: inherit; text-align: var(--text_align_start); }
.shopify-section-footer .link-btn a, .shopify-section-footer button { color: var(--custom_footer_link_text); }
#root .shopify-section-footer .link-btn a.inv, #root .shopify-section-footer button.inv { color: var(--custom_footer_link_bg); --btn_bc_h: var(--custom_footer_link_bg); }
.shopify-section-footer .link-btn a:before, .shopify-section-footer button:before { border-color: var(--custom_footer_link_bg); background: var(--custom_footer_link_bg); }
.shopify-section-footer nav ul img { display: inline-block; position: relative; top: -.0125em; margin-right: 4px; border-radius: 0; }
.shopify-section-footer .l4us a { color: var(--secondary_bg); text-decoration: underline; }
.shopify-section-footer hr { margin: 0; border-color: var(--black); opacity: .12; }
.shopify-section-footer > nav + hr { margin-top: -44px; }
.shopify-section-footer hr + * { margin-top: 40px; padding-top: 0; }
#totop { position: fixed; left: var(--lar0); right: var(--l0ra); bottom: var(--rpp); z-index: 7; margin: 0; padding: 0 var(--rpp) var(--root_pb); }
#totop a { display: block; overflow: hidden; position: relative; z-index: 2; width: 44px; height: 44px; border-radius: var(--btn_br); background: var(--secondary_bg); color: var(--secondary_btn_text); font-size: 8px; text-indent: -3000em; text-align: left; direction: ltr; }
#totop a:after { content: "\e908"; }
#cookie-bar { position: fixed; left: 0; right: 0; bottom: 0; z-index: 7; width: auto; margin: 0; padding: 0 var(--rpp); --mih: 68px; }
html:not(.cookie-on) #cookie-bar { visibility: hidden; opacity: 0; pointer-events: none; }
#cookie-inner { position: relative; z-index: 2; width: 100%; max-width: var(--glw); min-height: var(--mih); margin-left: auto; margin-right: auto; padding-top: 8px; padding-bottom: .1px; }
#cookie-bar:before { box-shadow: 0 -2px 10px rgba(0,0,0,.06); background: var(--white); }
#cookie-bar p { margin-bottom: 8px; }
#cookie-bar .icon-cookie { display: block; position: relative; margin: 0 14px 8px 0; color: var(--gray_text); font-size: 34px; line-height: 1em; }
#cookie-bar .link-btn { margin-top: 0; margin-bottom: 0; margin-left: auto; padding-left: 16px; /*--btn_dist: 32px;*/ }
#cookie-bar .link-btn a { min-width: auto; margin-bottom: 8px; /*white-space: nowrap;*/ }
.cookie-off #cookie-bar, .cookie-toggle #cookie-bar { transform: translateY(110%); }
.cookie-on { --root_pb: var(--cookie_h); }
.shopify-section-announcement-bar { display: none; }
body > *:not(#root, .fancybox__container) button:before { display: none; }
#root > .shopify-section:not(.shopify-section-header, .shopify-section-footer, [class*="shopify-section-announcement-bar"]) { width: 100%; }

/*.t1as {}*/
.t1as.t1pl #root { min-height: 100vh !important; padding-top: 0; }
@media only screen and (min-width: 761px) {
	/*.t1as {}*/
	.t1as.t1pl #content, .t1as #content { position: static; max-width: none; margin-top: 0; margin-bottom: 0; padding-right: calc(50% + var(--rpp)); }
	.t1as #background, .t1as #content #background { position: absolute; left: 50%; right: 0; width: auto; margin: 0; }
	.t1as .m6fr.wide.s4wi { margin-left: var(--rpn); margin-right: var(--rpn); }
	.t1as .m6fr.wide.s4wi article { padding-left: var(--rpp); padding-right: var(--rpp); }
	.t1as .m6fr.wide article > figure, .t1as .m6fr.wide article:before, .t1as .m6wd:before, .t1as .m6wd .background, .t1as .f8nw.wide:before, .t1as .f8nw.wide .background, .t1as .l4us.wide:before, .t1as .m6tb > nav:before, .t1as .m6wd:before, .t1as .m6bx.wide:before { left: var(--rpn); right: var(--rpn); width: auto; margin-left: 0; }
	.t1as .l4us.wide:before { transform: translateX(-32px); }
	.t1as #background img, .t1as #background iframe, .t1as #background video, .t1as #background picture { left: 50%; width: 50% !important; }
}
@media only screen and (min-width: 761px) and (max-width: 90em) {
	.t1as .m6fr.wide article, .t1as .m6fr.wide .swiper-slide article { padding-left: var(--rpp); padding-right: var(--rpp); }
}

/*.t1pl {}*/
.t1pl #root { min-height: 100vh; padding-bottom: .1px; }
.t1pl #content { padding-left: 0; padding-right: 0; }
.t1pl:not(.t1as) #content { margin-top: auto; margin-bottom: auto; padding: 0; }
.t1as #content.align-center { margin-top: auto; margin-bottom: auto; }

#nav > ul, .l4cl.hr { scrollbar-width: thin; }
#nav > ul::-webkit-scrollbar, .l4cl.hr::-webkit-scrollbar { width: 6px; height: 6px; }
#nav > ul::-webkit-scrollbar-track, .l4cl.hr::-webkit-scrollbar-track { background: none; }
#nav > ul::-webkit-scrollbar-thumb, .l4cl.hr::-webkit-scrollbar-thumb { background: var(--alto); }

#nav-top form, .l4dr ul { scrollbar-width: thin; }
#nav-top form::-webkit-scrollbar, .l4dr ul::-webkit-scrollbar { width: 6px; height: 6px; }
#nav-top form::-webkit-scrollbar-track, .l4dr ul::-webkit-scrollbar-track { background: none; }
#nav-top form::-webkit-scrollbar-thumb, .l4dr ul::-webkit-scrollbar-thumb { background: var(--custom_bd); }


html.no-sticky { --sticky_offset: 0px !important; --sticky_offset_m: 0px !important; }


.has-first-m6fr-wide #root #nav-bar > ul > li > a, .has-first-m6fr-wide #root #nav > ul > li > a { color: var(--custom_top_nav_fg); }




html:not(.has-first-m6fr-wide) .shopify-section-header:has(#nav.no-wide, #nav-bar.no-wide):before { border-width: 0; }
html:not(.has-first-m6fr-wide) .shopify-section-header:has(#nav.no-wide, #nav-bar.no-wide) #header-outer:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: -1; border-bottom: 1px solid var(--custom_top_nav_bd); }

html:not(.has-first-m6fr-wide) .shopify-section-header.has-no-wide:before { border-width: 0; }
html:not(.has-first-m6fr-wide) .shopify-section-header.has-no-wide #header-outer:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: -1; border-bottom: 1px solid var(--custom_top_nav_bd); }


/*! Columns --------- */
.cols { display: block; position: relative; margin-left: calc(0px - var(--cols)); --cols: 16px; }
.cols > * { display: block; position: relative; clear: none; float: right; border: 0 solid rgba(0,0,0,0); border-left-width: var(--cols); }
.cols > *:first-child { float: left; }
.cols > *:first-child:last-child:not([class*="align"]) { float: none; width: 100%; }
@media only screen and (min-width: 1001px) {
	.cols > .desktop-hide:first-child + *:last-child { float: none; width: 100%; }
}
.cols > .w10, #root .m6ac > .w10 { width: 10%; max-width: none; }
.cols > .w12, #root .m6ac > .w12 { width: 12.5%; max-width: none; }
.cols > .w14, #root .m6ac > .w14 { width: 14.2857142857%; max-width: none; }
.cols > .w15, #root .m6ac > .w15 { width: 15%; max-width: none; }
.cols > .w16, #root .m6ac > .w16 { width: 16.66666666666%; max-width: none; }
.cols > .w20, #root .m6ac > .w20 { width: 20%; max-width: none; }
.cols > .w25, #root .m6ac > .w25 { width: 25%; max-width: none; }
.cols > .w30, #root .m6ac > .w30 { width: 30%; max-width: none; }
.cols > .w33, #root .m6ac > .w33 { width: 33.33333333333%; max-width: none; }
.cols > .w35, #root .m6ac > .w35 { width: 35%; max-width: none; }
.cols > .w36, #root .m6ac > .w36 { width: 36%; max-width: none; }
.cols > .w40, #root .m6ac > .w40 { width: 40%; max-width: none; }
.cols > .w42, #root .m6ac > .w42 { width: 42%; max-width: none; }
.cols > .w45, #root .m6ac > .w45 { width: 45%; max-width: none; }
.cols > .w50, #root .m6ac > .w50 { width: 50%; max-width: none; }
.cols > .w55, #root .m6ac > .w55 { width: 55%; max-width: none; }
.cols > .w58, #root .m6ac > .w58 { width: 58%; max-width: none; }
.cols > .w60, #root .m6ac > .w60 { width: 60%; max-width: none; }
.cols > .w64, #root .m6ac > .w64 { width: 64%; max-width: none; }
.cols > .w66, #root .m6ac > .w66 { width: 66.66666666666%; max-width: none; }
.cols > .w70, #root .m6ac > .w70 { width: 70%; max-width: none; }
.cols > .w75, #root .m6ac > .w75 { width: 75%; max-width: none; }
.cols > .w80, #root .m6ac > .w80 { width: 80%; max-width: none; }
.cols > .w85, #root .m6ac > .w85 { width: 85%; max-width: none; }
.cols > .w90, #root .m6ac > .w90 { width: 90%; max-width: none; }
.cols > .w95, #root .m6ac > .w95 { width: 95%; max-width: none; }
.cols > .w100, #root .m6ac > .w100 { width: 100%; max-width: none; }
.cols:not(.align-middle) > h1, .cols:not(.align-middle) > h2, .cols:not(.align-middle) > h3, .cols:not(.align-middle) > h4, .cols:not(.align-middle) > h5, .cols:not(.align-middle) > h6, .cols:not(.align-middle) > p { align-self: baseline; }
.cols > .link-btn { margin-top: 0; }
.cols > h1 + p, .cols > h2 + p, .cols > h3 + p, .cols > h4 + p, .cols > h5 + p, .cols > h6 + p, .cols > p:first-child:last-child { margin-bottom: var(--main_mr_h); }
.cols > h1 + p a, .cols > h2 + p a, .cols > h3 + p a, .cols > h4 + p a, .cols > h5 + p a, .cols > h6 + p a { white-space: nowrap; }
.cols.align-middle > h1 + .link-btn, .cols.align-middle > h2 + .link-btn, .cols.align-middle > h3 + .link-btn, .cols.align-middle > h4 + .link-btn, .cols.align-middle > h5 + .link-btn, .cols.align-middle > h6 + .link-btn { top: 3px; }
.cols + h1, .cols + h2, .cols + h3, .cols + h4, .cols + h5, .cols + h6, .cols + .m6ac, .cols + .m6fr { margin-top: 16px; }
.cols.aside { padding-right: calc(var(--aside) + var(--cols)); --aside: 310px; }
.cols.aside > * { width: 100%; }
.cols.aside > aside { width: calc(var(--aside) + var(--cols)); margin-right: calc(0px - var(--aside) - var(--cols)); }

.cols.b30 { --cols: 30px; }
.cols.b50 { --cols: 50px; }
.cols.b75 { --cols: 75px; }

.width-10 { --width: 10%; }
.width-12 { --width: 12.5%; }
.width-14 { --width: 14.2857142857%; }
.width-15 { --width: 15%; }
.width-16 { --width: 16.66666666666%; }
.width-20 { --width: 20%; }
.width-25 { --width: 25%; }
.width-30 { --width: 30%; }
.width-33 { --width: 33.33333333333%; }
.width-35 { --width: 35%; }
.width-36 { --width: 36%; }
.width-40 { --width: 40%; }
.width-42 { --width: 42%; }
.width-45 { --width: 45%; }
.width-50 { --width: 50%; }
.width-55 { --width: 55%; }
.width-58 { --width: 58%; }
.width-60 { --width: 60%; }
.width-64 { --width: 64%; }
.width-66 { --width: 66.66666666666%; }
.width-70 { --width: 70%; }
.width-75 { --width: 75%; }
.width-80 { --width: 80%; }
.width-85 { --width: 85%; }
.width-90 { --width: 90%; }
.width-95 { --width: 95%; }
.width-100 { --width: 100%; }


/*! Modules --------- */
.accordion-a { margin: 26px 0 45px; text-align: left; }
.accordion-a details { display: block; position: relative; z-index: 2; padding: 0 64px .1px 26px; border: 0 solid rgba(0,0,0,0); border-top-width: 1px; }
.accordion-a details:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: -1px; bottom: -1px; border: 0 solid var(--custom_bd); border-top-width: 1px; /*opacity: .07;*/ }
.accordion-a details:last-child { border-bottom-width: 1px; }
.accordion-a details:last-child:before { border-bottom-width: 1px; }
.accordion-a details[open] { border-color: rgba(0,0,0,0); }
.accordion-a details[open] summary { margin-bottom: -8px; }
.accordion-a details[open] + details { border-top-color: rgba(0,0,0,0); }
.accordion-a details[open] + details[open] { border-top-color: var(--body_bg); }
.accordion-a details[open]:before { border-width: 0; border-top-width: 1px; /*background: var(--primary_text); opacity: .05;*/ }
.accordion-a details[open] + details:before { border-top-width: 0; }
.accordion-a:not(.compact) details > div > *:last-child { margin-bottom: 18px; }
.accordion-a details > div { position: relative; z-index: 2; }
.accordion-a details > div > .link-btn:last-child, .accordion-a details > div > .submit:last-child { margin-bottom: 10px; }
.accordion-a summary { display: block; position: relative; z-index: 2; margin: 0 -64px 0 -26px; padding: 22px 64px 22px 26px; font-size: var(--main_h_small); font-family: var(--main_ff_h); font-style: var(--main_fs_h); font-weight: var(--main_fw_h); line-height: var(--main_lh_h); text-transform: var(--main_tt_h); letter-spacing: var(--main_ls_h); cursor: pointer; outline: none; }
.accordion-a summary::-webkit-details-marker { display: none; }
.accordion-a summary:before { content: "\e945"; left: auto; width: 64px; font-size: var(--size_20_f); }
.accordion-a summary [class*="icon"], .accordion-a summary .img, .accordion-a summary img, .accordion-a summary picture, .accordion-a summary video, .accordion-a summary svg { display: block; position: relative; top: 10px; margin-top: -20px; margin-right: 10px; border-radius: 0; line-height: 1; }
#root .accordion-a summary picture img, #root .accordion-a summary .img * { margin-left: 0; margin-right: 0; }
.accordion-a details[open] summary:before { content: "\e946"; }
.accordion-a .l4cl.hr:first-child { margin-top: -11px; }
.accordion-a .l4cl.hr:last-child { margin-bottom: 0; }
.accordion-a .l4cl.hr li:last-child:before { border-bottom-width: 0; }
.accordion-a + .accordion-a { margin-top: -45px; }
.accordion-a:not(.compact) details[open] { border-color: var(--light); background: var(--light); color: var(--black); }
.accordion-a:not(.compact) details[open]:before { background: var(--light); }
.accordion-a.compact { margin-bottom: 26px; --main_mr: 18px; --ar_fz: .375em; --ar_w: 44px; --pd1: 18px; }
.accordion-a.compact details[open]:before, .accordion-a.compact details[open] + details:before { border-top-width: 1px; border-top-color: var(--custom_bd); background: none; }
.accordion-a.compact details { padding: 0; }
.accordion-a.compact details[open] { padding-bottom: 9px; }
.accordion-a.compact summary { margin: 0; padding: var(--pd1) 44px var(--pd1) 0; font-size: var(--main_fz); }
.accordion-a.compact summary:before { content: "\e904"; right: 0; width: var(--ar_w); font-size: var(--ar_fz); }
.accordion-a.compact details[open] summary { margin-bottom: 2px; color: inherit; }
.accordion-a.compact details[open] summary:before { content: "\e908"; }
.accordion-a.compact .l4cl:last-child { margin-bottom: 0; }
.accordion-a.compact + .accordion-a { margin-top: -27px; }
.accordion-a + .accordion-a > details:first-child:before { border-top-width: 0; }
.accordion-a.compact.cp2 { margin-top: calc(0px - var(--pd1)); padding-top: 0; border-top-width: 0; --ar_fz: calc(var(--main_fz) * 0.4285714286); --ar_w: auto; --pd1: 13px; }
.accordion-a.compact.cp2 summary { font-family: var(--main_ff); line-height: var(--main_lh); }
.accordion-a.compact.cp2 details:first-child:before { border-top-width: 0; }

@media only screen and (min-width: 761px) {
	.compact.l4cl:not(.hr) {
		overflow-y: hidden; overflow-x: auto; margin-left: 0; margin-right: 0; padding-bottom: 16px;
		flex-wrap: nowrap;
	}
	.compact.l4cl:not(.hr) li { width: 166px; min-width: 166px; max-width: 166px; margin-bottom: 0; }
	.compact.l4cl:not(.hr) li:first-child { width: 150px; min-width: 150px; max-width: 150px; border-left-width: 0; }
	.compact.l4cl:not(.hr) li.w20, .compact.w20.l4cl:not(.hr) li { width: 95.5px; min-width: 95.5px; max-width: 95.5px; }
	.compact.l4cl:not(.hr) li.w20:first-child, .compact.w20.l4cl:not(.hr) li:first-child { width: 79.5px; min-width: 79.5px; max-width: 79.5px; }
	.compact.l4cl:not(.hr) li.w25, .compact.w25.l4cl:not(.hr) li { width: 119.5px; min-width: 119.5px; max-width: 119.5px; }
	.compact.l4cl:not(.hr) li.w25:first-child, .compact.w25.l4cl:not(.hr) li:first-child { width: 103.5px; min-width: 103.5px; max-width: 103.5px; }
	.compact.l4cl:not(.hr) li.w50, .compact.w50.l4cl:not(.hr) li { width: 247px; min-width: 247px; max-width: 247px; }
	.compact.l4cl:not(.hr) li.w50:first-child, .compact.w50.l4cl:not(.hr) li:first-child { width: 215px; min-width: 215px; max-width: 215px; }
	.compact.l4cl:not(.hr) li.w100, .compact.w100.l4cl:not(.hr) li { width: 478px; min-width: 478px; max-width: 478px; }
	.compact.l4cl:not(.hr) li.w100:first-child, .compact.w100.l4cl:not(.hr) li:first-child { width: 462px; min-width: 462px; max-width: 462px; }
	.compact.l4cl:not(.hr) figure form, .compact.l4cl:not(.hr) figure .link-btn { display: none; }
	#root .compact.l4cl:not(.list, .hr) .static form { display: block; }
	#root .compact.l4cl:not(.list, .hr) .static .link-btn { display: flex; }
	#root .l4cl:not(.hr, .category.text-center, .box, [class*="upsell"]) .link-btn:not(.text-end, .text-start) a:not(.circle), #root .l4cl:not(.hr, .category.text-center, .box, [class*="upsell"]) button:not(.circle) { flex-grow: 3; }
}

.align-center { margin-left: auto; margin-right: auto; }
[dir="ltr"] .align-end:not(article) { margin-left: auto; }
[dir="rtl"] .align-end:not(article) { margin-right: auto; }

.js .countdown { font-family: var(--main_ff); line-height: var(--main_lh_h); text-align: center; letter-spacing: var(--main_ls); --w: calc(var(--main_fz) * 2.8571428571); --h: calc(var(--main_fz) * 3.7142857143); --dist2: 25px; --fz: calc(var(--main_fz) * 1.7142857143); --bg: var(--secondary_bg); --fg: var(--white); }
.countdown .simply-section { position: relative; z-index: 2; margin: 0 var(--dist2) 0 0; }
.countdown .simply-section:before { content: "\003A"; display: block; position: absolute; left: 100%; top: 0; width: var(--dist2); font-weight: var(--main_fw); line-height: var(--h); text-align: center; }
#root .countdown .simply-section:last-child { margin-right: 0; }
.countdown .simply-amount { display: block; height: var(--h); margin: 0; font-weight: var(--main_fw); font-size: var(--fz); line-height: var(--h); direction: ltr; }
.countdown .simply-amount > span { display: block; position: relative; z-index: 2; width: var(--w); height: var(--h); margin: 0 3px 0 0; color: var(--fg); font-weight: var(--main_fw_strong); }
.countdown .simply-amount > span:before { border-radius: var(--b2r); background: var(--bg); }
.countdown .simply-amount > span:last-child { margin-right: 0; }
.countdown.strong * { font-weight: var(--main_fw_strong); }
.countdown .simply-word { display: block; padding-top: calc(var(--main_fz) * 0.3571428571); color: var(--primary_text); font-size: calc(var(--main_fz) * 0.8571428571); font-weight: var(--main_fw); text-transform: capitalize; }
.countdown-container:not(.done), img.no-pl-px:not([src]) { visibility: hidden; opacity: 0; }
span.countdown { margin-left: 10px; }
span.countdown.compact { margin-left: 0; margin-right: 0; font-size: 1em; }
.countdown.compact { font-size: 1em; font-weight: var(--main_fw); }
.js .countdown.compact { min-height: 0; }
.countdown.compact .simply-section { margin: 0 6px 0 0; line-height: inherit; }
.countdown.compact .simply-section:before { top: 0; width: 6px; line-height: inherit; }
.countdown.compact .simply-amount { display: inline; width: auto; height: auto; margin: 0; background: none; color: inherit; font-size: 1em; font-weight: inherit; line-height: inherit; }
.countdown.compact .simply-amount > span { width: auto; height: auto; margin: 0; color: inherit; font-weight: var(--main_fw); }
.countdown.compact .simply-amount > span { display: inline; width: auto; height: auto; margin: 0; background: none; }

.fancybox__container { visibility: hidden; position: fixed; left: 0; right: 0; top: 0; bottom: 0; opacity: 0; }

.grecaptcha-badge { visibility: hidden; }

.has-anchor { position: relative; z-index: 2; }
.has-anchor .anchor[id] { display: block; position: absolute; left: 0; top: calc(0px - var(--sticky_offset) - var(--rpp)); }

/*.model-3d {}*/
.fslightbox-container .model-3d, .fancybox__container .model-3d { width: 100%; height: 100%; }
.model-3d model-viewer { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; }
a .model-3d * { -ms-pointer-events: none; pointer-events: none; }
model-viewer { --poster-color: transparent; }
#root .model-3d button { display: none; }

.m6as { position: relative; z-index: 2; margin: 32px 0 calc(44px - var(--main_mr)); --w_f: 50%; --d: 48px; --mih: 0px; --hdef: 100%; }
.m6as:before { bottom: var(--main_mr); }
.m6as > * { min-height: calc(var(--mih) + var(--main_mr)); padding: 0 0 0 var(--d); }
.m6as > div { flex-grow: 3; }
.m6as > div > *[id*="hotspot-variants"], .m6as > div .l4ml-form { width: 100%; }
.m6as > figure {
	width: var(--w_f); min-height: 0; margin-bottom: var(--main_mr);
	flex-shrink: 0;
}
/*.m6as:not([class*="align-"]) > figure { min-height: 100%; }
		.m6as.align-bottom > figure { min-height: 0; }*/
#root .m6as > figure { padding: 0; }
.m6as > figure img, .m6as > figure video, .m6as > figure svg, .m6as > figure iframe { width: 100%; }
.m6as > figure > picture img, .m6as > figure > picture, .m6as > figure video, .m6as > figure > picture svg { min-height: var(--mih) !important; max-height: 100%; object-fit: cover; }
.m6as figure.no-img { padding-top: 32%; }
.m6as figure.no-img svg { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; }
/*#root .m6as > figure img,*/ #root .m6as > figure > a, #root .m6as > figure > picture img, #root .m6as > figure > picture, #root .m6as > figure > picture video, #root .m6as > figure > picture iframe, #root .m6as > figure > a img, #root .m6as > figure > picture img { height: var(--hdef) !important; object-fit: cover; }
.m6as p + p > a.strong:first-child, .m6as p + a.strong, .m6ac p + p > a.strong:first-child, .m6ac p + a.strong { display: block; margin-top: -14px; }
.m6as .l4ft p + p > a.strong:first-child, .m6as .l4ft p + a.strong, .m6ac p + p > a.strong:first-child, .m6ac .l4ft p + a.strong { margin-top: 0; }
.m6as h1, .m6as h2, .m6as h3, .m6as h4, .m6as h5, .m6as h6 { margin-top: 0; }
.m6as + .m6as { margin-top: -10px; }
/*.m6as.inv {}*/
.m6as.inv > * { padding-left: 0; padding-right: var(--d); }
.m6as.overlay { color: var(--white); }
.m6as.overlay > * { padding-top: 35px; padding-bottom: 35px; padding-left: var(--d); padding-right: var(--d); }
.m6as.overlay > figure { align-self: stretch; }
.m6as.overlay h1, .m6as.overlay h2, .m6as.overlay h3, .m6as.overlay h4, .m6as.overlay h5, .m6as.overlay h6 { color: inherit; }
.m6as.overlay:before { background: var(--primary_text); opacity: 1; }
.m6as.overlay.inv > * { padding-left: var(--d); padding-right: var(--d); }
.m6as.text-center > * { padding-left: var(--d); padding-right: var(--d); }
.m6as[style*="--w_f:"] > figure { width: var(--w_f); }
.m6as[style*="--w_f:"] > * { width: calc(100% - var(--w_f)); }

@media only screen and (min-width: 1001px) {
	.m6as > figure .icon-play { width: var(--size_100_f); height: var(--size_100_f); font-size: var(--size_100_f); line-height: var(--size_100_f); }
}
@media only screen and (min-width: 761px) {
	#root .m6ac .l4cl.hr:not(.l4cl-banner) > li:first-child, #root .m6as .l4cl.hr:not(.l4cl-banner) > li:first-child { margin-top: -11px; }
}
.m6ac { margin: 32px 0 44px calc(0px - var(--dist_ac)); --dist_ac: 16px; }
.m6ac > * { width: 60%; border-left: var(--dist_ac) solid rgba(0,0,0,0); }
.m6ac > *:first-child { width: 40%; }
.m6ac .l4cl { margin-bottom: -4px; }
.m6ac.inv .l4cl.hr, .m6as.inv .l4cl.hr { margin-right: 0; }
.m6ac .l4cl.hr li:last-child:before, .m6as .l4cl.hr li:last-child:before { border-bottom-width: 0; }
.m6ac .l4cl li { width: 25%; }
.m6ac .l4cl li.w50 { width: 50%; }
.m6ac .l4cl li.w50 picture { padding-top: 100%; }
.m6ac .m6pr-compact { margin-left: calc(0px - var(--dist_ac)); }
.m6ac .m6pr-compact > * { width: 50%; border-left: var(--dist_ac) solid rgba(0,0,0,0); }

@media only screen and (min-width: 1001px) {
	.m6ac.sticky { align-items: flex-start; }
	.m6ac:has(>.sticky) { align-items: flex-start; }
	.m6ac.sticky > *, .m6ac > .sticky { position: sticky; top: calc(var(--sticky_offset) + var(--rpp)); }
	.m6ac.align-stretch > * > .l4ft:first-child:last-child { height: 100%; }
	.m6ac.align-stretch > * > .l4ft:first-child:last-child figure { position: absolute; left: 0; right: 0; top: 0; bottom: 0; }
	.m6ac.align-stretch > * > .l4ft:first-child:last-child figure, .m6ac.align-stretch > * > .l4ft:first-child:last-child figure img, .m6ac.align-stretch > * > .l4ft:first-child:last-child figure picture, .m6ac.align-stretch > * > .l4ft:first-child:last-child figure video { height: 100% !important; }
	.m6ac.align-stretch > * > .l4ft:first-child:last-child li:last-child { margin-bottom: 8px; }
}

.m6bx { position: relative; z-index: 2; margin-bottom: var(--main_mr); padding: var(--dist_a) var(--dist_b) max(0.1px, calc(var(--dist_a) - var(--main_mr))); color: var(--primary_text); font-size: var(--main_fz); --dist_a: var(--main_mr); --dist_b: var(--dist_a); --m6bx_bg: var(--body_bg); --m6bx_bw: 1px; --m6bx_bd: var(--custom_bd); }
.m6bx[class*="overlay-"], .m6bx[style*="--m6bx_bg"]:not([style*="--m6bx_bd"]) { --m6bx_bw: 0px; }
.m6bx > .img-overlay, .m6fr > .img-overlay { left: var(--l0ra); right: var(--lar0); width: 100%; --overlay_opacity: 1; }
.m6bx > .img-overlay.text-end, .m6fr > .img-overlay.text-end { left: var(--lar0); right: var(--l0ra); }
.m6bx > .img-overlay[style*="--mih"], .m6fr > .img-overlay[style*="--mih"] { top: 0; bottom: auto; height: min(var(--mih), 100%); }
.m6bx > .img-overlay[style*="--w"], .m6fr > .img-overlay[style*="--w"] { width: min(var(--w), 100%); }
.m6bx:before, .m6bx > .img-overlay { border: var(--m6bx_bw) solid var(--m6bx_bd); background: var(--m6bx_bg); }
.m6bx:has(.img-overlay):before { display: none; }
.m6bx > .overlay-theme { content: ""; display: block; position: absolute; }
.m6bx > .link-btn:last-child, .m6bx > .submit:last-child { margin-bottom: 14px; }
.m6bx + .m6bx { margin-top: calc(0px - var(--main_mr) + 16px); }
.m6bx + h1, .m6bx + h2, .m6bx + h3, .m6bx + h4, .m6bx + h5, .m6bx + h6 { margin-top: calc(var(--main_mr) * 1.7692307692); }
h1 + .m6bx, h2 + .m6bx, h3 + .m6bx, h4 + .m6bx, h5 + .m6bx, h6 + .m6bx { margin-top: var(--main_mr); }
/*.m6bx a[href*="tel:"] { color: inherit; text-decoration: none; }
		.m6bx a[href*="tel:"] i { margin-right: 5px; }*/
.m6bx > div > .m6fr:last-child { margin-bottom: var(--main_mr); }
.m6bx.overlay-gradient {
	background: none;
	background-clip: inherit; -webkit-background-clip: inherit;
	text-fill-color: currentcolor; -webkit-text-fill-color: currentcolor;
}
.m6bx.overlay-gradient:before { --m6bx_bg: var(--theme_bg_gradient); }
.m6bx.overlay-content:before { --m6bx_bg: var(--sand); --m6bx_bd: var(--m6bx_bg); }
.m6bx.inline { padding-bottom: max(0.1px, calc(var(--dist_a) - var(--main_mr_min))); --dist_a: 10px; --dist_b: min(var(--rpp), 20px); --main_mr_min: calc(var(--main_mr) * 0.3846153846); }
.m6bx.inline * { --main_mr: var(--main_mr_min); }
.m6bx.compact { margin-bottom: var(--main_mr); padding: var(--dist_a) var(--dist_b) max(0.1px, calc(var(--dist_a) - var(--main_mr_half))); --dist_b: calc(var(--main_mr) * 0.85); --dist_a: calc(var(--main_mr) * 0.65); }
.m6bx.compact > *:last-child { margin-bottom: var(--main_mr_half); }
.m6bx.size-xs { --dist_a: var(--main_mr); }
.m6bx.size-s { --dist_a: calc(var(--main_mr) * 2); }
.m6bx.size-m { --dist_a: calc(var(--main_mr) * 3); }
.m6bx.size-l { --dist_a: calc(var(--main_mr) * 4); }
.m6bx.wide { padding-left: 0; padding-right: 0; }
.m6bx.wide, .m6fr.wide { --offset: min(var(--rpn), calc(-50vw + var(--glw) * 0.5) + var(--scrollbar_width) * 0.5); }
.m6bx.wide > .img-overlay, .m6fr.wide > .img-overlay { left: var(--offset); right: var(--offset); width: auto; border-radius: 0; border-left-width: 0; border-right-width: 0; }
[dir="ltr"] .m6bx.wide > .img-overlay[style*="--w"]:not([style*="--w: 100%"]), [dir="ltr"] .m6fr.wide > .img-overlay[style*="--w"]:not([style*="--w: 100%"]) { border-right-width: var(--m6bx_bw); border-radius: 0 var(--b2r) var(--b2r) 0; }
[dir="rtl"] .m6bx.wide > .img-overlay[style*="--w"]:not([style*="--w: 100%"]), [dir="rtl"] .m6fr.wide > .img-overlay[style*="--w"]:not([style*="--w: 100%"]) { border-left-width: var(--m6bx_bw); border-radius: var(--b2r) 0 0 var(--b2r); }
.m6bx.wide > .img-overlay[style*="--w"], .m6fr.wide > .img-overlay[style*="--w"] { width: calc(var(--w) + 2 * (var(--offset) - var(--offset) - var(--offset))); }
[dir="ltr"] #root .m6bx.wide > .img-overlay.text-end, [dir="ltr"] #root .m6fr.wide > .img-overlay.text-end { left: auto; border-radius: var(--b2r) 0 0 var(--b2r); border-right-width: 0; border-left-width: var(--m6bx_bw); }
[dir="rtl"] #root .m6bx.wide > .img-overlay.text-end, [dir="rtl"] #root .m6fr.wide > .img-overlay.text-end { right: auto; border-radius: 0 var(--b2r) var(--b2r) 0; border-left-width: 0; border-right-width: var(--m6bx_bw); }
.m6bx.no-padding { padding: 0; }
@media only screen and (min-width: 761px) {
	.m6bx.no-padding-desktop { margin-bottom: 0; padding: 0; }
}
@media only screen and (max-width: 760px) {
	.m6bx.no-padding-mobile { margin-bottom: 0; padding: 0; }
}

/*.m6cn {}*/
.m6cn > a.has-img, #nav > .has-img { display: block; }
#nav > .has-img { padding-left: var(--rpp); padding-right: var(--rpp); }
.m6cn > a.has-img img, #nav > .has-img img { width: auto !important; max-height: 36px !important; }

.m6cu { position: relative; z-index: 3; min-height: var(--mih); margin: 50px 0; padding: var(--p2) var(--p1) calc(var(--p2) - var(--main_mr)) calc(var(--p1) - var(--dist)); color: var(--white); --main_mr: 12px; --p1: 40px; --p2: 42px; --dist: 30px; --mih: 150px; }
.m6cu > * { border-left: var(--dist) solid rgba(0,0,0,0); }
.m6cu > .w20 { max-width: 20%; }
.m6cu > .w25 { max-width: 25%; }
.m6cu > .w30 { max-width: 30%; }
.m6cu > .w33 { max-width: 33.333333333%; }
.m6cu > .w40 { max-width: 40%; }
.m6cu > .w45 { max-width: 45%; }
.m6cu > .w50 { max-width: 50%; }
.m6cu > .background { overflow: hidden; border-radius: var(--b2r); border-width: 0; }
.m6cu:before { z-index: -2; background: var(--coal); }
.m6cu:not(.wide):before { border-radius: var(--b2r); }
.m6cu h1, .m6cu h2, .m6cu h3, .m6cu h4, .m6cu h5, .m6cu h6 {
	min-width: 0; margin-bottom: var(--main_mr); color: inherit;
	flex-shrink: 5;
}
.m6cu h1:last-child, .m6cu h2:last-child, .m6cu h3:last-child, .m6cu h4:last-child, .m6cu h5:last-child, .m6cu h6:last-child { margin-bottom: var(--main_mr); }
.m6cu .link-btn { z-index: 9; margin-bottom: calc(var(--main_mr) - 8px); }
.m6cu p:has(a) { position: relative; z-index: 9; }
.m6cu p:not(.link-btn) a { color: inherit; }
.m6cu p.countdown { margin-top: 0; flex-shrink: 0; }
.m6cu .countdown .simply-word { color: inherit; font-size: 1em; font-weight: inherit; }
.m6cu.size-xs, .m6as.size-xs { --mih: 170px; }
.m6cu.size-s, .m6as.size-s { --mih: 260px; }
.m6cu.size-m, .m6as.size-m { --mih: 390px; }
.m6cu.size-l, .m6as.size-l { --mih: 520px; }
.m6cu.size-xl, .m6as.size-xl { --mih: 700px; }
.m6cu.wide { margin-left: calc(0px - var(--dist)); padding-left: 0; padding-right: 0; }
.m6cu.wide .background { width: var(--100vw) !important; border-radius: 0; }

.m6fr { position: relative; z-index: 2; margin: 0 0 50px; font-size: var(--main_fz); --pd: calc(var(--pda) * var(--pdb)); --pda: 55px; --pdb: 1; --pdc: 50px; --mih: 345px; --mhj: 0px; --main_mr: calc(var(--main_lh) * var(--main_fz) * var(--m_cust)); --m_cust: 0.8; --br1: var(--b2p); --br2: var(--b2p); --br3: var(--b2p); --br4: var(--b2p); }
.m6fr article { position: relative; z-index: 3; min-height: var(--mih); margin: 0 0 20px; /*--padding_bottom: 0;*/ }
.m6fr article > div { padding: var(--pdc) var(--pd) calc(var(--pdc) - var(--main_mr)); }
/*.m6fr article > div[style*="--max_width"] { max-width: calc(var(--max_width) + var(--pd) * 2); }
	.m6fr.wide article > div[style*="--max_width"] { max-width: var(--max_width); }*/
#root .m6fr article > div[class*="media-flexible"] { padding: 0; }
.m6fr article.aside:before { z-index: -2; /*background: var(--primary_text);*/ }
.m6fr article.module-color-palette[class^=palette-]:before { background: var(--primary_bg); }
.m6fr:not(.wide) article.aside.module-color-palette[class^=palette-] { overflow: hidden; }
.m6fr > article { display: none; }
.m6fr article:before, #root .m6fr article > figure, .m6fr article > figure .img-overlay, .m6fr article > figure picture { border-radius: var(--br1) var(--br2) var(--br3) var(--br4); }
#root .m6fr article > figure { overflow: hidden; padding: 0; }
#root .m6fr .media-flexible picture, #root .m6fr .media-flexible video, #root .m6fr .media-flexible img, #root .m6fr .media-flexible svg { border-radius: var(--b2p); }
#root .m6fr:not(.wide) figure:has(picture ~ picture) picture { border-radius: 0; }
#root .m6fr:not(.wide) figure:has(picture ~ picture) picture * { --b2p: 0px; }
#root .m6fr:not(.wide) figure.has-pics picture { border-radius: 0; }
#root .m6fr:not(.wide) figure.has-pics picture * { --b2p: 0px; }
#root .m6fr figure.text-end img { object-position: right center; }
#root .m6fr figure.text-start img { object-position: left center; }
/*.m6fr figure .img-overlay,*/ .m6fr figure .background, #background .img-overlay, .m6wd .background:before, #root .m6wd.overlay-content .img-overlay { border-radius: 0; background: var(--primary_text); opacity: .7; }
.has-html-background .img-overlay, .m6fr .has-html-background:before, .m6fr.has-html-background figure:before { display: none; }
#root .overlay-content .img-overlay { background: var(--white); opacity: .7; }
#root .overlay-black .img-overlay { background: var(--cod); opacity: .7; }
#root .overlay-sand .img-overlay { background: var(--sand); opacity: .7; }
#root .overlay-tan .img-overlay { background: var(--tan); opacity: .7; }
#root .overlay-theme .img-overlay { background: var(--secondary_bg); opacity: .7; }
#root .m6fr figure picture, #root .m6fr figure video { display: block; overflow: hidden; position: relative; z-index: 2; height: 100% !important; object-fit: cover; }
#root .m6fr figure picture:not([style], [class*="width-"], [style*="--size"]), #root .m6fr figure video:not([style], [class*="width-"], [style*="--size"]) { width: 100% !important; }
.l4ft li.overlay-theme { color: var(--white); }
.m6fr figure.overlay-content ~ *, .m6fr figure.overlay-sand ~ *, .m6fr figure.overlay-tan ~ *, .l4ft figure.overlay-content ~ *, .l4ft figure.overlay-sand ~ *, .l4ft figure.overlay-tan ~ *, .l4ft li.overlay-content, .l4ft li.overlay-sand, .l4ft li.overlay-tan, .m6fr.slider-fraction[data-active-content*="overlay-content"], .m6fr.slider-fraction[data-active-content*="overlay-content"] .swiper-button-nav, .m6fr.slider-fraction[data-active-content*="overlay-sand"], .m6fr.slider-fraction[data-active-content*="overlay-sand"] .swiper-button-nav, .m6fr.slider-fraction[data-active-content*="overlay-tan"], .m6fr.slider-fraction[data-active-content*="overlay-tan"] .swiper-button-nav { color: var(--primary_text); }
.m6fr .link-overlay { z-index: 8; }
.m6fr article.aside { --w: 50%; --w2: var(--w); }
.m6fr article.aside > div { width: 100%; }
/*.m6fr.wide article.aside > * { width: calc(var(--w) - var(--pd)); }*/
#root .m6fr.wide article.aside > .link-btn { width: calc(var(--w) - var(--pd) + 16px); }
#root .m6fr article.aside figure { width: auto; }
.m6fr article.aside figure { left: var(--w2); right: 0; z-index: 2; }
#root .m6fr.wide article.aside figure { left: var(--w2); right: min(calc(-50vw + var(--glw) * 0.5), var(--rpn)); }
/*.m6fr article.aside.inv {}*/
[dir="ltr"] .m6fr article.aside:not(.inv) > div { padding-right: calc(100% - var(--w) + var(--pd) / var(--pdb)); }
[dir="ltr"] .m6fr article.aside.inv > div { padding-left: calc(100% - var(--w) + var(--pd) / var(--pdb)); }
.m6fr article.aside.inv figure { right: var(--w2); left: 0; }
#root .m6fr.wide article.aside.inv figure { right: var(--w2); left: min(calc(-50vw + var(--glw) * 0.5), var(--rpn)); }
.m6fr h1, .m6fr h2, .m6fr h3, .m6fr h4, .m6fr h5, .m6fr h6 { width: 100%; color: inherit; /*--main_mr: calc(var(--main_lh_h) * var(--main_fz) * var(--m_cust));*/ }
.m6fr h1:last-child, .m6fr h2:last-child, .m6fr h3:last-child, .m6fr h4:last-child, .m6fr h5:last-child, .m6fr h6:last-child { margin-bottom: var(--main_mr); }
.m6fr h1 .small, .m6fr h2 .small, .m6fr h3 .small, .m6fr h4 .small, .m6fr h5 .small, .m6fr h6 .small { display: block; margin-top: 32px; color: inherit; font-weight: inherit; font-size: 0.75em; opacity: 1; }
.m6fr p { width: 100%; }
.m6fr .text-center:not(.aside) p:not(.link-btn), .m6fr .text-center:not(.aside) h1, .m6fr .text-center:not(.aside) h2, .m6fr .text-center:not(.aside) h3, .m6fr .text-center:not(.aside) h4, .m6fr .text-center:not(.aside) h5, .m6fr .text-center:not(.aside) h6 { margin-left: auto; margin-right: auto; }
.m6fr .text-center:not(.aside) .link-btn { max-width: none; }
.m6fr p a { position: relative; z-index: 2; }
.m6fr p + h1, .m6fr p + h2, .m6fr p + h3, .m6fr p + h4, .m6fr p + h5, .m6fr p + h6 { margin-top: calc(0px - var(--main_mr) * 0.75); }
.m6fr .link-btn { position: relative; z-index: 9; width: calc(100% + var(--btn_dist)); margin-top: calc(var(--main_lh) * var(--main_fz) * var(--m_cust) * 0.25); /*margin-bottom: 0;*/ }
.m6fr .link-btn a:before { z-index: -1; }
.m6fr .link { position: relative; z-index: 9; }
.m6fr .swiper-outer { overflow: hidden; }
.m6fr .swiper-pagination-bullets, .m6fr.slider-fraction .swiper-custom-pagination { position: absolute; left: 0; right: 0; bottom: 24px; z-index: 9; }
.m6fr.slider-fraction .swiper-custom-pagination { bottom: 20px; padding-left: 45px; padding-right: 45px; }
.m6fr.slider-fraction .swiper-custom-pagination .swiper-pagination-fraction, .swiper-custom-fraction { display: block; margin: 0 2px; font-weight: var(--main_fw_strong) !important; font-size: var(--size_16_f); }
#root .swiper-custom-fraction span, #root .swiper-custom-pagination span, #root .swiper-pagination-fraction span { font-weight: inherit; }
.slider-fraction .swiper-custom-pagination .swiper-pagination-current, .slider-fraction .swiper-custom-pagination .swiper-button-prev, .no-thumbs-mobile .swiper-pagination-current, .last-slide-active .total-el, .last-slide-active ~ .swiper-custom-pagination .total-el { opacity: .35; }
.m6fr .swiper-pagination-bullet:before { background: var(--white); opacity: 1; }
.m6fr[data-active-content*="overlay-content"] .swiper-pagination-bullet:before, .m6fr[data-active-content*="overlay-sand"] .swiper-pagination-bullet:before, .m6fr[data-active-content*="overlay-tan"] .swiper-pagination-bullet:before { background: var(--primary_text); opacity: .25; }
.m6fr[data-active-content*="overlay-content"] .swiper-pagination-bullet.swiper-pagination-bullet-active:before, .m6fr[data-active-content*="overlay-sand"] .swiper-pagination-bullet.swiper-pagination-bullet-active:before, .m6fr[data-active-content*="overlay-tan"] .swiper-pagination-bullet.swiper-pagination-bullet-active:before { background: var(--secondary_bg); opacity: 1; }
.m6fr[data-active-content*="overlay-theme"] .swiper-pagination-bullet:before, .last-slide-active .swiper-pagination-total { opacity: .35; }
.m6fr[data-active-content*="overlay-theme"] .swiper-pagination-bullet.swiper-pagination-bullet-active:before { background: var(--white); opacity: 1; }
.m6fr .swiper-button-nav { display: none; top: var(--d); bottom: var(--d); z-index: 998; color: inherit; --d: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh) + var(--label_dist) * 2); }
#root .slider-fraction .swiper-custom-pagination .swiper-button-nav { display: block; overflow: visible; position: relative; left: 0; right: auto; top: 0; width: 27px; height: 30px; margin-left: 0; margin-right: 0; color: inherit; font-size: var(--size_12_f); }
#root .slider-fraction .swiper-custom-pagination .swiper-button-nav:after { content: ""; display: block; position: absolute; left: -9px; right: -9px; top: -7px; bottom: -7px; }
#root .m6fr .slider-fraction .swiper-custom-pagination .swiper-button-nav, #root .m6fr.wide .slider-fraction .swiper-custom-pagination .swiper-button-nav { width: 27px; height: 30px; }
.slider-fraction .swiper-custom-pagination .swiper-button-prev:before { content: "\e96a"; }
.slider-fraction .swiper-custom-pagination .swiper-button-next:before { content: "\e96b"; }
.m6fr .play-pause { display: block; position: absolute; left: 0; bottom: 0; z-index: 999; width: 78px; height: 70px; color: inherit; font-size: 16px; text-align: left; text-indent: -3000em; text-decoration: none; direction: ltr; }
.m6fr .play-pause:before { content: "\e986"; top: auto; bottom: 21px; margin: 0; line-height: 30px; }
.m6fr.paused .play-pause:before { content: "\e985"; }
.m6fr[data-active-content*="overlay-content"] .play-pause, .m6fr[data-active-content*="overlay-sand"] .play-pause, .m6fr[data-active-content*="overlay-tan"] .play-pause { color: var(--primary_text); }
/*.m6fr[data-active-content*="overlay-theme"] .play-pause {}*/
.m6fr article.no-padding > div:not(.m6bx), .m6fr.no-padding article > div:not(.m6bx) { padding-left: 0; padding-right: 0; }
.m6fr.size-xs article, .m6fr article.size-xs, .m6fr.compact article, .m6fr article.compact { --mih: 260px; }
.m6fr.size-s article, .m6fr article.size-s { --mih: 390px; }
.m6fr.size-m article, .m6fr article.size-m { --mih: 520px; }
.m6fr.size-l article, .m6fr article.size-l { --mih: 700px; }
.m6fr.size-xl article, .m6fr article.size-xl { --mih: calc(100vh - var(--header_height_static) - var(--nav_top_h)); }
/*.m6fr.wide {}*/
.m6fr.wide article:not(.wide) { --br1: 0; --br2: 0; --br3: 0; --br4: 0; }
.m6fr.wide article, .m6fr.wide article.aside { max-width: var(--glw); margin-left: auto; margin-right: auto; }
.m6fr.wide article > div:not(.m6bx) { padding-left: 0; padding-right: 0; }
.m6fr.wide article:not(.background-wide) > figure, .m6fr.wide article:not(.background-wide):before, .m6fr.wide article:not(.background-wide) .link-overlay, .m6fr.wide article:not(.background-wide) .media-flexible { left: min(var(--rpn), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5)); right: min(var(--rpn), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5)); width: auto; margin: 0; border-radius: 0; }
.m6fr.wide article:not(.background-wide) > figure img, .m6fr.wide article:not(.background-wide) > figure video, .m6fr.wide article:not(.background-wide) > figure picture, .m6fr.wide article:not(.background-wide) > figure svg { border-radius: 0; }
#root .m6fr figure.plain:before, #root .m6fr article.plain:before, .m6wd .background.plain:before { display: none; }
.m6fr.wide.s4wi .swiper-outer { margin-left: min(var(--rpn), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5)); margin-right: min(var(--rpn), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5)); }
.m6fr.wide.s4wi .swiper-slide { overflow: hidden; padding-left: var(--rpp); padding-right: var(--rpp); }
.m6fr.wide.s4wi article { max-width: var(--glw); }
.m6fr.s4wi { overflow: hidden; border-radius: var(--b2r); }
.m6fr.s4wi.wide { overflow: visible; border-radius: 0; }
.m6fr.wide.s4wi .swiper-outer, #root .m6fr.wide.s4wi article:not(.aside) figure/*, #root .m6fr.wide.s4wi .swiper-outer article, #root .m6fr.wide.s4wi .swiper-outer article.aside figure, #root .m6fr.wide.s4wi .swiper-outer article:before*/ { border-radius: 0; }

/* Rudolf: change layout of .m6fr */
@media only screen and (min-width: 761px) {
	/*.m6fr {}*/
	/* aspects */
	.m6fr:has(article[style*="--aspect:"]) { container-type: inline-size; }
	#root .m6fr article[style*="--aspect:"] { --mih: calc(var(--aspect) * 1cqw); }
	#root .m6fr.wide article[style*="--aspect:"] { --mih: calc(var(--aspect) * 1vw); }
	#root .m6fr article[class*="width"][style*="--aspect:"] { --mih: calc(var(--aspect) * var(--wv) * 1cqw); }
	#root .m6fr.wide article[class*="width"][style*="--aspect:"] { --mih: calc(var(--aspect) * var(--wv) * 1vw - var(--rpp)); }

	/* remove aside border radius when wide */
	[dir="ltr"] .m6fr.wide article.aside.inv > figure, [dir="rtl"] .m6fr.wide article.aside > figure { --br1: 0; --br2: var(--b2p); --br3: var(--b2p); --br4: 0; }
	[dir="ltr"] .m6fr.wide article.aside > figure, [dir="rtl"] .m6fr.wide article.aside.inv > figure { --br1: var(--b2p); --br2: 0; --br3: 0; --br4: var(--b2p); }

	/* remove aside upper border radius when first in content */
	[dir="ltr"] #content > .shopify-section:first-child .m6fr.wide article.aside:not(.inv) > figure, [dir="ltr"] #content > .m6fr.wide:first-child article.aside:not(.inv) > figure, [dir="rtl"] #content > .shopify-section:first-child .m6fr.wide article.aside.inv > figure, [dir="rtl"] #content > .m6fr.wide:first-child article.aside.inv > figure { --br1: 0; }
	[dir="ltr"] #content > .shopify-section:first-child .m6fr.wide article.aside.inv > figure, [dir="ltr"] #content > .m6fr.wide:first-child article.aside.inv > figure, [dir="rtl"] #content > .shopify-section:first-child .m6fr.wide article.aside:not(.inv) > figure, [dir="rtl"] #content > .m6fr.wide:first-child article.aside:not(.inv) > figure { --br2: 0; }

	/* remove aside border radius when pallete (any) */
	[dir="ltr"] .m6fr article.aside.overlay[class*="palette-"]:not(.inv) > figure, [dir="rtl"] .m6fr article.aside.overlay.inv[class*="palette-"] > figure { --br1: 0; --br4: 0; }
	[dir="ltr"] .m6fr article.aside.overlay.inv[class*="palette-"] > figure, [dir="rtl"] .m6fr article.aside.overlay[class*="palette-"]:not(.inv) > figure { --br2: 0; --br3: 0; }

	/* remove aside border radius inside slider */
	[dir="ltr"] .m6fr.s4wi article.aside:not(.inv), [dir="rtl"] .m6fr.s4wi article.aside.inv { --br2: 0; --br3: 0; }
	[dir="rtl"] .m6fr.s4wi article.aside:not(.inv), [dir="ltr"] .m6fr.s4wi article.aside.inv { --br1: 0; --br4: 0; }

	/* remove aside border radius when pallete (any) inside slider */
	.m6fr.s4wi article.aside.overlay[class*="palette-"] { --br1: 0; --br2: 0; --br3: 0; --br4: 0; }
}
@media only screen and (max-width: 760px) {
	/*.m6fr {}*/
	/* aspects */
	.m6fr:has(article[style*="--aspect_m:"]) { container-type: inline-size; }
	#root .m6fr article[style*="--aspect_m:"] { --mih: calc(var(--aspect_m) * 1cqw); }
	#root .m6fr.wide article[style*="--aspect_m:"] { --mih: calc(var(--aspect_m) * 1vw); }
	#root .m6fr article.aside[style*="--aspect_m:"] > figure { min-height: 0; height: calc(var(--aspect_m) * 1cqw); }
	#root .m6fr.wide article.aside[style*="--aspect_m:"] > figure { min-height: 0; height: calc(var(--aspect_m) * 1vw); }

	/* remove aside border radius when palette (any) */
	.m6fr article.aside.overlay[class*="palette-"] > figure { --br3: 0; --br4: 0; }

	/* remove aside border radius inside slider */
	.m6fr.s4wi article.aside.overlay { --br1: 0; --br2: 0; }
	.m6fr.s4wi article.aside.overlay[class*="palette-"] { --br1: 0; --br2: 0; --br3: 0; --br4: 0; }
}

.m6fr article.align-top { align-items: flex-start; }
.m6fr article { align-items: center; }
.m6fr article.align-bottom { align-items: flex-end; }

.m6fr article.align-start:not(.aside) { justify-content: flex-start; }
.m6fr article:not(.aside) { justify-content: center; }
.m6fr article.align-end:not(.aside) { justify-content: flex-end; }


/* 2. Remove all ".m6fr article" references under /*! Flexbox --------- *
remember to keep the ones that refer to ".m6fr article > figure"

3.
once "padding-bottom" is calculated, you might need to substract the paddings, so instead of:
padding-bottom: 50%;
it should be:
padding-bottom: calc(50% - var(--prc) - var(--pdc) + var(--main_mr));

/* Rudolf: change layout of .m6fr */


.m6fr.overlay-content, .m6fr article.overlay-content { color: var(--primary_text); }
.js .m6fr.overlay-content:before, .m6fr.overlay-content article:before, .m6fr article.overlay-content:before { background: var(--white); }
.m6fr.overlay-content figure:before, .m6fr article.overlay-content figure:before { background: none; }
.m6fr.overlay-black, .m6fr article.overlay-black { color: var(--white); }
.js .m6fr.overlay-black:before, .m6fr.overlay-black article:before, .m6fr article.overlay-black:before { background-color: var(--black); }
.m6fr.overlay-black figure:before, .m6fr article.overlay-black figure:before { background: none; }
.m6fr.overlay-sand, .m6fr article.overlay-sand { color: var(--primary_text); }
.js .m6fr.overlay-sand:before, .m6fr.overlay-sand article:before, .m6fr article.overlay-sand:before { background-color: var(--sand); }
.m6fr.overlay-sand figure:before, .m6fr article.overlay-sand figure:before { background: none; }
.m6fr.overlay-tan, .m6fr article.overlay-tan { color: var(--primary_text); }
.js .m6fr.overlay-tan:before, .m6fr.overlay-tan article:before, .m6fr article.overlay-tan:before { background-color: var(--tan); }
.m6fr.overlay-tan figure:before, .m6fr article.overlay-tan figure:before { background: none; }
.m6fr.overlay-theme, .m6fr article.overlay-theme { color: var(--white); }
.js .m6fr.overlay-theme:before, .m6fr.overlay-theme article:before, .m6fr article.overlay-theme:before { background-color: var(--secondary_bg); }
.m6fr.overlay-theme figure:before, .m6fr article.overlay-theme figure:before { background: none; }
.m6fr.overlay-theme .link-btn a, .m6fr article.overlay-theme .link-btn a { color: var(--secondary_bg); }

.m6lm { --bg: var(--body_bg); }
.js *:not(.link-more-clicked) > .m6lm { overflow: hidden; position: relative; z-index: 2; max-height: calc(var(--main_fz) * var(--main_lh) * 5); margin-bottom: var(--main_mr); }
.js *:not(.link-more-clicked) > .m6lm + .has-link-more { margin-top: calc(0px - var(--main_mr)); }
.js .m6lm + * > a.link-more { display: block; }
.js :not(.link-more-clicked) > .m6lm > *:not(h1, h2, h3, h4, h5, h6) { margin-bottom: 0; }
.m6lm:not(.high) + .has-link-more:not(.link-more-clicked), .link-more-clicked > .m6lm:before { display: none; }
*:not(.link-more-clicked) > .m6lm.high:before {
	top: auto; height: calc(var(--main_fz) * var(--main_lh) * 2);
	background: linear-gradient(to bottom, rgba(0,0,0,0) 0%,var(--bg) 100%);
}
.module-color-palette[class^=palette-] .m6lm { --bg: var(--primary_bg); }

/* module-panel */
.m6pn {
	display: block; overflow-x: hidden; overflow-y: auto; visibility: hidden; position: fixed; left: var(--lar0); right: var(--l0ra); top: 0; bottom: 0; z-index: 160; width: 100%; max-width: 460px; background: var(--white); opacity: 0;
	transform: translateX(20px);
}
.m6pn:not(.toggle) { pointer-events: none; }
.m6pn.wide {
	left: 0; top: auto; max-width: none;
	transform: translateY(20px);
}
.m6pn.inv { left: var(--l0ra); right: var(--lar0); transform: translateX(-20px); }

.m6pr { position: relative; z-index: 3; margin-bottom: 25px; margin-left: calc(0px - var(--cols)); --cols: 75px; --cont_w: 40%; }
.m6pr > * { position: sticky; top: calc(var(--sticky_offset) + var(--rpp)); width: var(--cont_w); border-left: var(--cols) solid rgba(0,0,0,0); }
.m6pr > footer, .m6pr .l4pr-container { position: sticky; width: calc(100% - var(--cont_w)); margin-left: 0; margin-right: 0; margin-top: 24px; }
.m6pr > footer { position: relative; top: 0; }
.m6pr > .m6tb:last-child { margin-bottom: 0; }
.m6pr .l4pr-container { margin-top: 0; }
#root .m6pr > header, #root .m6pr > footer { position: relative; top: 0; }
#root .m6pr > header { z-index: 3; }
.m6pr > header { position: relative; top: 0; width: 100%; min-width: 100%; max-width: 100%; margin: 0; }
.m6pr .l4pr { position: relative; top: 0; width: 100%; margin: 0 0 var(--main_mr); }
.m6pr .l4pr.s4wi { border-left-width: 38px; border-right-width: 38px; }
.m6pr .l4pr.s4wi.slider-fraction { border-left-width: 0; border-right-width: 0; }
.m6pr .l4pr.s4wi:not(.slider-fraction) > .s1lb { left: -38px; }
.m6pr .l4dr { margin-right: -22px; }
.m6pr .l4dr li { margin-right: 22px; }
#root .m6pr .l4dr .l4sc.box li { margin-bottom: 0; }

@media only screen and (min-width: 1001px) {
	.scrolled:has(.l4pr, .m6pr, .m6cl.sticky, .m6cl, .l4vw, .f8fl, .f8sr, .m6ac.sticky, .m6ac > .sticky) body { overflow-x: visible; }
}

/*.m6pr-compact {}*/
.m6pr-compact header { margin-bottom: 0; }
.m6pr-compact .r6rt { font-size: calc(var(--main_fz) * 0.8571428571); }
.m6pr-compact p + .r6rt { margin-top: -11px; }
/*.m6pr-compact p:not(.l4ch),*/ .m6pr-compact .l4pl { margin-bottom: 22px; }
/*.m6pr-compact .s1pr {}*/
.m6pr-compact .s1pr .small { display: inline; margin: 0; padding: 0; }
.m6pr-compact p + .s1pr { margin-top: -17px; }
.m6pr-compact footer { margin-top: 26px; }
.m6pr-compact .submit button { padding-left: 10px; padding-right: 10px; }

.module-spacer { display: block; width: 100%; height: 0; margin-top: 0; margin-bottom: 0; }

.module-color-palette[class^="palette-"].no-bg:before { background-color: transparent; }

.m6tb { margin-top: 17px; margin-bottom: var(--main_mr); --dist: 10px; }
.m6tb > nav { position: relative; z-index: 2; margin: 0 -12px 50px; }
.m6tb > nav:before { background: var(--sand); }
.m6tb > nav ul { list-style: none; margin: 0; padding: 0; }
.m6tb > nav ul li { position: relative; z-index: 2; }
.m6tb > nav ul li.link-btn, .m6tb > nav ul li.link-btn a { margin-bottom: 0; }
.m6tb > nav ul li.link-btn i { display: inline; position: relative; top: 0; margin: 0; font-size: 1em;}
.m6tb > nav ul a { display: block; text-decoration: none; }
.m6tb > nav ul li:not(.link-btn) a { padding: calc(var(--main_fz) * 0.7142857143) calc(var(--main_fz) * 0.8571428571); color: inherit; }
.m6tb > nav ul a > * + *, .m6tb .tabs-header > * + * { margin-left: var(--dist); }
.m6tb > nav ul li.active:not(.link-btn) a { background: var(--body_bg); color: var(--secondary_bg); font-weight: var(--main_fw_strong); text-decoration: none; cursor: default; }
.m6tb > div > * { margin-bottom: 20px; }
.m6tb .tabs-header { position: relative; z-index: 2; padding: 18px 20px 18px 0; border-bottom: 1px solid rgba(0,0,0,0); color: inherit; font-size: /*var(--size_16_f)*/ var(--main_fz); font-size: var(--main_ff); font-weight: var(--main_fw_h); line-height: var(--main_lh_h); letter-spacing: var(--main_ls); text-decoration: none; }
.m6tb .tabs-header h1, .m6tb .tabs-header h2, .m6tb .tabs-header h3, .m6tb .tabs-header h4, .m6tb .tabs-header h5, .m6tb .tabs-header h6 { margin: 0; padding: 0; font-size: 1em; font-weight: var(--main_fw_h); font-family: inherit; line-height: var(--main_lh_h); letter-spacing: var(--main_ls); }
.m6tb .tabs-header:before { content: "\e904"; left: auto; font-size: 0.375em; }
.m6tb .tabs-header:not(.toggle):after { bottom: -1px; border-bottom: 1px solid var(--custom_bd); }
.m6tb .tabs-header.toggle { margin-bottom: -2px; border-bottom-width: 0; }
.m6tb .tabs-header.toggle:before { transform: rotate(180deg); }
.m6tb.compact { margin: 0; }
.m6tb.compact > nav { margin: 0; }
.m6tb.compact > nav ul { margin-right: -30px; margin-bottom: 12px; }
.m6tb.compact > nav ul li { margin-right: 30px; }
.m6tb.compact > nav ul a { padding: 0; }

.m6wd { position: relative; z-index: 3; margin: 50px 0; padding: 50px 0 var(--main_mr); }
.m6wd:before { z-index: -2; background: var(--sand); }
.m6wd .r6rt .rating > * .fill { background-color: var(--sand); }
.m6fr.wide + .m6wd, .m6wd + .m6fr.wide { margin-top: -50px; }
.m6wd.large { padding-top: 96px; padding-bottom: 62px; }
.m6wd.overlay-content { color: var(--white); }
.m6wd.overlay-content:before, .m6wd.overlay-content .r6rt .rating > * .fill { background-color: var(--primary_text); }
.m6wd.overlay-content .swiper-pagination-bullet:before { background: var(--white); }
.m6wd.overlay-content .swiper-pagination-bullet-active:before { background: var(--secondary_bg); }
.m6wd.overlay-black { color: var(--white); }
.m6wd.overlay-black:before, .m6wd.overlay-black .r6rt .rating > * .fill { background-color: var(--black); }
.m6wd.overlay-black figure.background:before { background: var(--black); opacity: .22; }
.m6wd.overlay-black .swiper-pagination-bullet:before { background: var(--white); }
.m6wd.overlay-black .swiper-pagination-bullet-active:before { background: var(--secondary_bg); }
.m6wd.overlay-theme { color: var(--white); }
.m6wd.overlay-theme:before, .m6wd.overlay-theme .r6rt .rating > * .fill { background-color: var(--secondary_bg); }
.m6wd.overlay-theme figure.background:before { background: var(--secondary_bg); opacity: .22; }
.m6wd.overlay-theme .swiper-pagination-bullet:before { background: var(--white); }
.m6wd.overlay-tan, .m6wd.overlay-sand { color: inherit; }
.m6wd.overlay-tan:before, .m6wd.overlay-tan .r6rt .rating > * .fill { background-color: var(--tan); }
/*.m6wd.overlay-sand {}*/
.m6wd.overlay-sand:before, .m6wd.overlay-sand .r6rt .rating > * .fill { background-color: var(--tan); }

.media-flexible { display: block; position: relative; z-index: 2; }
.media-flexible > * { margin: 0; }
.media-flexible > *[style] { position: absolute; width: auto; margin: 0; border-radius: var(--b2p); }
.media-flexible.constrain > *[style]:not(picture) { position: relative; }
.media-flexible > figure picture, .media-flexible > figure > a, .media-flexible > figure > a picture, .media-flexible > figure > a img, .media-flexible > figure > a video, .media-flexible > figure > a iframe, .media-flexible > figure > a svg { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; }
.media-flexible img { object-fit: cover; }
#root .media-flexible .link-btn { position: absolute; left: 0; right: 0; bottom: 0; margin-top: 0; margin-bottom: 0; }
.m6fr .media-flexible { overflow: visible; }
figure.constrain > picture { position: relative; }
#root [class*="media-flexible"] figure { display: block; left: auto; right: auto; top: auto; bottom: auto; margin: 0 !important; }
#root .media-flexible img, #root .media-flexible video, #root .media-flexible svg, #root .media-flexible iframe {
	width: 100% !important; height: 100% !important;
	object-fit: cover;
}

.media-shadow img, .media-shadow iframe, .media-shadow svg, .media-shadow video, .l4cl figure.media-shadow { box-shadow: var(--box_shadow); }

.n6br { position: relative; z-index: 3; font-size: var(--breadcrumbs_fz); --dist: 26px; }
.n6br.text-start, .n6br.text-end, .n6br.text-center { justify-content: var(--justify_content); }
.n6br ol, .n6br ul { overflow: hidden; list-style: none; margin: 0 calc(0px - var(--dist)) 13px -4px; padding: 0 0 0 4px; }
.n6br li { position: relative; margin: 0 var(--dist) 0 0; }
.n6br li:before { content: "\e906"; right: 100%; left: auto; width: var(--dist); font-size: 0.7em; text-align: center; opacity: .17; }
.n6br li.text-end { margin-left: auto; }
.n6br li.text-start { margin-left: 0; margin-right: auto; }
.n6br li a { color: inherit; opacity: .53; outline-offset: -2px; }
.n6br p { margin-bottom: 8px; }
.n6br p i { display: block; top: 0; line-height: calc(var(--main_fz) * var(--main_lh)); }
.n6br p i.size-content { font-size: 1em; }
.n6br p .icon-upload:not([class*="size-"]), .n6br i.size-18 { font-size: max(1.4285714286em, 20px); }

.n6pg, .spr-pagination { margin-top: 26px; margin-bottom: 11px; padding-top: 17px; border-top: 1px solid var(--custom_bd); font-size: var(--main_fz); --dist: 24px; }
.n6pg ol, .n6pg ul, .spr-pagination > div { list-style: none; margin-right: calc(0px - var(--dist)); margin-bottom: var(--main_mr); padding: 0; }
.n6pg li, .spr-pagination > div > * { margin-right: var(--dist); }
#root .n6pg li:before { border-width: 0; }
.n6pg li a, .spr-pagination > div > * a { display: block; position: relative; z-index: 2; text-decoration: none; }
.n6pg li:not(.prev, .next) a:before, .spr-pagination > div > *:not(.spr-pagination-prev, .spr-pagination-next) a:before { left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
.n6pg li.prev a, .n6pg li.next a, .spr-pagination > div > .spr-pagination-prev a, .spr-pagination > div > .spr-pagination-next a { overflow: hidden; border-radius: 99px; width: calc(var(--main_fz) * 2.6428571429); height: calc(var(--main_fz) * 2.6428571429); color: var(--secondary_text); font-size: 0.5714285714em; text-indent: -3000em; text-align: left; direction: ltr; }
.n6pg li.prev a:after, .spr-pagination > div > .spr-pagination-prev a:after, .n6pg li.next a:after, .spr-pagination > div > .spr-pagination-next a:after { left: 0; right: 0; top: 0; bottom: 0; width: auto; margin: 0; padding: 1px 0 0; box-shadow: var(--btn_sh_inner); border-radius: 999px; border: 0 solid var(--secondary_bg_btn); background: var(--secondary_bg_btn); }
.n6pg li.prev a:after, .spr-pagination > div > .spr-pagination-prev a:after { content: "\e907"; padding-right: 1px; }
.n6pg li.next a:after, .spr-pagination > div > .spr-pagination-next a:after { content: "\e906"; padding-left: 1px; }
#root .n6pg.inv li.prev a, #root .n6pg.inv li.next a, #root .spr-pagination.inv .spr-pagination-prev a, #root .spr-pagination.inv .spr-pagination-next a { background: none; color: var(--secondary_bg_btn); }
#root .n6pg.inv li.prev a:after, #root .n6pg.inv li.next a:after, #root .spr-pagination.inv .spr-pagination-prev a:after, #root .spr-pagination.inv .spr-pagination-next a:after { box-shadow: none; border-width: var(--btn_bd); background: none; }
#root .n6pg li.prev a:before, #root .n6pg li.next a:before, .spr-pagination > div > .spr-pagination-prev a:before, .spr-pagination > div > .spr-pagination-next:before { border-radius: 99px; }
.n6pg li.prev, .spr-pagination > div > .spr-pagination-prev { margin-right: 22px; }
.n6pg li.next, .spr-pagination > div > .spr-pagination-next { margin-left: -3px; }
.n6pg p { margin-right: auto; }
.n6pg:first-child, .l4cm + .n6pg, .table-wrapper + .n6pg, table + .n6pg, .spr-pagination:first-child, .l4cm + .spr-pagination, .table-wrapper + .spr-pagination, table + .spr-pagination { margin-top: 0; padding-top: 0; border-top-width: 0; }
.n6pg .text-center { width: 100%; }

.no-zindex { position: relative; }

.popup-a { display: none; }

.r6rt, .l4rv .spr-starratings, .l4rv .spr-starrating {
	position: relative; color: inherit;
	align-items: center;
	justify-content: var(--justify_content);
}
.r6rt .rating { display: inline-block; position: relative; top: 0; height: var(--main_fz); margin: 0 calc(var(--main_fz) * 0.3571428571) 0 0; padding: 0; box-shadow: none; border-radius: 0; background: none; color: inherit; font-size: calc(var(--main_fz) * 0.8571428571); line-height: 1; text-align: left; text-indent: -3000em; }
.r6rt .rating > *, .l4rv .spr-starratings > i, .l4rv .spr-starrating a { display: block; position: relative; z-index: 2; top: 0; float: left; width: calc(var(--main_fz) * 0.8571428571); height: 100%; margin: 0 calc(var(--main_fz) * 0.1428571429) 0 0; text-decoration: none; }
.r6rt .rating > *:before, .l4rv .spr-starratings > i:before, .l4rv .spr-starrating a:before { content: "\e933"; display: block; z-index: 1; width: auto; color: var(--primary_text); font-family: i; opacity: .17; }
.r6rt .rating > * .fill { overflow: hidden; right: auto; z-index: 2; background: var(--body_bg); color: var(--secondary_bg); }
.r6rt .rating > * .fill:before { content: "\e933"; }
.l4cl .r6rt .rating-label, .l4in .r6rt .rating-label { opacity: .53; }

.recommendation-modal__container {
	visibility: hidden; position: fixed; left: 50%; top: 50%; z-index: 1000000; max-width: 360px !important; padding: 28px 32px 6px; box-shadow: none !important; border-radius: var(--b2r); background: var(--body_bg); font-size: var(--main_fz); line-height: var(--main_lh) !important; text-align: center; outline: none !important;
	transform: translate(-50%, -50%); opacity: 0;
}
.recommendation-modal__backdrop { visibility: hidden; position: fixed; left: 0; right: 0; top: 0; bottom: 0; z-index: 999999; width: auto !important; height: auto !important; background: var(--primary_text) !important; opacity: 0; }

.shopify-installments-container:not(:empty) { margin-bottom: 20px; }


.swiper-wrapper, .swiper-slide { list-style: none; margin: 0; padding: 0; }
.swiper-custom-pagination { font-size: var(--size_16_f); text-align: center; }
.autoplay .swiper-wrapper { transition-timing-function: linear; }
#root .autoplay .swiper-outer { overflow: visible; }
.autoplay .swiper-custom-pagination { display: none; }
#root .swiper-button-disabled { opacity: .35; cursor: default; }

.w160, #content.w160, .popup-a.w160 .box-inner { width: 100%; max-width: 160px; }
.w300, #content.w300, .popup-a.w300 .box-inner { width: 100%; max-width: 300px; --glw: 300px; }
.link-btn .w300 { min-width: 300px; max-width: none; }
.w340, #content.w340, .popup-a.w340 .box-inner { width: 100%; max-width: 340px; --glw: 340px; }
.w360, #content.w360, .popup-a.w360 .box-inner { width: 100%; max-width: 360px; --glw: 360px; }
.w380, #content.w380, .popup-a.w380 .box-inner { width: 100%; max-width: 380px; --glw: 380px; }
.w420, #content.w420, .popup-a.w420 .box-inner { width: 100%; max-width: 420px; --glw: 420px; }
.w480, #content.w480, .popup-a.w480 .box-inner { width: 100%; max-width: 480px; --glw: 480px; }
.w520, #content.w520, .popup-a.w520 .box-inner { width: 100%; max-width: 520px; --glw: 520px; }
.w560, #content.w560, .popup-a.w560 .box-inner { width: 100%; max-width: 560px; --glw: 560px; }
.w580, #content.w580, .popup-a.w580 .box-inner { width: 100%; max-width: 580px; --glw: 580px; }
.w600, #content.w600, .popup-a.w600 .box-inner { width: 100%; max-width: 600px; --glw: 600px; }
.w630, #content.w630, .popup-a.w630 .box-inner { width: 100%; max-width: 630px; --glw: 630px; }
.w690, #content.w690, .popup-a.w690 .box-inner { width: 100%; max-width: 690px; --glw: 690px; }
.w720, #content.w720, .popup-a.w720 .box-inner { width: 100%; max-width: 720px; --glw: 720px; }
.w740, #content.w720, .popup-a.w740 .box-inner { width: 100%; max-width: 740px; --glw: 740px; }
.w780, #content.w780, .popup-a.w780 .box-inner { width: 100%; max-width: 780px; --glw: 780px; }
.w900, #content.w900, .popup-a.w900 .box-inner { width: 100%; max-width: 900px; --glw: 900px; }
.w940, #content.w940, .popup-a.w940 .box-inner { width: 100%; max-width: 940px; --glw: 940px; }
.w1020, #content.w1020, .popup-a.w1020 .box-inner { width: 100%; max-width: 1020px; --glw: 1020px; }
.w300.align-center, .w360.align-center, .w480.align-center, .w520.align-center, .w630.align-center, .w560.align-center, .w690.align-center, .w720.align-center, .w780.align-center, .w900.align-center, .w940.align-center, .w1020.align-center, .align-center[style*="max-width:"] { margin-left: auto; margin-right: auto; }
.cols > .w300, .cols > .w360, .cols > .w480, .cols > .w520, .cols > .w560, .cols > .w630, .cols > .w690, .cols > .w720, .cols > .w780, .cols > .w900, .cols > .w940, .cols > .w1020 { width: auto; }

#root .shopify-cleanslate div, .shopify-cleanslate > div > span { width: auto !important; font-size: var(--main_fz) !important; }
#root .shopify-cleanslate button { display: inline !important; float: none !important; min-width: 0 !important; margin: 0 !important; padding: 0 !important; box-shadow: none !important; border-width: 0 !important; background: none !important; color: var(--secondary_bg) !important; font-size: 1em !important; font-weight: inherit !important; line-height: inherit !important; text-decoration: underline !important; }
#root .shopify-cleanslate button * { color: inherit !important; }
[data-whatintent="mouse"] #root .shopify-cleanslate button:hover { text-decoration: none !important; }
#root .shopify-cleanslate button:before, #root .shopify-cleanslate button:after { display: none; }

/*.shopify-section[style*="--glw:"] { width: 100%; max-width: var(--glw); margin-left: auto; margin-right: auto; }*/


/*! Content --------- */
.fw-300 { font-weight: 300; }
.fw-400 { font-weight: 400; }
#root .font-regular { font-weight: var(--main_fw); }
.fw-600, .semi { font-weight: 600; }
.strong, strong, b, #nav > ul > li.active > a, #nav-bar > ul > li.active > a, .l4cn a.email, .l4cn a[href*="tel:"], .submit a, .l4cl .link-btn a, .l4ca footer, .l4in li.active a, label.strong, .check.inside label.strong span, .check label.strong, .countdown.compact .simply-seconds-section, .n6br a.overlay-theme, #nav > ul > li > a.strong, #nav-bar > ul > li > a.strong { font-weight: var(--main_fw_strong); }
.fw-700 { font-weight: 700; }
.em, em, i, cite { font-style: italic; }
i { top: 0; }

q, blockquote { display: block; font-size: var(--main_h4); font-style: italic; line-height: var(--main_lh) /*1.6111111111*/; }
q { margin: 12px 0; }
q h1, q h2, q h3, q h4, q h5, q h6, q p { display: inline; }
q:has(h1, h2, h3, h4, h5, h6) { font-weight: var(--main_fw_h); font-style: var(--main_fs_h); font-family: var(--main_ff_h); line-height: var(--main_lh_h); letter-spacing: var(--main_ls_h); }
q:has(h1) { font-size: var(--main_h1); }
q:has(h2) { font-size: var(--main_h2); }
q:has(h3) { font-size: var(--main_h3); }
q:has(h4) { font-size: var(--main_h4); }
q:has(h5) { font-size: var(--main_h5); }
q:has(h6) { font-size: var(--main_h6); }
blockquote { position: relative; z-index: 2; padding: 12px 0 .1px 32px; }
blockquote:before { border: 0 solid var(--secondary_bg); border-left-width: 6px; }
blockquote > * { margin-bottom: var(--main_mr_h); }
blockquote .small, blockquote .size-14 { font-style: normal; }

.ff-primary { font-family: var(--main_ff_h); font-weight: var(--main_fw_h); font-style: var(--main_fs_h); --main_fw_strong: var(--main_fw_h); letter-spacing: var(--main_ls_h); }
.ff-secondary { font-family: var(--main_ff); font-weight: var(--main_fw); font-style: var(--main_fs); --main_fw_strong: var(--main_fw); letter-spacing: var(--main_ls); }

.text-start, .text-left, .l4ft li.text-start, #nav, #nav-bar, #nav-top, #nav-user, #search { text-align: var(--text_align_start); --justify_content: flex-start; }
.text-center, .l4ft li.text-center { text-align: center; --justify_content: center; }
.text-end, .text-right, .l4ft li.text-end { text-align: var(--text_align_end); --justify_content: flex-end; }
.text-justify { text-align: justify; --justify_content: space-between; }
.text-uppercase { text-transform: uppercase; --btn_tt: uppercase; }
.text-underline { text-decoration: underline; }
p.text-overflow, .text-overflow.l4cl h1, .text-overflow.l4cl h2, .text-overflow.l4cl h3, .text-overflow.l4cl h4, .text-overflow.l4cl h5, .text-overflow.l4cl h6, .text-overflow.l4cl li figure + p, .text-overflow.l4cl h1 a, .text-overflow.l4cl h2 a, .text-overflow.l4cl h3 a, .text-overflow.l4cl h4 a, .text-overflow.l4cl h5 a, .text-overflow.l4cl h6 a, .text-overflow.l4cl li figure + p a { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

.size-8, .m6as .size-8, .check.size-8, .l4ft .size-8, .m6fr .size-8 { font-size: var(--size_8_f); }
.size-10, .m6as .size-10, .check.size-10, .l4ft .size-10, .m6fr .size-10, .l4cl.category .size-10 { font-size: var(--size_10_f); }
.size-12, .m6as .size-12, .check.size-12, .l4ft .size-12, .m6fr .size-12, .l4cl.category .size-12 { font-size: var(--main_fz_small); --main_fz: var(--main_fz_small); }
.size-13, .m6as .size-13, .check.size-13, .l4ft .size-13, .m6fr .size-13, .l4cl.category .size-13 { font-size: var(--size_13_f); }
.size-14, .m6as .size-14, .check.size-14, .l4ft .size-14, .m6fr .size-14, .l4cl.category .size-14 { font-size: var(--size_14_f); }
.size-16, .m6as .size-16, .check.size-16, .l4ft .size-16, .m6fr .size-16, .l4cl.category .size-16 { font-size: var(--size_16_f); }
.size-18, .m6as .size-18, .check.size-18, .l4ft .size-18, .m6fr .size-18, .l4cl.category .size-18 { font-size: var(--size_18_f); }
.size-20, .m6as .size-20, .check.size-20, .l4ft .size-20, .m6fr .size-20, .l4cl.category .size-20 { font-size: var(--size_20_f); }
.size-22, .m6as .size-22, .check.size-22, .l4ft .size-22, .m6fr .size-22, .l4cl.category .size-22 { font-size: var(--size_22_f); }
.size-24, .m6as .size-24, .check.size-24, .l4ft .size-24, .m6fr .size-24, .l4cl.category .size-24 { font-size: var(--size_24_f); }
.size-26, .m6as .size-26, .check.size-26, .l4ft .size-26, .m6fr .size-26, .l4cl.category .size-26 { font-size: var(--size_26_f); }
.size-28, .m6as .size-28, .check.size-28, .l4ft .size-28, .m6fr .size-28, .l4cl.category .size-28 { font-size: var(--size_28_f); }
.size-30, .m6as .size-30, .check.size-30, .l4ft .size-30, .m6fr .size-30, .l4cl.category .size-30 { font-size: var(--size_30_f); }
.size-32, .m6as .size-32, .check.size-32, .l4ft .size-32, .m6fr .size-32, .l4cl.category .size-32 { font-size: var(--size_32_f); }
.size-34, .m6as .size-34, .check.size-34, .l4ft .size-34, .m6fr .size-34, .l4cl.category .size-34 { font-size: var(--size_34_f); }
.size-36, .m6as .size-36, .check.size-36, .l4ft .size-36, .m6fr .size-36, .l4cl.category .size-36 { font-size: var(--size_36_f); }
.size-38, .m6as .size-38, .check.size-38, .l4ft .size-38, .m6fr .size-38, .l4cl.category .size-38 { font-size: var(--size_38_f); }
.size-40, .m6as .size-40, .check.size-40, .l4ft .size-40, .m6fr .size-40, .l4cl.category .size-40 { font-size: var(--size_40_f); }
.size-46, .m6as .size-46, .check.size-46, .l4ft .size-46, .m6fr .size-46, .l4cl.category .size-46 { font-size: var(--size_46_f); }
.size-48, .m6as .size-48, .check.size-48, .l4ft .size-48, .m6fr .size-48, .l4cl.category .size-48 { font-size: var(--size_48_f); }
.size-50, .m6as .size-50, .check.size-50, .l4ft .size-50, .m6fr .size-50, .l4cl.category .size-50 { font-size: var(--size_50_f); }
.size-52, .m6as .size-52, .check.size-52, .l4ft .size-52, .m6fr .size-52, .l4cl.category .size-52 { font-size: var(--size_52_f); }
.size-56, .m6as .size-56, .check.size-56, .l4ft .size-56, .m6fr .size-56, .l4cl.category .size-56 { font-size: var(--size_56_f); }
.size-60, .m6as .size-60, .check.size-60, .l4ft .size-60, .m6fr .size-60, .l4cl.category .size-60 { font-size: var(--size_60_f); }
.size-70, .m6as .size-70, .check.size-70, .l4ft .size-70, .m6fr .size-70, .l4cl.category .size-70 { font-size: var(--size_70_f); line-height: 1; letter-spacing: -.035em; }
.size-content { --main_fz: var(--size_content); --mob_fz: var(--size_content); font-size: var(--main_fz); }
.size-small { font-size: var(--main_fz_small); }
.size-h-small { font-size: var(--main_h_small); }

.lh-compact { line-height: var(--main_lh_hc); }

.overlay-a, .overlay-theme, .m6tx .overlay-theme, #root .l4cl li.link a, #root .l4dr li.active > a, .l4dr li.has-social.toggle > a i, .l4in li.active a, .accordion-a details[open] summary, #root .l4cu.overlay-theme li > span, .n6br a.overlay-theme, .n6br li.strong a, .n6br li a.strong, .m6bx .l4in a { color: var(--secondary_bg); }
#nav > ul > li > a.overlay-theme, #nav-bar > ul > li > a.overlay-theme { color: var(--custom_top_nav_fg_hover); }
#nav > ul > li > a.overlay-theme, #nav-bar > ul > li > a.overlay-theme { color: var(--custom_top_nav_fg_hover); }

.overlay-b, .overlay-content, .js select.changed, #root .l4cu.overlay-content li > span, .l4ft li.overlay-content { color: var(--primary_text); }
.overlay-c, .overlay-gray, .m6tx .overlay-gray, #root .l4cu.overlay-gray li > span { color: var(--gray_text); --product_label_text: var(--white); }
.overlay-d, .overlay-lime, .m6tx .overlay-lime, .overlay-tertiary, #root .l4cu.overlay-lime li > span { color: var(--tertiary_bg); }
.overlay-e, .overlay-sky, .m6tx .overlay-sky, #root .l4cu.overlay-sky li > span { color: var(--sky); }
.overlay-f, .overlay-pine, .m6tx .overlay-pine, #root .l4cu.overlay-pine li > span { color: var(--pine); }
.overlay-g, .overlay-rose, .m6tx .overlay-rose, #root .l4cu.overlay-rose li > span { color: var(--rose); --price_color: var(--rose); }
.overlay-h, .overlay-tan, .m6tx .overlay-tan, #root .l4cu.overlay-tan li > span { color: var(--tan); }
.overlay-btn, .m6tx .overlay-btn { color: var(--secondary_bg_btn); }
.overlay-quaternary, .m6tx .overlay-quaternary, #root .l4cu.overlay-quaternary li > span { color: var(--quaternary_bg); }
.overlay-quinary, .m6tx .overlay-quinary, #root .l4cu.overlay-quinary li > span { color: var(--quinary_bg); }
.overlay-content { color: inherit; }
.l4al .overlay-content:before { background: var(--content); }
.l4al .overlay-gray:before { background: var(--gray); }
.l4al .overlay-lime:before { background: var(--lime); }
.l4al .overlay-sky:before { background: var(--sky); }
.l4al .overlay-pine:before { background: var(--pine); }
.l4al .overlay-rose:before { background: var(--rose); }
.l4al .overlay-tan:before { background: var(--tan); }
.l4al .overlay-tan { color: var(--primary_text); }
.overlay-gray { --product_label_bg: rgba(51,51,51, .4); }
.overlay-tertiary { --product_label_bg: var(--tertiary_bg); }
.overlay-quaternary { --product_label_bg: var(--quaternary_bg); }
.overlay-quinary { --product_label_bg: var(--quinary_bg); }
.overlay-rose { --product_label_bg: var(--alert_error); }
.overlay-sale, .m6tx .overlay-sale { color: var(--product_label_bg); --product_label_bg: var(--sale_label_bg); --product_label_text: var(--sale_label_text); }
i.overlay-sale:after { color: var(--product_label_text); }
.overlay-dark { --product_label_bg: var(--primary_text); }
.overlay-content { --product_label_bg: var(--sand); --product_label_text: var(--primary_text); }
.overlay-preorder { --product_label_bg: var(--product_label_bg_custom); --product_label_text: var(--product_label_text_custom); }
.overlay-gradient:not(.inv) {
	background: var(--theme_bg_gradient);
	background-clip: text; -webkit-background-clip: text;
	text-fill-color: transparent; -webkit-text-fill-color: transparent;
}

.overlay-error, .l4ca .overlay-error { color: var(--alert_error); }
.overlay-valid, .strong .icon-check, h1 .icon-check, h2 .icon-check, h3 .icon-check, h4 .icon-check, h5 .icon-check, h6 .icon-check, table .icon-check { color: var(--alert_valid); }

.lead { font-size: var(--main_lead); font-weight: 500; line-height: var(--main_lh_l); }
h1 + .lead, h2 + .lead, h3 + .lead, h4 + .lead, h5 + .lead, h6 + .lead, .h1 + .lead, .h2 + .lead, .h3 + .lead, .h4 + .lead, .h5 + .lead, .h6 + .lead, legend + .lead { margin-top: 16px; }

.s1hd, .title-font, .l4cl.category h1, .l4cl.category h2, .l4cl.category h3, .l4cl.category h4, .l4cl.category h5, .l4cl.category h6 { font-family: var(--main_ff_h); font-weight: var(--main_fw_h); font-style: var(--main_fs_h); --main_fw_strong: var(--main_fw_h); letter-spacing: var(--main_ls_h); }
#nav-bar.s1hd, #nav.s1hd { --main_fw_strong: var(--main_fw_h_strong); }

.s1bl { -webkit-filter: blur(3px); filter: blur(3px); }

/*.s1br {}*/
.s1br .rating-label { display: block; overflow: hidden; position: relative; width: 100%; height: 2px; margin: 22px 0 0; border-radius: var(--b2r); background: var(--sand); }
.s1br .rating-label > .bar { display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; bottom: 0; border-radius: var(--b2r); background: var(--secondary_bg); }
.text-center .s1br, .text-center.s1br { max-width: 302px; margin-left: auto; margin-right: auto; }

.s1bx {
	position: relative; z-index: 2; max-width: 100%; padding: 0 10px; color: var(--white); font-size: var(--main_fz_small); font-weight: var(--main_fw_strong); line-height: var(--main_lh); text-align: center;
	-webkit-box-decoration-break: clone; box-decoration-break: clone;
}
#root .s1bx { color: var(--secondary_btn_text); }
#root .link-btn a.inline .s1bx { color: var(--secondary_text); }
.s1bx:before { top: 0; bottom: 0; box-shadow: var(--btn_sh_inner); border-radius: var(--b2r); background: var(--secondary_bg); }
#root .s1bx.inv { color: var(--secondary_bg); }
.s1bx.inv:before { box-shadow: none; border: 1px solid var(--secondary_bg); background: none; }
.s1bx.size-m:before { top: -2px; bottom: -2px; }
span.s1bx { display: inline-block; white-space: nowrap; }
.s1bx.rounded { min-width: calc(var(--main_fz_small) * var(--main_lh_l)); }
#nav > ul > li > a .s1bx:not([class*="size-"]), #nav-bar > ul > li > a .s1bx:not([class*="size-"]) { font-size: 1em; }
#nav > ul > li > a .s1bx:not(.strong, [class*="fw-"]), #nav-bar > ul > li > a .s1bx:not(.strong, [class*="fw-"]) { font-weight: inherit; }
#nav > ul > li > a .s1bx:not(.circle):before, #nav-bar > ul > li > a .s1bx:not(.circle):before { top: -3px; bottom: -3px; }
.s1bx img { width: auto !important; max-height: 10px !important; margin-left: 6px; border-radius: 0; }
#root .s1bx.rounded, .s1bx.rounded:before { border-radius: 999px; text-align: center; }
.link-btn .s1bx, .submit .s1bx { margin-top: -10px; }
.link-btn a.s1bx { font-size: var(--main_fz_small); font-weight: var(--main_fw_strong); line-height: var(--main_lh_l); }
.link-btn > .s1bx, .submit > .s1bx { margin-top: 0; }
.link-btn .s1bx:not([class*="size-"]), .submit .s1bx:not([class*="size-"]) { font-size: 1em; }
.link-btn .s1bx:not(.inline), button .s1bx:not(.inline), .submit .s1bx:not(.inline) { display: block; }
.link-btn .s1bx.inline, .submit .s1bx.inline { margin-left: 5px; }
.link-btn .s1bx.rounded, .submit .s1bx.rounded { min-width: calc(var(--main_fz) * max(var(--main_lh), 1.8)); }
.link-btn .s1bx.inline.rounded, .submit .s1bx.inline.rounded { min-width: calc(var(--main_fz) * max(var(--main_lh), 1.8)); }
.link-btn .s1bx.inline.rounded:before, .submit .s1bx.inline.rounded:before {
	top: 50%; bottom: auto; height: 100%; min-height: calc(var(--main_fz) * max(var(--main_lh), 1.8));
	transform: translateY(-50%);
}
.link-btn .s1bx.inline.rounded.size-small, .submit .s1bx.inline.rounded.size-small { min-width: calc(var(--main_fz_small) * max(var(--main_lh), 1.8)); }
.link-btn .s1bx.inline.rounded.size-small:before, .submit .s1bx.inline.rounded.size-small:before { min-height: calc(var(--main_fz_small) * max(var(--main_lh), 1.8)); }
.s1bx.inline { padding-left: 6px; padding-right: 6px; }
.s1bx.plain, .s1lb.plain { --btn_sh_inner: none; }
.s1bx.size-12:before { top: -1px; bottom: -1px; }

.s1lb { display: block; margin: 0 -7px 10px 0; }
.s1lb, #root .s1lb { margin-bottom: 10px; }
#root figure .s1lb { margin-bottom: 0; }
.s1lb > span { display: block; }
#root .s1lb > span, #root .l4cl.hr .s1lb > span.rect, #root .l4cl.hr .s1lb > span.rounded, #root .l4cl.list .s1lb > span.rect, #root .l4cl.list .s1lb > span.rounded { overflow: hidden; position: relative; left: 0; top: 0; right: 0; width: auto; min-width: 0; max-width: 100%; height: auto; min-height: 0; margin: 0 7px 7px 0; padding: 4px 10px; box-shadow: var(--btn_sh_inner); border-radius: var(--b2r); border-color: var(--product_label_bg); background: var(--product_label_bg); color: var(--product_label_text); font-size: var(--sale_label_fz); font-weight: var(--main_fw_strong); line-height: var(--main_lh_h); text-transform: var(--main_tt); text-overflow: ellipsis; white-space: nowrap; text-align: inherit; text-indent: 0; --btn_sh_inner_c: var(--product_label_bg_dark); }
#root .l4cl.hr .s1lb > span.rect, #root .l4cl.hr .s1lb > span.rounded, #root .l4cl.list .s1lb > span.rect, #root .l4cl.list .s1lb > span.rounded { display: block; }
#root .s1lb > span span { display: inline; float: none; margin: 0; padding: 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; color: inherit; font-size: 1em; font-weight: inherit; line-height: var(--main_lh_h); }
#root .s1lb > span.wide { width: 100%; text-align: center; }
#root .s1lb > span.rect, #root .s1lb > span.rounded { width: var(--size); min-height: var(--size); padding: 18px; font-size: var(--main_fz); font-weight: var(--main_fw); line-height: var(--main_lh_l); text-align: center; word-wrap: break-word; white-space: normal; }
#root .s1lb > span.rect { border-radius: var(--b2r); --size: calc(var(--main_fz) * 8.7142857143); }
#root .s1lb > span.rounded { border-radius: 999px; --size: calc(var(--main_fz) * 10.6428571429); }
#root .s1lb > span.rect .s1pr, #root .s1lb > span.rounded .s1pr { display: block; margin: 4px 0 0; font-weight: var(--main_fw_strong); font-size: max(calc(var(--size) * 0.2098765432), 2.4285714286em); }
root .l4cl.hr .s1lb > span.rect .s1pr, #root .l4cl.hr .s1lb > span.rounded .s1pr, #root .l4cl.list .s1lb > span.rect .s1pr, #root .l4cl.list .s1lb > span.rounded .s1pr { display: inline; margin: 0; font-size: 1em; }
#root .s1lb .strong { font-weight: var(--main_fw_strong); }
figure .s1lb, .l4pr .s1lb { position: absolute; left: var(--label_dist); right: var(--label_dist); top: var(--label_dist); z-index: 10; pointer-events: none; }
figure .s1lb a, .l4pr .s1lb a { display: inline; position: relative; left: 0; right: 0; top: 0; bottom: 0; color: inherit; text-indent: 0; text-align: inherit; direction: inherit; }
.s1lb[class*="palette-"] { --product_label_bg: var(--primary_bg); --product_label_text: var(--primary_text); }
.s1lb.align-middle {
	top: 50%; bottom: auto; padding-top: 7px;
	transform: translateY(-50%);
}
.s1lb.align-bottom { top: auto; bottom: calc(var(--label_dist) - 7px); }
#root .s1lb.align-stretch { --label_dist: 0px; }
#root figure .s1lb.align-stretch:not(.align-bottom) > .rect { border-top-left-radius: 0; border-top-right-radius: 0; }
#root figure .s1lb.align-stretch.align-bottom > .rect { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
[dir="ltr"] #root figure .s1lb.align-stretch:not(.align-end):not(.align-bottom) > .rect:last-child { border-bottom-left-radius: 0; }
[dir="ltr"] #root figure .s1lb.align-stretch.align-end:not(.align-bottom) > .rect:last-child { border-bottom-right-radius: 0; }
[dir="ltr"] #root figure .s1lb.align-stretch.align-bottom:not(.align-end) > .rect:last-child { border-top-left-radius: 0; }
[dir="ltr"] #root figure .s1lb.align-stretch.align-bottom.align-end > .rect:last-child { border-top-right-radius: 0; }
[dir="rtl"] #root figure .s1lb.align-stretch:not(.align-end):not(.align-bottom) > .rect:last-child { border-bottom-right-radius: 0; }
[dir="rtl"] #root figure .s1lb.align-stretch.align-end:not(.align-bottom) > .rect:last-child { border-bottom-left-radius: 0; }
[dir="rtl"] #root figure .s1lb.align-stretch.align-bottom:not(.align-end) > .rect:last-child { border-top-right-radius: 0; }
[dir="rtl"] #root figure .s1lb.align-stretch.align-bottom.align-end > .rect:last-child { border-top-left-radius: 0; }

.s1nw { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }

.s1pr, p.s1pr, span.s1pr { color: var(--price_color); font-weight: var(--price_fw); font-size: var(--price_fz); font-family: var(--price_ff); font-style: var(--price_fs); line-height: var(--price_lh); letter-spacing: var(--price_ls); }
.l4ca .s1pr { font-size: var(--price_fz); line-height: var(--price_lh); white-space: nowrap; }
.s1pr .small { display: block; margin: 3px 0 0; color: inherit; font-size: 0.8571428571em; text-decoration: none; }
.s1pr .small { display: block; margin: 1px 0 2px; color: var(--primary_text_h); font-family: var(--main_ff); font-weight: var(--main_fw); font-size: 0.8571428571em; letter-spacing: var(--main_ls); text-decoration: none; opacity: .53; }
.s1pr span[class*="overlay"]:not(.old-price, .s1bx), .l4cl .s1pr span[class*="overlay"]:not(.old-price, .s1bx) { display: block; margin: 0 0 4px; font-size: var(--main_fz_small); text-decoration: none; opacity: 1; }
.s1pr .strong { font-weight: var(--main_fw_strong); }
.s1pr-fw { font-weight: var(--price_fw); }
.s1pr-fz { font-weight: var(--price_fz); }

#root .price-varies { color: var(--price_color); text-decoration: none; }
#root .old-price { font-weight: var(--main_fw); text-decoration: line-through; }
#root .old-price:not([class*="overlay-"]) { color: var(--price_color_old); }

.s1tt:not(.ready), .s1tt > * { display: none; }
.s1tt { display: inline; position: relative; left: 14px; z-index: 2; margin: 0 0 0 -10px; }
.s1tt .s1tt-popup { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; text-align: left; text-indent: -3000em; direction: ltr; }
#root a.s1tt:after, .s1tt .s1tt-popup:after {
	content: ""; display: block; position: absolute; left: 50%; top: 50%; right: auto; bottom: auto; z-index: 9; min-width: 44px; height: 44px;
	transform: translate(-50%, -50%);
}
.s1tt > .icon-info { display: inline-block; position: relative; top: calc(var(--main_fz) * 0.**********); margin: 0; color: var(--primary_text_h); font-size: calc(var(--main_fz) * 1.1428571429); line-height: 1; opacity: .33; }
.s1tt-cont { position: relative; z-index: 20; }


/*! Links --------- */
shopify-accelerated-checkout, shopify-accelerated-checkout-cart {
	--wallet-button-height-horizontal: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh)) !important;
	--shopify-accelerated-checkout-button-block-size: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh)) !important;
	--shopify-accelerated-checkout-button-border-radius: var(--btn_br) !important;
	--wallet-button-border-radius: var(--btn_br) !important;
	--wallet-button-width-horizontal: 100% !important;
}

.inner-plain { --btn_sh_inner: none; }
a.square, button.square, input.square { --b2r: 0px; --btn_br: 0px; }

.link-btn, .submit {
	margin-right: calc(0px - var(--btn_dist)); margin-bottom: calc(var(--main_mr) - var(--btn_dist2)); font-size: var(--main_fz); --input_h: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh));
	justify-content: var(--justify_content);
}
.link-btn > *, .submit > * { display: block; margin-right: var(--btn_dist); margin-bottom: var(--btn_dist2); }
.submit > a { text-decoration: none; }
.link-btn a img, button img { margin-left: 2px; }
.link-btn a.inv, button.inv, .inv-btn button { box-shadow: none; border-width: 0; }
.link-btn a.inv, button.inv, .inv-btn button { --btn_bs_h: none; --btn_bc_h: var(--secondary_bg_btn); --btn_bd_h: var(--btn_bd); --btn_bw_h: var(--btn_bd); --btn_bg_h: none; }
#root .link-btn a.inv, #root button.inv, #root .inv-btn button { background: none; color: var(--secondary_bg_btn); /*--secondary_btn_text: var(--secondary_bg_btn);*/ }
#root .link-btn a.inv:before, #root button.inv:before, #root .inv-btn button:before { box-shadow: var(--btn_bs_h); border-color: var(--btn_bc_h); border-width: var(--btn_bd_h); background: var(--btn_bg_h); }

.inv-btn input[type="button"], .inv-btn input[type="reset"], .inv-btn input[type="submit"] { padding: calc(var(--btn_pv) - var(--btn_bd)) calc(var(--btn_ph) - var(--btn_bd)); box-shadow: none; border-width: var(--btn_bd); background: none; --secondary_btn_text: var(--secondary_bg_btn); }

.link-btn a.size-s, button.size-s, .link-btn.size-s a, .size-s > button, .link-btn .s1bx.size-s, .size-s button[class*="shopify-"] { min-height: 0; padding: calc(var(--btn_pv) * 0.65) calc(var(--btn_ph) * 0.7); }
.link-btn a.size-m, button.size-m, .link-btn.size-m a, .size-m > button, .link-btn .s1bx.size-m, .size-m button[class*="shopify-"] { min-height: 0; padding: calc(var(--btn_pv) * 1.2) calc(var(--btn_ph) * 1.25); }
.link-btn a.size-l, button.size-l, .link-btn.size-l a, .size-l > button, .link-btn .s1bx.size-l, .size-l button[class*="shopify-"] { min-height: 0; padding: calc(var(--btn_pv) * 1.35) calc(var(--btn_ph) * 1.5); }
.link-btn a.rounded, button.rounded, .link-btn a.rounded:before, button.rounded:before { border-radius: var(--btn_pv); }
.link-btn a.rounded.size-m, button.rounded.size-m, .link-btn a.rounded.size-m:before, button.rounded.size-m:before, .link-btn.size-m a, .size-m > button, .link-btn.size-m a:before, .size-m > button:before { border-radius: calc(var(--btn_pv) * 1.7); }
.link-btn a.rounded.size-l, button.rounded.size-l, .link-btn a.rounded.size-l:before, button.rounded.size-l:before, .link-btn.size-l a, .size-l > button, .link-btn.size-l a:before, .size-l > button:before { border-radius: calc(var(--btn_pv) * 2); }
.link-btn a.shadow:before, button.shadow:before, .s1bx.shadow:before, #root .link-btn a.shadow.inv:before, #root button.shadow.inv:before, #root .s1bx.shadow.inv:before { box-shadow: 0 3px 10px rgba(0,0,0,.2); }
.link-btn a.inline, #root button.inline { min-width: auto; min-height: 0; padding: 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-size: 1em; line-height: var(--main_lh); }
#root .link-btn a.inline, #root button.inline { --secondary_btn_text: var(--secondary_bg_btn); animation: none; }
.link-btn a.overlay-content, #root .link-btn a.overlay-content, #root button.overlay-content, .overlay-content button, #root .s1bx.overlay-content, .s1bx.overlay-content { color: var(--secondary_btn_text); --secondary_btn_text: var(--primary_text); --secondary_bg: var(--sand); --secondary_bg_btn: var(--sand); --secondary_bg_btn_dark: var(--sand); }
.link-btn a.overlay-content.inv, #root .link-btn a.overlay-content.inv { background: none; --btn_bg_h: none; --btn_bc_h: var(--alto); }
.link-btn a.overlay-tertiary, button.overlay-tertiary, .overlay-tertiary button, .s1bx.overlay-tertiary { --secondary_btn_text: var(--tertiary_text); --secondary_bg: var(--tertiary_bg); --secondary_bg_btn: var(--tertiary_bg); --secondary_bg_btn_dark: var(--tertiary_bg_dark); }
.link-btn a.overlay-blush, button.overlay-blush, .overlay-blush button, .s1bx.overlay-blush { --secondary_btn_text: var(--primary_text); --secondary_bg: var(--blush); --secondary_bg_btn: var(--blush); --secondary_bg_btn_dark: var(--blush); }
.link-btn a.overlay-quaternary, button.overlay-quaternary, .overlay-quaternary button, .s1bx.overlay-quaternary { --secondary_btn_text: var(--quaternary_text); --secondary_bg: var(--quaternary_bg); --secondary_bg_btn: var(--quaternary_bg); --secondary_bg_btn_dark: var(--quaternary_bg_dark); }
.link-btn a.overlay-quinary, button.overlay-quinary, .overlay-quinary button, .s1bx.overlay-quinary { --secondary_btn_text: var(--quinary_text); --secondary_bg: var(--quinary_bg); --secondary_bg_btn: var(--quinary_bg); --secondary_bg_btn_dark: var(--quinary_bg_dark); }
.link-btn a.overlay-sale, button.overlay-sale, .overlay-sale button, .s1bx.overlay-sale { --secondary_btn_text: var(--sale_label_text); --secondary_bg: var(--sale_label_bg); --secondary_bg_btn: var(--sale_label_bg); --secondary_bg_btn_dark: var(--sale_label_bg_dark); }
.link-btn a.overlay-rose, button.overlay-rose, .overlay-rose button, .s1bx.overlay-rose { --secondary_btn_text: var(--white); --secondary_bg: var(--alert_error); --secondary_bg_btn: var(--alert_error); --secondary_bg_btn_dark: var(--alert_error); }
.link-btn a.overlay-gray, button.overlay-gray, .overlay-gray button, .s1bx.overlay-gray { --secondary_btn_text: var(--white); --secondary_bg: var(--gray); --secondary_bg_btn: var(--gray); --secondary_bg_btn_dark: var(--gray); }
.link-btn a.overlay-gradient:not(.inv, .inline), button.overlay-gradient:not(.inv, .inline), .overlay-gradient:not(.inv, .inline) button, .s1bx.overlay-gradient:not(.inv, .inline) {
	--secondary_bg: var(--theme_bg_gradient); --secondary_bg_btn: var(--theme_bg_gradient); --secondary_bg_btn_dark: var(--theme_bg_gradient_dark); --pulse: var(--pine);
	background-clip: inherit; -webkit-background-clip: inherit;
	text-fill-color: currentcolor; -webkit-text-fill-color: currentcolor;
}
.link-btn a.overlay-white, button.overlay-white, .overlay-white button, .s1bx.overlay-white { --secondary_btn_text: var(--primary_text); --secondary_bg: var(--white); --secondary_bg_btn: var(--white); --secondary_bg_btn_dark: var(--white); }
.link-btn a.overlay-coal, button.overlay-coal, .overlay-coal button, .s1bx.overlay-coal { --secondary_btn_text: var(--white); --secondary_bg: var(--primary_text); --secondary_bg_btn: var(--primary_text); --secondary_bg_btn_dark: var(--black); }
.link-btn a.wide, .submit .wide { min-width: 296px; }
.link-btn .cols a.wide, .submit .cols .wide { width: 100%; min-width: 0; }
.link-btn i, button i, .submit i { display: inline-block; position: relative; top: .15em; margin-left: 3px; font-size: 1.4285714286em; line-height: 1px; }
.link-btn i, button i, .submit i { top: .15em; }
.link-btn i.icon, button i.icon, .submit i.icon { top: 0; }
.link-btn .icon-cart, button .icon-cart, .submit .icon-cart { left: -2px; top: .2em; font-size: 1.5714285714em; }
.link-btn [class*="icon-check"], button [class*="icon-check"], .submit [class*="icon-check"] { top: 0; font-size: .8em; }
.link-btn .icon-pin, button .icon-pin, .submit .icon-pin { top: .2em; margin-right: 6px; font-size: 1.2857142857em; }
.link-btn .icon-filter, button .icon-filter, .submit .icon-filter { top: 0; margin-right: 8px; font-size: 0.9285714286em; }
.link-btn .icon-envelope, button .icon-envelope, .submit .icon-envelope, .link-btn .icon-envelope-wide, button .icon-envelope-wide, .submit .icon-envelope-wide { top: 0; margin-right: 4px; font-size: 0.7142857143em; }
.link-btn .icon-chevron-down, button .icon-chevron-down, .submit .icon-chevron-down, .link-btn .icon-chevron-up, button .icon-chevron-up, .submit .icon-chevron-up { top: -.35em; font-size: 0.4285714286em; }
.link-btn .icon-chevron-left, button .icon-chevron-left, .submit .icon-chevron-left, .link-btn .icon-chevron-right, button .icon-chevron-right, .submit .icon-chevron-right { top: -.1em; font-size: 0.6428571429em; }
.link-btn .icon-chevron-left, button .icon-chevron-left, .submit .icon-chevron-left { margin-right: 3px; }
.link-btn .icon-chevron-right, button .icon-chevron-right, .submit .icon-chevron-right { margin-left: 3px; }
h1 + .link-btn, h2 + .link-btn, h3 + .link-btn, h4 + .link-btn, h5 + .link-btn, h6 + .link-btn { margin-top: var(--main_mr); }
button.w160, .link-btn a.w160 { max-width: 160px; }
.link-btn a.circle, button.circle { --s: var(--btn_circle_size); }
#root .link-btn a.circle, #root button.circle, .link-btn a.circle:before, button.circle:before {
	width: var(--s); height: var(--s); min-height: 0; min-width: 0; padding: 0; border-radius: 999px; text-align: left; text-indent: -3000em; direction: ltr;
	flex-grow: 0;
}
#root .link-btn a.circle i, #root button.circle i { display: block; margin-top: calc(0px - var(--s) * 0.5); font-size: calc(var(--s) * 0.4680851064); line-height: var(--s); }
#root .link-btn a.circle i[class*="icon-plus"], #root button.circle i[class*="icon-plus"] { font-size: calc(var(--s) * 0.4); }
#root .link-btn a.circle i[class*="icon-check"], #root button.circle i[class*="icon-check"] { font-size: calc(var(--s) * 0.3); }
/*.link-btn.single {}*/
.link-btn.single > * { min-width: 222px; }
.link-btn.single > .inline { min-width: 0; }
.link-btn.single > * + * { margin-top: 15px; }
.link-btn.cols { margin-left: 0; }
.link-btn.cols > * { border-left-width: 0; }
.link-btn.tags { margin-bottom: 36px; }
#root .link-btn.tags a { background: none; color: var(--primary_text); }
#root .link-btn.tags a:before { border-width: 1px; border-color: var(--custom_bd); background: none; }
.link-btn.tags + h1, .link-btn.tags + h2, .link-btn.tags + h3, .link-btn.tags + h4, .link-btn.tags + h5, .link-btn.tags + h6 { margin-top: 36px; }
/*.link-btn.wide {}*/
.link-btn.wide > * { width: 100%; }
.link-btn.wide > *:not(span, .inline) + .inline { margin-top: 8px; }


/*.link-more {}*/
p + p > .link-more:first-child, .js .link-more-clicked p + p > a.link-more[data-no="1"]:first-child { display: block; margin-top: calc(0px - var(--main_mr)); }
.link-more-clicked .link-more .icon-chevron-down:before { content: "\e908"; }
.js .link-more-clicked a.link-more[data-no="1"] { display: inline; }
.link-more-clicked .link-more span.hidden, .link-more-clicked .link-more span.was-hidden { display: inline; position: relative; left: 0; top: 0; }
div:not(.info) > p.limit { display: flex; }
div:not(.info).link-more-clicked > p.limit, li.hidden ~ .has-link-more a.link-more { display: block; }


/*! Media --------- */
img, iframe, object, embed, video { max-width: 100% !important; border-radius: var(--b2p); border-style: none; object-fit: contain; }
img, iframe, object, embed, video, picture { object-position: var(--ip_hor) var(--ip_ver); }
img { image-rendering: optimizeQuality; -ms-interpolation-mode: bicubic; }
body > div:first-child img, body > div[id] img, body > p:first-child + div img, body > div + div img { height: auto !important; }
img::selection { background: transparent; }
img::-moz-selection { background: transparent; }
iframe { display: block; border-width: 0 !important; outline: none; }
picture { width: 100%; }
figure { position: relative; z-index: 2; margin: 0 0 calc(var(--main_mr) * 0.7692307692); font-size: calc(var(--main_fz) * 0.8571428571); }
h1 + figure, h2 + figure, h3 + figure, h4 + figure, h5 + figure, h6 + figure, .h1 + figure, .h2 + figure, .h3 + figure, .h4 + figure, .h5 + figure, .h6 + figure { margin-top: 14px; }
figure + h3, figure + h4, figure + h5, figure + h6 { margin-top: 34px; }
figure + h1, figure + h2 { margin-top: 30px; }
figure.lead { margin-top: 24px; font-weight: var(--main_fw); font-size: calc(var(--main_fz) * 0.8571428571); line-height: var(--main_lh); }
figure > a { display: block; position: relative; width: 100%; text-decoration: none; }
figure > a picture { position: relative; z-index: 2; }
figure .icon-play, figure .icon-cube, .l4pr .icon-play { display: block; position: absolute; left: 0; right: 0; top: 50%; z-index: 9; margin: -35px 0 0; color: var(--white); font-size: var(--size_38_f); line-height: var(--size_70_f); text-align: center; text-indent: 0; }
figure .icon-play {
	left: 50%; right: auto; width: var(--size_70_f); height: var(--size_70_f); margin-top: 0; font-size: var(--size_70_f); pointer-events: none;
	transform: translate(-50%, -50%);
}
figure .icon-play:after { content: ""; display: block; position: absolute; left: 0; top: 0; bottom: 0; right: 0; border-radius: 99px; background: var(--black); opacity: .4; }
figure .icon-play:before { content: "\e980"; position: relative; z-index: 2; font-size: 1em; }
figure .link-btn.visible, .m6fr article > .link-btn { position: absolute; left: var(--label_dist); right: var(--label_dist); bottom: var(--label_dist); top: auto; z-index: 999; min-width: 0; width: auto; margin-bottom: calc(0px - var(--btn_dist2)); padding: 0; --btn_dist: var(--f8pr_submit_dist); }
figcaption { display: block; width: 100%; }
figcaption + *, * + figcaption { margin-top: 9px; }
.iframe-playing { display: block; position: relative; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; background: var(--black_static); }
figure .iframe-playing { display: block; position: absolute; z-index: 9; width: 100% !important; height: 100% !important; background: var(--black_static); }
.js [data-sal]:not(.sal-animate) { opacity: 0; }

picture video { display: block; }

#root .portrait, .orientation-portrait { --ratio: 1.25; }
#root .square, .orientation-square { --ratio: 1; }
#root .landscape, .orientation-landscape { --ratio: 0.75; }

[class*="orientation"] picture, picture[class*="orientation"], picture[style*="--ratio"], [style*="--ratio"] picture { display: block; position: relative; z-index: 2; width: 100%; padding-top: calc(var(--ratio) * 100%); }
.orientation-stripe { --ratio: 0.62; }
.orientation-landscape { --ratio: 0.75; }
.orientation-square { --ratio: 1; }
.orientation-portrait { --ratio: 1.25; }
#root [class*="orientation"] img, #root [class*="orientation"] svg, #root [class*="orientation"] iframe, #root [class*="orientation"] video, /*#root .shopify-section-header [class*="orientation"] a,*/ #root [style*="--ratio"] img, #root [style*="--ratio"] svg, #root [style*="--ratio"] iframe, #root [style*="--ratio"] video, #root [class*="orientation"] picture video { display: block; position: absolute; left: 0; right: 0; top: 0 /*!important*/; bottom: 0; width: 100% !important; height: 100% !important; /*object-fit: contain;*/ transform: none; }
#root [class*="orientation"] img, #root [style*="--ratio"] img { height: 100% !important; }

.m6as > figure iframe { display: block; position: relative; z-index: 1; width: 100% !important; }
#background iframe, #root .l4ft figure iframe, #root .m6fr figure iframe { display: block; width: 100% !important; height: 100% !important; border-radius: 0; }

.no-bd-radius { border-radius: 0; --b2p: 0px; --b2r: 0px; }
.rounded { --b2p: 999px; --b2i: 999px; }

/*.img-compare {}*/
.img-compare figure { width: 100%; }
.img-compare:not(.icv) > *:not(img:first-child, picture:first-child) { display: none; }

.img-mask[class*="mask-"] {
	-webkit-mask-image: var(--mask); mask-image: var(--mask);
	-webkit-mask-size: 100% 100%; mask-size: 100% 100%;
}
.img-mask.img-mask-star { --mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 12.6 12' viewBox='0 0 12.6 12'%3E%3Cpath d='m12.6 4.6-4.5-.4L6.3 0 4.5 4.2 0 4.6l3.4 2.9-1 4.5 3.9-2.4 3.9 2.4-1-4.5z'/%3E%3C/svg%3E"); }
.img-mask.img-mask-triangle { --mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 448 416' viewBox='0 0 448 416'%3E%3Cpath d='M432 416H16c-8.8 0-16-7.2-16-16 0-2.7.7-5.3 1.9-7.6l208-384c4.2-7.8 13.9-10.7 21.7-6.5 2.7 1.5 5 3.7 6.5 6.5l208 384c4.2 7.8 1.3 17.5-6.4 21.7-2.4 1.2-5 1.9-7.7 1.9z'/%3E%3C/svg%3E"); }
.img-mask.img-mask-circle { --mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 8.2 8.2' viewBox='0 0 8.2 8.2'%3E%3Ccircle cx='4.1' cy='4.1' r='4.1'/%3E%3C/svg%3E"); }

.img-multiply, .img-multiply-bg { position: relative; z-index: 2; }
.img-multiply img, img.img-multiply, .img-multiply-bg img, img.img-multiply-bg, #search figure.img-multiply, .m6pn figure.img-multiply, .l4ft figure.img-multiply, .img-multiply-bg picture, .img-multiply picture { mix-blend-mode: multiply; }
.img-multiply a { mix-blend-mode: multiply; }
figure.img-multiply:before, picture.img-multiply:before, picture.img-multiply.s4wi .swiper-slide a:before { border-radius: var(--b2p); background: var(--body_bg); }
figure.img-multiply-bg:before, picture.img-multiply-bg:before, picture.img-multiply-bg.s4wi .swiper-slide a:before { border-radius: var(--b2p); background: var(--gallery); }

.img-overlay { border: 1px solid var(--primary_bd); background: var(--primary_bg); opacity: var(--overlay_opacity); }
figure .img-overlay { border-width: 0; }

figure.maps, .image-map { width: 100%; max-width: 100%; }
img[src*="maps.gstatic.com"], .gm-style img { max-width: inherit !important; }
.gm-svpc img[style*="height: 30px"], .gm-svpc img[style*="height:30px"] { height: 30px !important; }
.gm-svpc img[style*="height: 40px"], .gm-svpc img[style*="height:40px"] { height: 40px !important; }
.map-canvas[style] { transform: none !important; }

.placeholder-svg { height: 100%; width: 100%; fill: hsla(0,0%,52%,.35); background-color: hsla(0,0%,52%,.1); object-fit: contain; }
.shopify-challenge__container .shopify-challenge__button { float: none; }

#root .img-contain, #root .img-contain * { object-fit: contain; }


/*! Tables --------- */
table { display: table; width: 100%; border-collapse: collapse; border-spacing: 0; --p_lr: 18px; }
caption { display: none; margin: 0; padding: 0 0 10px; font-weight: var(--main_fw_strong); line-height: var(--main_lh_h); text-align: left; }
th, td { padding: 9px var(--p_lr); border: 0 solid var(--custom_bd); font-weight: var(--main_fw); text-align: inherit; }
th { background: var(--sand); font-weight: var(--main_fw_strong); }
td { border-bottom-width: 1px; }
tr > *:first-child { border-left-width: 1px; }
tr > *:last-child { border-right-width: 1px; }
tr:first-child > * { border-top-width: 1px; }
tr:nth-child(2n) > td { background: var(--porcelain); }
h1 + table, h2 + table, h3 + table, h4 + table, h5 + table, h6 + table, h1 + .table-wrapper, h2 + .table-wrapper, h3 + .table-wrapper, h4 + .table-wrapper, h5 + .table-wrapper, h6 + .table-wrapper { margin-top: 22px; }
table.size-s tr > * { padding-top: 3px; padding-bottom: 3px; }
table.size-m tr > * { padding-top: 12px; padding-bottom: 12px; }
table.size-l tr > * { padding-top: 14px; padding-bottom: 14px; }


/*! Lists --------- */
ul.text-center, ol.text-center, .text-center ul, .text-center ol { list-style-position: inside; padding-left: 0; padding-right: 0; }

.l4ad { list-style: none; padding: 0; }
.l4ad li { position: relative; margin: 0 0 25px; padding: 0 0 7px 18px; border-bottom: 1px solid var(--custom_bd); }
#root .l4ad h1, #root .l4ad h2, #root .l4ad h3, #root .l4ad h4, #root .l4ad h5, #root .l4ad h6 { position: relative; z-index: 2; margin: 0 0 18px; font-size: var(--main_h_small); }
.l4ad h1 i, .l4ad h2 i, .l4ad h3 i, .l4ad h4 i, .l4ad h5 i, .l4ad h6 i { display: block; position: absolute; left: -18px; top: .125em; font-size: 0.875em; line-height: 1; }
.l4ad p, .l4ad ul { margin-bottom: 14px; }
.l4ad .l4cn { /*margin-bottom: 6px;*/ margin-right: -22px; }
.l4ad .l4cn li { margin-right: 22px; padding: 0 0 0 28px; border-width: 0; }
.l4ad .l4as li { margin-left: 0; margin-right: 0; padding-left: 0; padding-right: 0; }

.l4al { list-style: none; padding: 0; --btn_sh_inner: 0 2px 4px rgba(0,0,0,.1); }
.l4al > li:not([class*="overlay-"]) { color: var(--primary_text); }
.l4al li { position: relative; z-index: 3; margin: 0 0 10px; padding: var(--main_fz) calc(var(--main_fz) * 1.1428571429); }
.l4al li:before { border-radius: var(--b2r); border: 1px solid rgba(0,0,0,0); background: var(--body_bg); }
.l4al li[class*="overlay"]:before { opacity: .11; }
.l4al li:not([class*="overlay"]):before { border-color: var(--custom_bd); }
#root .l4al li.overlay-tan:before { opacity: 1; }
.l4al li:after { z-index: -2; box-shadow: var(--btn_sh_inner); border-radius: var(--b2r); background: var(--body_bg); }
.l4al img, .l4al svg, .l4al picture {
	width: auto;
	object-fit: contain;
	flex-shrink: 0;
}
.l4al svg { display: inline-block; position: relative; top: -.1em; height: var(--main_fz); margin-right: 3px; }
.l4al picture { max-height: none !important; }
#root .l4al p, #root .l4al h1, #root .l4al h2, #root .l4al h3, #root .l4al h4, #root .l4al h5, #root .l4al h6 { margin-bottom: 1px; font-size: 1em; line-height: var(--main_lh); }
.l4al li.cols { padding-left: 0; }
.l4al li .cols.cols-mobile > * ~ * { max-width: 40%; }
.l4al li.size-m { padding-top: 17px; padding-bottom: 17px; }
.l4al li > i:not(.icon-check) { display: block; position: absolute; left:  calc(var(--main_fz) * 1.1428571429px); top: var(--main_fz); margin-right: 2px; margin-left: 0; font-size: calc(var(--main_fz) * 1.4285714286); line-height: calc(var(--main_fz) * var(--main_lh)); }
.l4al li > i:not(.icon-check) ~ * { padding-left: calc(var(--main_fz) * 2.5); }
.l4al li > i.icon-check { display: inline-block; position: relative; top: .025em; line-height: 1px; }
.l4al li > i.sticky { display: block; position: absolute; left: 10px; top: 0; margin-top: -10px; padding: 0 6px; border-width: 0; background: var(--body_bg); font-size: var(--size_20_f); line-height: var(--size_20_f); }
#root .l4al li > i.sticky ~ * { padding-left: 0; padding-right: 0; }
.l4al .close { display: block; overflow: hidden; position: absolute; right: 0; top: 0; z-index: 9; width: 44px; height: 44px; /*color: var(--gray_text);*/ font-size: var(--size_12); text-indent: -3000em; text-align: left; direction: ltr; }
.l4al .close:before { content: "\e91f"; }
#root .l4al .close { padding: 0; }
.l4al.fixed { position: fixed; right: 24px; top: 24px; z-index: 161; width: 430px; margin: 0; }
.l4al + .l4ca { margin-top: -14px; }
.l4al > .overlay-lime { color: var(--alert_valid); }
.l4al > .overlay-lime:before { background: var(--alert_valid); }
.l4al > .overlay-lime.inv:before { border: 1px solid var(--alert_valid); background: none; opacity: 1; }
.l4al > .overlay-rose { color: var(--alert_error); }
#root .l4al > .overlay-rose:before { background: var(--alert_error_bg); opacity: 1; }
#root .l4al > .overlay-rose.inv:before { border: 1px solid var(--alert_error); background: none; opacity: 1; }
.l4al > .overlay-secondary { color: var(--secondary_text); }
#root .l4al > .overlay-secondary:before { background: var(--secondary_bg); opacity: 1; }
.l4al > .overlay-tertiary { color: var(--tertiary_text); }
#root .l4al > .overlay-tertiary:before { background: var(--tertiary_bg); opacity: 1; }
.l4al > .overlay-quaternary { color: var(--quaternary_text); }
#root .l4al > .overlay-quaternary:before { background: var(--quaternary_bg); opacity: 1; }
.l4al > .overlay-quinary { color: var(--quinary_text); }
#root .l4al > .overlay-quinary:before { background: var(--quinary_bg); opacity: 1; }
.l4al > .overlay-content { color: var(--white); }
#root .l4al > .overlay-content:before { background: var(--primary_text); opacity: 1; }
.l4al li[class^="palette-"]:before { background: var(--primary_bg); }
.l4al + .l4tt { margin-top: -6px; }
/*.l4al:has(li.done:first-child:last-child) { display: none; }*/
/*.l4al.inline {}*/
.l4al.inline li { padding-top: calc(var(--main_fz) * 0.6428571429); padding-bottom: calc(var(--main_fz) * 0.6428571429); }
.l4al.inline li:before, .l4al.inline li:after { box-shadow: none; }
/*.l4al.inline.compact {}*/
.l4al.inline.compact li { padding-top: 4px; padding-bottom: 4px; }
/*#root .l4al.inline.compact[class*="margin-"] + * { margin-top: -10px; }*/

.l4as { position: relative; list-style: none; padding: 0; }
.l4as li { margin: 0; padding: 0 0 0 calc(var(--main_fz) * 8); border-width: 0; }
.l4as li > span:first-child { display: block; float: left; width: calc(var(--main_fz) * 7.2857142857); margin-left: calc(0px - var(--main_fz) * 8); padding-right: 10px; font-weight: var(--main_fw_strong); }
.l4as.caption { min-height: 62px; padding-left: 92px; }
.l4as.caption:before { content: "\e947"; display: block; overflow: hidden; position: absolute; left: 0; top: 0; width: 62px; height: 62px; padding: 1px 0 0; border-radius: 99px; color: var(--gray_text); font-family: i; font-weight: 400; font-size: 62px; line-height: 62px; opacity: .9; }
.l4as.caption li.img { position: absolute; left: 0; top: 0; width: 62px; padding: 0; }
.l4as.caption li.img img { display: block; border-radius: 999px; }

/*.l4ch { display: block; position: relative; z-index: 2; list-style: none; padding-left: var(--pd); padding-right: 0; --pd: calc(var(--main_fz) * 1.4285714286); }
	.l4ch li { position: relative; z-index: 2; }
		.l4ch li:before, .l4ch li.custom-icon > i:first-child, .l4ch:before, .l4ch > i:first-child {
			display: block; overflow: visible; position: absolute; left: 0; right: 0; top: 50%; bottom: auto; margin: -10px 0 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-weight: var(--main_fw); font-family: i; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal;
			left: calc(0px - var(--main_fz) * 1.4285714286); right: auto; top: 0; min-width: 10px; margin: 0; font-size: 0.7em; line-height: calc(var(--main_fz) * var(--main_lh)); text-align: center;
		}
			.l4ch > i:first-child, .l4ch:before { left: 0; }
		.l4ch li:before, .l4ch:before { content: "\e901"; color: var(--alert_valid); }
		.l4ch li.custom-icon:before, ul.l4ch:before, ol.l4ch:before , .custom-icon.l4ch:before { display: none; }
	.l4ch:not([style*="margin-bottom: 0px;"], .m0) + .l4ch { margin-top: calc(0px - var(--main_mr)); }
	.l4ch, .f8pr .l4ch { margin-bottom: var(--main_mr); }
	.l4ch.no-checks li, .l4ch li.no-checks { margin-left: calc(0px - var(--pd)); }
		l4ch.no-checks li:before, .l4ch li.no-checks:before { display: none; }
.l4ch:empty { margin: 0; padding: 0; }
	.l4ch:empty + hr { display: none; }
.l4ch.empty { margin: 0; padding: 0; }
	.l4ch.empty + hr { display: none; }

.l4ch.plus { padding-left: calc(var(--main_fz) * 2); --plus_size: calc(var(--main_fz) * 1.3571428571); }
	.l4ch.plus li:before, .l4ch.circle li:before { content: "\e948"; left: calc(0px - var(--main_fz) * 2); top: calc(var(--main_fz) * var(--main_lh) * 0.5 - var(--plus_size) * 0.5); width: var(--plus_size); height: var(--plus_size); padding-left: 0; box-shadow: var(--btn_sh_inner); border-radius: var(--plus_size); background: var(--alert_valid); color: var(--white); font-size: var(--plus_size); line-height: var(--plus_size); }
	.l4ch.plus li.overlay-content:before { content: "\e949"; background: var(--gray); }
.l4ch.circle { padding-left: calc(var(--main_fz) * 1.4285714286); }
	.l4ch.circle li:before { content: "\e953"; }*/

.l4ca { list-style: none; margin-bottom: var(--main_mr); padding: 0; border: 0 solid var(--custom_bd); border-top-width: 1px; border-bottom-width: 1px; font-size: var(--main_fz); --img_d: 22px; --img_w: 105px; --pt: 16px; --mr_i: 6px; }
.l4ca li { position: relative; z-index: 2; /*min-height: 138px;*/ padding: calc(var(--pt) + 1px) 0 .1px; border-bottom: 1px solid rgba(0,0,0,0); }
.l4ca li:before { top: 0; border: 0 solid var(--custom_bd); border-top-width: 1px; }
.l4ca li:first-child:before, .l4ca li.first-child:before { border-top-width: 0; }
/*.l4ca li:last-child:not(.toggle-l4ca), .l4ca li.last-child:not(.toggle-l4ca), .l4ca li:last-child:not(.toggle-l4ca):before, .l4ca li.last-child:not(.toggle-l4ca):before { border-bottom-width: 1px; }
			#root .l4ca li.has-l4ml .l4ml > li:last-child:before, #root .l4ca li.has-l4ml .l4ml > li:last-child:before, #root .l4ca li.has-l4ml .l4ml > li.last-child:before, #root .l4ca li.has-l4ml .l4ml > li.last-child:before { border-bottom-width: 0; }
			.l4ca:has(+.product-recommendations.hidden+form) li:last-child, l4ca:has(+form) li:last-child, .l4ca:has(+.product-recommendations.hidden+form) li:last-child:before, l4ca:has(+form) li:last-child:before { border-bottom-width: 0; }
			.l4ca li:has(+li.has-l4ca:last-child):not(.toggle-l4ca), .l4ca li:has(+li.has-l4ca:last-child):not(.toggle-l4ca):before { border-bottom-width: 1px; }*/
.l4ca ul li:before { display: none; }
.l4ca li > *, .l4ca li > footer > * { padding-left: var(--img_d); }
.l4ca li > footer > .input-amount { width: auto; }
.l4ca li > *:first-child, #root .l4ca li > footer { padding-left: 0; padding-right: 0; }
.l4cl:not(.list, .hr) li > div:last-child { flex-grow: 3; }
.l4ca + .l4ca { margin-top: calc(-1px - var(--main_mr)); }
.l4ca figure {
	display: block; overflow: hidden; width: var(--img_w); margin-bottom: var(--pt);
	align-self: flex-start;
	flex-shrink: 0;
}
.l4ca figure picture { padding-top: calc(var(--ratio) * 100%); background: var(--body_bg); }
.l4ca li.auto figure picture, .l4cl li.auto figure picture { padding-top: 0; }
.l4ca li.auto figure picture img, .l4ca li.auto figure picture svg, .l4cl li.auto figure picture img, .l4cl li.auto figure picture svg { position: relative; }
.l4ca figure picture img, .l4ca figure picture svg { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; object-fit: contain; }
#root .l4cl figure:has(picture ~ picture) picture { display: block !important; }
#root .l4cl li:not(.second-img-first) figure:has(picture ~ picture) picture ~ picture, #root .l4cl li.second-img-first figure:has(picture ~ picture) picture:first-of-type { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; opacity: 0; }
[data-whatintent="mouse"] #root .l4cl li:not(.second-img-first):hover figure:has(picture ~ picture) picture { opacity: 0; }
[data-whatintent="mouse"] #root .l4cl .second-img-first:hover picture ~ picture { opacity: 0; }
[data-whatintent="mouse"] #root .l4cl li:not(.second-img-first):hover figure:has(picture ~ picture) picture ~ picture, [data-whatintent="mouse"] #root .l4cl .second-img-first:hover figure:has(picture ~ picture) picture:first-of-type { opacity: 1; }
#root .l4cl .has-picture-picture figure picture { display: block !important; }
[data-whatintent="mouse"] #root .l4cl .has-picture-picture:not(.second-img-first):hover figure picture { opacity: 0; }
#root .l4cl .has-picture-picture:not(.second-img-first) figure picture ~ picture, #root .l4cl .has-picture-picture.second-img-first figure picture:first-of-type { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; opacity: 0; }
[data-whatintent="mouse"] #root .l4cl .has-picture-picture:not(.second-img-first):hover figure picture ~ picture, [data-whatintent="mouse"] #root .l4cl .has-picture-picture.second-img-first:hover figure picture:first-of-type { opacity: 1; }
.l4ca h1, .l4ca h2, .l4ca h3, .l4ca h4, .l4ca h5, .l4ca h6 { position: static; margin: 0 0 var(--mr_i); color: inherit; font-size: var(--main_fz); font-family: var(--main_ff); font-weight: var(--main_fw_h); line-height: var(--main_lh_l); letter-spacing: var(--main_ls); }
.l4ca h1 .small, .l4ca h2 .small, .l4ca h3 .small, .l4ca h4 .small, .l4ca h5 .small, .l4ca h6 .small { margin: 0 0 7px; color: inherit; font-size: var(--main_fz_small); font-weight: var(--main_fw); opacity: 1; }
.l4ca section { min-width: 0; margin-bottom: calc(var(--pt) - var(--mr_i)); margin-right: auto; font-size: var(--main_fz_small); /*line-height: 1.6666666667;*/ }
.l4ca .cols { position: static; }
.l4ca section ul, .l4ca section p, .l4ca.compact ul { margin-bottom: var(--mr_i); font-size: var(--main_fz_small); line-height: var(--main_lh_l); }
/*.l4ca section ul:not([class*="size-"]), .l4ca section p:not([class*="size-"]), .l4ca.compact ul:not([class*="size-"]) { font-size: calc(var(--main_fz) * 0.8571428571); }*/
.l4ca p { font-size: calc(var(--main_fz) * 0.8571428571); line-height: var(--main_lh_l); }
.l4ca p.size-12 { font-size: var(--size_12_f); }
.l4ca p:not(.s1pr), .l4ca ul { word-wrap: break-word; }
.l4ca section ul, .f8ps header ul, .l4ca footer ul, .l4ca.compact ul { overflow: hidden; list-style: none; margin-right: -18px; padding: 0; color: inherit; font-weight: var(--main_fw); }
.l4ca section ul li, .f8ps header ul li, .l4ca footer ul li, .l4ca.compact footer ul li, .l4ca.compact ul li { position: relative; z-index: 2; min-height: 0; margin-right: 18px; padding: 0; border-width: 0; font-size: 1em; }
.l4ca section ul li:not(.has-link-more), .f8ps header ul li:not(.has-link-more), .l4ca footer ul li:not(.has-link-more), .l4ca.compact footer ul li:not(.has-link-more), .l4ca.compact ul li:not(.has-link-more), .l4ca section ul li.has-link-more:before, .f8ps header ul li.has-link-more:before, .l4ca footer ul li.has-link-more:before, .l4ca.compact footer ul li.has-link-more:before, .l4ca.compact ul li.has-link-more:before { opacity: .62; }
#root .l4ca.compact .l4ml li { opacity: 1; }
.l4ca section ul li:before, .f8ps header ul li:before, .l4ca footer ul li:before, .l4ca.compact footer ul li:before, .l4ca.compact ul li:before { content: "\002F"; display: block; position: absolute; left: var(--lar1); right: var(--l1ra); top: 0; bottom: auto; width: 18px; border-width: 0; background: none; font-size: 1em; text-align: center; text-indent: 0; opacity: 1; }
#root .l4ca li a.link-more { font-weight: var(--main_fw_strong); text-decoration: none; }
#root .l4ca .r6rt { color: var(--gray_text); font-size: var(--main_fz_small); font-weight: var(--main_fw); }
.l4ca p { margin-bottom: var(--mr_i); }
.l4ca footer p { font-size: var(--main_fz); }
.l4ca footer p a { display: block; position: relative; }
.l4ca footer p:not(.link-btn) a { color: inherit; }
.l4ca footer p a i { display: block; position: relative; top: -.05em; color: inherit; font-size: 17px; opacity: .62; }
.l4ca footer p a:after { left: -15px; right: -15px; top: -15px; bottom: -15px; }
.l4ca footer p a i.icon-trash:before { content: "\e93d"; }
.l4ca footer p a i.icon-x-circle, .l4ca p a i.icon-x-circle { font-size: 22px; opacity: .34; }
.l4ca footer p a i + span { margin-left: 8px; }
.l4ca footer .link-btn { margin-bottom: 8px; }
.l4ca footer .link-btn a { display: block; }
.l4ca + h1, .l4ca + h2, .l4ca + h3, .l4ca + h4, .l4ca + h5, .l4ca + h6 { margin-top: 38px; }
#root .l4ca > li.has-l4ca { display: none; }
/*.l4ca > li:has(.l4ca) { display: none; }*/
#root .l4ca li.has-l4ml { display: block; padding-left: 0; padding-right: 0; }
.l4ca .l4ca { margin-top: calc(0px - var(--pt) * 0.5); margin-bottom: 0; border-top-width: 0; }
#root .l4ca .l4ca li { padding-top: 0; }
.l4ca:empty { display: none; }
.l4ca.compact { border-top-width: 0; --offset: 66px; --img_d: 14px; --pt: 12px; }
.l4ca.compact li { clear: both; border-width: 0; }
.l4ca.compact li.no-img { padding-left: 0; padding-right: 0; }
.l4ca.compact li.disabled .s1pr { -webkit-filter: blur(3px); filter: blur(3px); }
.l4ca.compact figure { margin-right: var(--img_d); width: calc(var(--offset) - var(--img_d)); }
.l4ca.compact figure ~ section { width: calc(100% - var(--offset) - var(--img_d)); }
.l4ca.compact footer .s1pr { text-align: var(--text_align_end); }
.l4ca.compact h1, .l4ca.compact h2, .l4ca.compact h3, .l4ca.compact h4, .l4ca.compact h5, .l4ca.compact h6 { margin-bottom: var(--mr_i); font-size: var(--main_fz); font-family: var(--main_ff); letter-spacing: var(--main_ls); }
.l4ca.compact h1:last-child, .l4ca.compact h2:last-child, .l4ca.compact h3:last-child, .l4ca.compact h4:last-child, .l4ca.compact h5:last-child, .l4ca.compact h6:last-child { margin-bottom: calc(var(--pt) * 0.5); }
.l4ca.compact section {
	width: calc(100% - var(--offset)); padding: 0; font-size: var(--main_fz);
	flex-shrink: 3;
	flex-grow: 3;
}
.l4ca.compact section .cols:last-child { margin-bottom: 2px; }
#root .l4ca.compact section .cols > div p:last-child, #root .l4ca.compact section .cols > div ~ p { margin-bottom: 0; }
.l4ca.compact .cols .s1pr { padding-left: 10px; /*line-height: var(--main_lh_h);*/ }
.l4ca.compact footer { position: relative; z-index: 9; margin-right: -14px; margin-bottom: calc(var(--pt) - var(--mr_i)); }
.l4ca.compact footer > * { margin-right: 14px; }
.l4ca.compact footer ul { display: block; overflow: hidden; margin-bottom: var(--pt); margin-right: 0; text-overflow: ellipsis; white-space: nowrap; }
.l4ca.compact footer ul li { display: inline; }
.l4ca.compact footer ul ~ p { font-size: calc(var(--main_fz) * 0.8571428571); line-height: 1.6666666667; }
.l4ca.compact li > *, .l4ca.compact li > footer > * { padding-left: 0; padding-right: 0; }
.l4ca.compact + h1, .l4ca.compact + h2, .l4ca.compact + h3, .l4ca.compact + h4, .l4ca.compact + h5, .l4ca.compact + h6 { margin-top: 10px; }
.l4ca.compact:has(+.is-sticky), .l4ca.compact:has(+.product-recommendations.hidden+.is-sticky) { border-bottom-width: 0; }
.l4ca.summary { margin-top: -15px; border-top-width: 0; }
.l4ca.summary ul li { min-height: 0; padding: 0; }
.l4ca.summary figure { float: var(--text_align_start); width: 158px; }
.l4ca.summary section { margin-bottom: 10px; }
.l4ca.summary h1, .l4ca.summary h2, .l4ca.summary h3, .l4ca.summary h4, .l4ca.summary h5, .l4ca.summary h6 { font-size: var(--main_h_small); }
#root .l4ca.summary .s1pr { position: relative; left: 0; right: 0; top: 0; margin-bottom: var(--mr_i); }

.l4cl { position: relative; z-index: 2; list-style: none; margin: var(--main_mr) 0 calc(var(--main_mr) - var(--dist_a)) calc(0px - var(--dist_a)); padding: 0; /*font-weight: var(--main_fw_strong);*/ font-style: var(--main_fs); line-height: var(--main_lh); line-height: var(--main_lh); text-transform: var(--main_tt); letter-spacing: var(--main_ls); --dist_a: 16px; --btn_dist: var(--f8pr_submit_dist); --hx_small_m: calc(0px - var(--main_fz) * var(--main_lh)); --img_small_w: 44px; }
.l4cl.processing { opacity: .25; }
.l4cl.processing:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; }
#root .l4cl { width: auto; /*3.0446px*/ }
.l4cl li { position: relative; z-index: 2; width: 20%; margin: 0 0 var(--dist_a); border: 0 solid rgba(0,0,0,0); border-left-width: var(--dist_a); }
.l4cl[style*="--dist_a: 0px"] li, .l4cl[style*="--dist_a: 1px"] li, .l4cl[style*="--dist_a: 2px"] li, .l4cl[style*="--dist_a: 3px"] li { margin-bottom: 22px; padding-left: 20px; padding-right: 20px; }
.l4cl[style*="--dist_a: 0px"] li > figure, .l4cl[style*="--dist_a: 1px"] li > figure, .l4cl[style*="--dist_a: 2px"] li > figure, .l4cl[style*="--dist_a: 3px"] li > figure { margin-left: -20px; margin-right: -20px; }
.l4cl[style*="--dist_a: 0px"] li > figure { border-radius: 0; }
.l4cl[style*="--dist_a: 0px"] li > figure picture { --b2p: 0px; }
.l4cl[style*="--dist_a: 0px"] li > figure:not(.rounded) .s1lb.align-stretch { border-radius: 0; }
.l4cl li.w8, .l4cl.w8 li, .l4ne li.w8, .l4ne.w8 li { width: 8%; max-width: none; }
.l4cl li.w10, .l4cl.w10 li, .l4ne li.w10, .l4ne.w10 li { width: 10%; max-width: none; }
.l4cl li.s11, .l4cl.s11 li, .l4ne li.s11, .l4ne.s11 li { width: 11.1111111111%; max-width: none; }
.l4cl li.w12, .l4cl.w12 li, .l4ne li.w12, .l4ne.w12 li { width: 12.5%; max-width: none; }
.l4cl li.w14, .l4cl.w14 li, .l4ne li.w14, .l4ne.w14 li { width: 14.2857142857%; max-width: none; }
.l4cl li.w16, .l4cl.w16 li, .l4ne li.w16, .l4ne.w16 li { width: 16.6666666667%; max-width: none; }
.l4cl li.w20, .l4cl.w20 li, .l4ne li.w20, .l4ne.w20 li { width: 20%; max-width: none; }
.l4cl li.w25, .l4cl.w25 li, .l4ne li.w25, .l4ne.w25 li { width: 25%; max-width: none; }
.l4cl li.w33, .l4cl.w33 li, .l4ne li.w33, .l4ne.w33 li { width: 33.33333333333%; max-width: none; }
.l4cl li.w50, .l4cl.w50 li, .l4ne li.w50, .l4ne.w50 li { width: 50%; max-width: none; }
.l4cl li.w66, .l4cl.w66 li, .l4ne li.w66, .l4ne.w66 li { width: 66.66666666666%; max-width: none; }
.l4cl li.w100, .l4cl.w100 li, .l4ne li.w100, .l4ne.w100 li { width: 100%; max-width: none; }
.l4cl .swiper-slide li { border-left-width: 0; }
.l4cl.equalize .swiper-autoheight .swiper-wrapper, .l4cl.equalize .swiper-autoheight .swiper-wrapper .swiper-slide { align-items: stretch; }
.l4cl.equalize .swiper-autoheight .swiper-wrapper .swiper-slide { display: flex; }
.l4ne.w50 figure, .l4ne .w50 figure { padding-top: 62%; }
.l4cl h1, .l4cl h2, .l4cl h3, .l4cl h4, .l4cl h5, .l4cl h6 { list-style: none; margin: 0 0 3px; padding: calc(var(--main_fz) * var(--main_lh_l) + 4px) 0 0; font-weight: var(--main_fw_h); font-family: var(--main_ff); line-height: var(--main_lh_l); letter-spacing: var(--main_ls); }
.l4cl:not(.category) h1:not([class*="size-"]), .l4cl:not(.category) h2:not([class*="size-"]), .l4cl:not(.category) h3:not([class*="size-"]), .l4cl:not(.category) h4:not([class*="size-"]), .l4cl:not(.category) h5:not([class*="size-"]), .l4cl:not(.category) h6:not([class*="size-"]) { font-size: 1em; }
.l4cl h1 .small, .l4cl h2 .small, .l4cl h3 .small, .l4cl h4 .small, .l4cl h5 .small, .l4cl h6 .small { display: block; margin: var(--hx_small_m) 0 calc(var(--main_fz) - var(--main_fz_small) + 2px); font-weight: var(--main_fw); font-size: var(--main_fz_small); }
.l4cl figure + h1, .l4cl figure + h2, .l4cl figure + h3, .l4cl figure + h4, .l4cl figure + h5, .l4cl figure + h6, .l4cl figure + div { margin-top: -7px; }
.l4cl.list figure + h1, .l4cl.list figure + h2, .l4cl.list figure + h3, .l4cl.list figure + h4, .l4cl.list figure + h5, .l4cl.list figure + h6, .l4cl.list figure + div { margin-top: 0; }
.l4cl figure[class*="margin-"] + div > h1:first-child, .l4cl figure[class*="margin-"] + div > h2:first-child, .l4cl figure[class*="margin-"] + div > h3:first-child, .l4cl figure[class*="margin-"] + div > h4:first-child, .l4cl figure[class*="margin-"] + div > h5:first-child, .l4cl figure[class*="margin-"] + div > h6:first-child { padding-top: 0; --hx_small_m: 0px; }
.l4cl figure + div.box { margin-top: -20px; }
.l4cl.category h1, .l4cl.category h2, .l4cl.category h3, .l4cl.category h4, .l4cl.category h5, .l4cl.category h6 { margin-bottom: var(--main_mr_h); padding-top: 0; font-weight: var(--main_fw_h); font-family: var(--main_ff_h); line-height: var(--main_lh_h); letter-spacing: var(--main_ls_h); }
.l4cl.category h1 .small, .l4cl.category h2 .small, .l4cl.category h3 .small, .l4cl.category h4 .small, .l4cl.category h5 .small, .l4cl.category h6 .small { margin-top: 0; }
#root .l4cl.category a span, #root .l4ft p a.strong span { z-index: 10; }
.l4cl h1.p0, .l4cl h2.p0, .l4cl h3.p0, .l4cl h4.p0, .l4cl h5.p0, .l4cl h6.p0 { padding-top: 0; }
.l4cl h1.p0 .small, .l4cl h2.p0 .small, .l4cl h3.p0 .small, .l4cl h4.p0 .small, .l4cl h5.p0 .small, .l4cl h6.p0 .small { margin-top: 4px; }
.l4cl .has-text h1, .l4cl .has-text h2, .l4cl .has-text h3, .l4cl .has-text h4, .l4cl .has-text h5, .l4cl .has-text h6, .l4cl .cols h1, .l4cl .cols h2, .l4cl .cols h3, .l4cl .cols h4, .l4cl .cols h5, .l4cl .cols h6 { margin: 0; padding: 0; font-weight: var(--main_fw_h); font-style: var(--main_fs_h); font-family: var(--main_ff_h); line-height: var(--main_lh_h); text-transform: var(--main_tt_h); letter-spacing: var(--main_ls_h); }
.l4cl p[class*="overlay"]:not(.s1pr, [class*="size-"]) { font-size: calc(var(--main_fz) * 0.8571428571); }
.l4cl .overlay-gray { color: inherit; opacity: .53; }
.l4cl .s1lb > span { opacity: 1; }
.l4cl .f8pr .submit button, .l4cl .f8pr .link-btn a { min-width: 0; }
.l4cl .r6rt { position: static; font-weight: var(--main_fw); font-size: calc(var(--main_fz) * 0.8571428571); }
.l4cl .r6rt, .accordion-a .l4cl .r6rt { margin-bottom: 1px; }
.l4cl .r6rt .rating { top: calc(0px - var(--main_fz) * 0.0714285714); }
.l4cl .r6rt a { position: relative; z-index: 10; }
.l4cl li p.stock + .price { margin-top: 3px; }
.l4cl a { display: block; color: inherit; }
.l4cl a:after { z-index: 9; }
.l4cl .small:has(a) { position: relative; z-index: 10; }
.l4cl .small.has-link { position: relative; z-index: 10; }
.l4cl .small a { display: inline; }
#root .l4cl.inline-links p:not(.link-btn) a { display: inline; color: var(--secondary_bg); font-weight: var(--main_fw); text-decoration: underline; }
.l4cl a.s1tt { display: inline-block; }
.l4cl div.box { position: relative; z-index: 2; padding: 20px min(20px, var(--rpp)); border-radius: 0 0 var(--b2r) var(--b2r); background: var(--bg); color: var(--fg); font-weight: var(--main_fw); --bg: var(--sand); --fg: var(--primary_text); }
.l4cl li:has(div.box) figure, .l4cl li:has(div.box) img { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.l4cl li.has-div-box figure, .l4cl li.has-div-box img { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.l4cl li > .cols > *:first-child { flex-shrink: 0; }
.l4cl li > .cols > *:first-child ~ * { flex-grow: 3; }
.l4cl li > .cols > *:first-child figure { width: var(--img_small_w); }
.l4cl figure { display: block; /*overflow: hidden;*/ position: relative; z-index: 2; height: auto; margin: 0 0 20px; }
#root .l4cl figure.rounded, #root .l4cl figure.rounded picture, #root .l4cl figure.rounded:before, #root .l4cl figure.rounded picture:before { border-radius: 100%; }
.l4cl figure[style*="--bw"][style*="--bd"], .l4cl li[style*="--bw"][style*="--bd"]:before { border-radius: var(--b2p); border: var(--bw) solid var(--bd); }
.l4cl.b2r figure[style*="--bw"][style*="--bd"], .l4cl.b2r li[style*="--bw"][style*="--bd"]:before { border-radius: var(--b2r); }
.l4cl .rounded figure[style*="--bw"][style*="--bd"], .l4cl li.rounded[style*="--bw"][style*="--bd"]:before, .l4cl.b2r .rounded figure[style*="--bw"][style*="--bd"], .l4cl.b2r li.rounded[style*="--bw"][style*="--bd"]:before { border-radius: 100%; }
.l4cl figure[style*="--bw"][style*="--bd"]:not([style*="--pd"]), .l4cl li[style*="--bw"][style*="--bd"]:not([style*="--pd"]):before { background: var(--bd); }
.l4cl figure[style*="--bw"][style*="--bd"][style*="--pd"], .l4cl figure[style*="--bg"][style*="--pd"], .l4cl li[style*="--bw"][style*="--bd"][style*="--pd"], .l4cl li[style*="--bg"][style*="--pd"] { padding: var(--pd); }
.l4cl figure[style*="--bg"], .l4cl figure[style*="--bg"][style*="--bw"][style*="--bd"], .l4cl li[style*="--bg"], .l4cl li[style*="--bg"][style*="--bw"][style*="--bd"] { background: var(--bg); }
#root .l4cl figure.gradient-background { border-width: 0; background: var(--theme_bg_gradient); }
#root .l4cl figure.gradient-background[style*="--bw"] { padding: var(--bw); }
#root .l4cl figure.gradient-background[style*="--pd"] { padding: var(--pd); }
#root .l4cl figure.gradient-background[style*="--bw"][style*="--pd"] { padding: calc(var(--bw) + var(--pd)); }
.l4cl figure img, .l4cl figure iframe, .l4cl figure video, .l4cl figure svg, .l4ca.compact img, .l4ca.compact svg, .l4ca.compact iframe, .l4ca.compact video { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; max-height: 100% !important; height: 100% !important; object-fit: contain; }
#root .l4cl figure img, #root .l4cl figure iframe, #root .l4cl figure video, #root .l4cl figure svg { position: absolute; left: 0; right: 0; top: 0; bottom: 0; border-radius: 0; }
.l4cl figure picture { display: block; overflow: hidden; position: relative; z-index: 2; width: 100%; height: auto; padding-top: calc(var(--ratio) * 100%); }
#root .l4cl figure img { height: 100% !important; }
.l4cl img, .l4ca img, .l4ca.compact img { display: block; max-width: 100% !important; object-fit: contain; }
#root .l4cl .filled, #root .l4ca .filled, #root .l4cl .filled *, #root .l4ca .filled *, #root .l4pr .filled, #root .l4pr .filled * { object-fit: cover; }
.l4cl figure picture, .l4cl figure a { width: 100%; height: 100%; }
.l4cl figure > span.img-overlay { left: 0; right: 0; top: 0; bottom: 0; }
.l4cl figure .text-overlay {
	display: block; position: absolute; left: 0; right: 0; top: 50%; padding: min(20px, var(--rpp)); color: var(--white); font-weight: var(--main_fw_strong); font-size: var(--size_32_f); text-align: center;
	transform: translateY(-50%);
}
.l4cl:not(.list) .has-form figure { position: relative; z-index: 99; }
.l4cl:not(.list) li:has(figure input, figure .link-btn, figure button) { position: relative; z-index: 99; }
.l4cl figure .link-btn, .l4cl figure form { position: absolute; left: var(--label_dist); right: var(--label_dist); bottom: var(--label_dist); top: auto; min-width: 0; margin-bottom: 0; padding: 0; --btn_dist: var(--f8pr_submit_dist); }
.l4cl figure form.align-stretch { left: 0; right: 0; bottom: 0; }
.l4cl figure .link-btn a, .l4cl figure button { float: none; width: auto; }
.l4cl figure .link-btn a:not(.inline, .circle) { padding-left: clamp(5px, var(--btn_ph), 20px); padding-right: clamp(5px, var(--btn_ph), 20px); }
.l4cl figure form .invalid-feedback { display: none; }
.l4cl figure button.compact { padding-left: 0; padding-right: 0; }
#root .l4cl figure .submit { min-height: var(--input_h); }
.l4cl figure .link-btn.no-border, .l4cl figure form.no-border { --label_dist: 0px; }
.l4cl:not(.list, .hr) figure:not(.overlay-static) .link-btn:not(.visible), .l4cl:not(.list, .hr) figure:not(.overlay-static) form:not(.visible) { z-index: 11; }
.l4cl:not(.list, .hr) figure.overlay-static .link-btn:not(.visible), .l4cl:not(.list, .hr) figure.overlay-static form { position: relative; left: 0; right: 0; bottom: 0; width: 100%; padding-top: var(--f8pr_submit_dist); }
.l4cl:not(.list, .hr) figure.overlay-static form:has(.link-btn.visible) { position: absolute; }
.l4cl:not(.list, .hr) figure.overlay-static .link-btn ~ .link-btn.visible, .l4cl:not(.list, .hr) figure.overlay-static form ~ .link-btn.visible { display: none; }
.l4cl:not(.list, .hr) figure.overlay-static .link-btn:not(.visible) { width: calc(100% + var(--btn_dist)); }
.mobile .l4cl:not(.list, .hr) figure .link-btn, .mobile .l4cl:not(.list, .hr) figure form { visibility: visible; opacity: 1; }
.l4cl.list figure .link-btn, .l4cl.list figure form { display: none; }
#root .l4cl figure .submit { padding: 0; }
#root .l4cl figure .submit.wide > * + * { margin-top: var(--btn_dist); }
/* taken from async-hovers.css */
.l4cl figure picture .swiper-outer, .l4cl figure picture .swiper-wrapper { position: absolute; left: 0; right: 0; top: 0; bottom: 0; height: auto !important; }
.l4cl figure picture .swiper-slide { height: 100%; }
#root .l4cl.mobile-wide figure picture .swiper-slide { border-width: 0; }
#root .l4cl figure picture .swiper-button-nav { top: 64px; bottom: 64px; width: 44px; height: auto; }
#root .l4cl figure picture .swiper-button-prev { left: var(--l0ra); right: var(--lar0); }
#root .l4cl figure picture .swiper-button-next { left: var(--lar0); right: var(--l0ra); }
#root .l4cl figure picture .swiper-button-nav:after { display: none; }
[data-whatintent="mouse"] #root .l4cl li:hover figure picture .swiper-button-nav { display: block; }
.l4cl figure.slider-ready { z-index: 20; }
.l4cl figure.slider-ready img { border-radius: 0; }
.l4cl figure > a.remove { display: block; overflow: hidden; position: absolute; right: 0; top: 0; z-index: 9; width: 44px; height: 44px; margin: -19px -19px 0 -19px; z-index: 9; color: var(--white); font-size: 22px; text-align: left; text-indent: -300em; direction: ltr; }
.l4cl figure > a.remove i { display: block; position: absolute; left: 50%; top: 50%; z-index: -1; width: 22px; height: 22px; margin: -11px 0 0 -11px; border-radius: 99px; background: var(--gray_text); line-height: 22px; }
.l4cl figure > a.remove i:before { content: "\e91f"; display: block; position: relative; z-index: 2; font-size: 8px; }
@media only screen and (min-width: 761px) {
	#root .l4cl figure picture .swiper-button-nav { display: none; }
}
/* endof: taken from async-hovers.css */
.l4cl .link-btn { position: relative; z-index: 9; margin-top: auto; margin-bottom: 0; }
.l4cl form { position: relative; z-index: 9; }
.l4cl p + form { margin-top: var(--main_mr); }
.l4cl button, .l4cl .link-btn a { width: auto; /*min-width: 0;*/ height: auto; font-style: var(--btn_fs); font-weight: var(--btn_fw); text-transform: var(--btn_tt); letter-spacing: var(--btn_ls); }
#root .l4cl button.img-only, #root .l4cl .link-btn a.img-only { min-width: 0; }
.l4cl:not(.list) form:last-child, .l4cl:not(.list) .link-btn:last-child { margin-top: auto; }
#root .l4cl form:last-child .submit:last-child, #root .l4cl figure form .submit:last-child, #root .l4cl figure .link-btn:last-child, .l4cl:not(.upsell) form:last-child .link-btn:last-child, .l4cl form:last-child .submit:last-child > *, .l4cl form:last-child .link-btn:last-child > *, .l4cl form.align-stretch .link-btn:last-child > * { margin-bottom: 0; }
.l4cl .link-btn.sticky, .l4cl form.sticky { visibility: hidden; position: absolute; left: 0; right: 0; top: 100%; z-index: 99; padding-top: var(--f8pr_submit_dist); opacity: 0; transform: translateY(-5px); }
.mobile .l4cl .link-btn.sticky, .mobile .l4cl form.sticky {
	visibility: visible; position: relative; top: 0; bottom: 0; opacity: 1;
	transform: none;
}
.l4cl [style*="--label_dist: 0"] { --f8pr_submit_dist: 0px; --btn_dist: 0px; }
.l4cl:not(.list) .link-btn:not(.sticky), .l4cl:not(.list) .submit:not(.sticky) { padding-top: 8px; }
.l4cl.list * + .link-btn, .l4cl.list * + form .submit:first-child { padding-top: 8px; }
.l4cl:not(.list) figure + .link-btn, .l4cl:not(.list) figure + form .submit, .l4cl:not(.list) figure + div > .link-btn:first-child, .l4cl:not(.list) figure + div > form:first-child .submit { padding-top: 0; }
.l4cl:not(.hr, .list) .link-btn a:last-child { margin-bottom: 0; }
.l4cl .link-btn.sticky a i/*, .l4cl .link-btn a i, .l4cl button a i*/ { font-size: var(--size_20); line-height: 1px; }
/*.l4cl .link-btn.sticky a i.icon-cart, .l4cl button a i.icon-cart { font-size: var(--size_22); line-height: 1px; }*/
.l4cl .link-btn.sticky a i.icon-cart {
	font-size: var(--size_24);
	transform: translateY(.1em);
}
.l4cl .info { display: none; position: relative; z-index: 9; margin-bottom: calc(0px - var(--main_mr_half)); font-weight: var(--main_fw); }
.l4cl .info p { display: none; margin-bottom: var(--main_mr_half); }
.l4cl .info p.link-more:not(:first-child) { margin-top: calc(0px - var(--main_mr_half)); }
.l4cl .info p + p > .link-more:first-child, .js .link-more-clicked p + p > a.link-more[data-no="1"]:first-child { display: block; margin-top: 0; /*margin-top: -18px;*/ }
p.limit, .l4cl .info p:first-child { display: -moz-box; display: -webkit-box; display: box; overflow: hidden; }
p.limit, .l4cl .info p:first-child { -webkit-line-clamp: 3; line-clamp: 3; -webkit-box-orient: vertical; box-orient: vertical; }
/*p.limit,*/ .l4cl .info:not(.long) p:first-child ~ .link-more { display: none; }
.l4cl .info p a { display: inline; color: var(--secondary_bg); }
.l4cl .check { z-index: 98; --check_color_size: 20px; --check_color_dist: 3px; --check_color_space: 6px; }
.l4cl .check.color { margin-bottom: 4px; }
.l4cl .check.color:not(:first-child) { margin-top: 2px; }
.l4cl figure .check.color { margin-top: 0; margin-bottom: 0; padding-top: calc(15px - var(--check_color_dist)); }
.l4cl figure .check.color ~ .link-btn { display: none; }
#root .l4cl .check li, #root .l4cl .check li:last-child { width: auto; min-width: 0; max-width: none; margin-top: 0; margin-bottom: 0; border-width: 0; }
.l4cl + .n6pg { margin-top: -16px; }
.l4cl li > form p { margin-bottom: var(--main_mr); }
.l4cl li > form p.link-btn, .l4cl li > form p.submit { margin-bottom: calc(var(--main_mr) - var(--btn_dist2)); }
#root .l4cl .select-wrapper a { text-decoration: none !important; }
#root .l4cl li.link, #root .l4cl.wide li.link { display: none; width: 100%; padding: 0; background: none; }
.l4cl .link-btn a:focus { position: relative; left: 0; }
/*.cols + .l4cl, .cols + .l4ft { margin-top: 12px; }*/
.l4cl .l4al { font-weight: var(--main_fw); }
#root .l4cl .l4al li { display: block; width: 100%; margin: 0 0 10px; border-width: 0; }
.l4cl p:has(a) { z-index: 9; }
/*.l4cl.category { font-weight: var(--main_fw); }
	.l4cl.category:not(.font-regular) a { font-weight: var(--main_fw_strong); }
	.l4cl.category:not(.font-regular) .link-btn a { font-weight: var(--btn_fw); }*/
.l4cl.category p + p { margin-top: var(--main_mr) /*calc(var(--main_mr) * 0.25)*/; }
.l4cl.category figure + * { margin-top: 0; }
/*#root .l4cl.category p + .link-btn { margin-top: calc(var(--main_mr) * 0.45); }*/
.l4cl.hr { padding-bottom: 1px; --img_w: 108px; --mh: calc(var(--img_w) * var(--ratio) + var(--img_t) * 2); --mih: var(--mh); --img_t: 10px; }
#root .l4cl.hr li { width: 100%; min-height: var(--mih); margin: 0; padding: 16px calc(var(--img_w) + 16px) 16px 0; }
#root .l4cl.hr li:not(:has(figure)) { min-height: 0; padding-left: 0; padding-right: 0; }
.l4cl.hr li:before { bottom: -1px; border: 0 solid var(--custom_input_bd ); border-bottom-width: 1px; }
#root .l4cl.hr figure { position: absolute; right: 0; top: var(--img_t); width: var(--img_w); height: auto; max-height: calc(100% - 10px - var(--img_t)); margin: 0; padding: 0; }
#root .l4cl.hr figure picture { height: auto !important; }
.l4cl.hr li > .link-btn:not(.sticky):last-child, .l4cl.hr li > div:last-child > .link-btn:not(.sticky):last-child { margin-bottom: calc(0px - var(--btn_dist2)); }
/*.l4cl.hr li > form:last-child, .l4cl.hr li > div:last-child > form:last-child { margin-bottom: -5px; }*/
.l4cl.hr h1, .l4cl.hr h2, .l4cl.hr h3, .l4cl.hr h4, .l4cl.hr h5, .l4cl.hr h6 { padding: 0; }
.l4cl.hr h1 .small, .l4cl.hr h2 .small, .l4cl.hr h3 .small, .l4cl.hr h4 .small, .l4cl.hr h5 .small, .l4cl.hr h6 .small { position: relative; margin-top: 0; }
.l4cl.hr .s1pr .small { display: inline; margin: 0; padding: 0; }
.l4cl.hr .link-btn.sticky {
	position: absolute; left: auto; right: calc(var(--img_w) + 16px); top: auto; bottom: 14px;
	transform: none;
}
#root .l4cl.hr .link-btn.sticky:before, #root .l4cl.hr .link-btn.sticky:after, .l4cl.hr figure .link-btn, .l4cl.hr figure form { display: none; }
.l4cl.hr .link-btn.sticky a { width: 45px; height: 45px; min-width: 0; margin-top: 0; margin-bottom: 0; padding: 0; }
.l4cl.hr .link-btn.sticky a .icon-cart {
	margin-top: -20px; line-height: 40px;
	transform: none;
}
.l4cl.hr .submit { max-width: 238px; }
#root .l4cl.hr .check li:before { display: none !important; }
.m6ac .l4cl.hr .link-btn a, .m6ac .l4cl.hr button, .l4cl.hr button.w160 { flex-grow: unset; }
.accordion-a .l4cl.hr { overflow-x: hidden; overflow-y: auto; max-height: 600px; }
@media only screen and (min-width: 761px) {
	.l4cl:not(.list, .hr) figure:not(.overlay-static) .link-btn:not(.visible), .l4cl:not(.list, .hr) figure:not(.overlay-static) form:not(.visible) { visibility: hidden; opacity: 0; }
	html:not(.mobile) .l4cl figure .check.color { visibility: hidden; position: absolute; left: 0; right: 0; bottom: 0; z-index: 9; margin: 0; padding: calc(15px - var(--check_color_dist)) calc(10px - var(--check_color_dist) - var(--check_color_space)) calc(15px - var(--check_color_dist) - var(--check_color_space) * 0.5) calc(10px - var(--check_color_dist)); background: var(--body_bg); opacity: 0; }
	html:not(.mobile) .l4cl figure.overlay-static .check.color:not(:last-child) { display: none; }
	.l4cl { scrollbar-width: thin; }
	.l4cl::-webkit-scrollbar { width: 6px; height: 6px; }
	.l4cl::-webkit-scrollbar-track { background: none; }
	.l4cl::-webkit-scrollbar-thumb { background: var(--alto); }
	.l4cl.is-scrollable { padding-right: 16px; }
}
.l4cl.hr.l4cl-banner { margin-bottom: calc(var(--main_mr) - var(--pdd)); --pd: 24px; --pdd: var(--pd); --mih: calc(var(--mh) + var(--pd) * 2); --img_t: 0px; }
#root .l4cl.hr.l4cl-banner li { margin-bottom: var(--pdd); /*padding: var(--pd);*/ padding-left: var(--pd); padding-top: 0; padding-bottom: 0; border-top: var(--pd) solid rgba(0,0,0,0); border-bottom: var(--pd) solid rgba(0,0,0,0); border-right: var(--pd) solid rgba(0,0,0,0); }
#root .l4cl.hr.l4cl-banner li:last-child { margin-bottom: 0; }
.l4cl.hr.l4cl-banner li:before { top: calc(0px - var(--pd)); bottom: calc(0px - var(--pd)); right: calc(0px - var(--pd)); box-shadow: 0 2px 4px rgba(0,0,0,.1); border-radius: var(--b2p); border-width: 0; background: var(--primary_bg); }
.m6as .l4cl.hr { --pdd: var(--d); }
.m6as > *:has(.l4cl.hr) { align-self: stretch; }
.m6as > *:has(.l4cl.hr) > * { flex-grow: 3; }
.m6as .l4cl.hr { height: 100%; }
#root .m6as .l4cl.hr figure { top: 50%; transform: translateY(-50%); }
.m6as .l4cl.hr li { flex-direction: column; justify-content: center; }
/*.l4cl.hr.inv {}*/
#root .l4cl.hr.inv li { padding-right: 0; padding-left: calc(var(--img_w) + 16px); }
#root .l4cl.hr.inv .check li { padding: 0; }
#root .l4cl.hr.inv figure { left: 0; right: auto; }
.l4cl.hr.inv .link-btn.sticky { left: calc(var(--img_w) + 16px); right: auto; }
/*.l4cl.hr.no-img {}*/
#root .l4cl.hr.no-img li, #root .l4cl.hr li.no-img, #root .l4cl.hr.no-img.inv li, #root .l4cl.hr.inv li.no-img { min-height: 0; padding-left: 0; padding-right: 0; }
#root .l4cl.hr.no-img figure, #root .l4cl.hr li.no-img figure { display: none; }
@media only screen and (min-width: 1201px) {
	.m6ac .l4cl.hr .link-btn:not(.sticky) a, .m6ac .l4cl.hr button:not(.wishlist-productcard) {
		min-width: 154px;
		flex-grow: unset;
	}
}
@media only screen and (min-width: 761px) {
	.l4cl.aside { display: block; padding-left: calc(50% + 8px); }
	.l4cl.aside > li { float: left; width: 50%; }
	.l4cl.aside > li:first-child { position: relative; left: -200%; float: right; clear: none; width: 100%; margin-right: -100%; }
	.l4cl.aside > li:nth-child(2n) { clear: left; }
	.l4cl.aside > li:first-child + li { clear: none; }
	.l4cl.aside.inv { padding-left: 0; padding-right: calc(50% + 8px); }
	.l4cl.aside.inv > li:first-child { left: 0; }
	.mobile-only:first-child + .l4cl { margin-top: 0; }
}

.l4cl.list { text-align: var(--text_align_start); --img_w: 138px; --pr_dist: 58px; --img_dist: 24px; --justify_content: flex-start; }
.l4cl.list li { width: 100%; margin: 0; padding: var(--pd) 0; text-align: var(--text_align_start); --justify_content: flex-start; --pd: 23px; }
#root .l4cl.list li { width: 100%; }
.l4cl.list > li:first-child { margin-top: calc(0px - var(--pd)); }
.l4cl.list li:before { border-bottom: 1px solid var(--custom_bd); }
.l4cl.list h1, .l4cl.list h2, .l4cl.list h3, .l4cl.list h4, .l4cl.list h5, .l4cl.list h6 { padding-top: 0; }
.l4cl.list h1 .small, .l4cl.list h2 .small, .l4cl.list h3 .small, .l4cl.list h4 .small, .l4cl.list h5 .small, .l4cl.list h6 .small { margin-top: 0; }
#root .l4cl.list figure, .l4cl.wide figure {
	width: var(--img_w); min-height: 0; margin: 0 var(--img_dist) 0 0; padding: 0 !important;
	flex-shrink: 0;
	align-self: flex-start;
}
.l4cl.list li > *:not(figure) + *:last-child {
	margin-left: auto; padding-left: var(--pr_dist);
	flex-shrink: 0;
}
.l4cl.list li > *:not(:last-child) { min-width: 0; }
.l4cl.list li > div:not(:last-child) {
	flex-basis: 0;
	flex-grow: 3;
}
.l4cl.list .link-btn { visibility: visible; position: relative; left: 0; bottom: 0; margin-top: 0; opacity: 1; }

@media only screen and (min-width: 1001px) {
	.l4cl.list .link-btn a, .l4cl.list form button { white-space: nowrap; }
}

.l4cl.wide { display: block; margin-left: 0; --img_w: 60px; --img_dist: min(var(--rpp), 20px); }
#root .l4cl.wide li { width: 100%; border-left-width: 0; }
.l4cl.wide h1, .l4cl.wide h2, .l4cl.wide h3, .l4cl.wide h4, .l4cl.wide h5, .l4cl.wide h6 { margin-bottom: 0; padding-top: 0; line-height: 1.6153846154; }
.l4cl.wide li > div { max-width: 195px; }
.l4cl.wide .link-btn { visibility: visible; position: relative; left: 0; right: 0; top: 0; bottom: 0; margin-top: 0; margin-left: auto; margin-top: 0; padding-left: 11px; padding-top: 0; opacity: 1; }
#root .l4cl.wide .link-btn { margin-top: 0; }
.l4cl.wide .link-btn a, .popup-a .l4cl.in-popup-cart .link-btn a { overflow: hidden; position: relative; left: 0; width: 44px; min-width: 0; height: 44px; margin-top: 0; margin-bottom: 4px; padding: 0; border-radius: 99px; text-indent: -3000em; text-align: left; direction: ltr; }
.l4cl.wide .link-btn a i, .popup-a .l4cl.in-popup-cart .link-btn a i {
	margin-top: -11px; font-size: var(--size_20_f); line-height: 22px;
	transform: none;
}
.l4cl.wide .link-btn a:before, .popup-a .l4cl.in-popup-cart .link-btn a:before { border-radius: 99px; }
.l4cl.wide .s1pr { margin-bottom: 4px; margin-left: auto; padding-left: 11px; font-size: var(--main_fz); white-space: nowrap; }
.l4cl.wide .s1pr ~ .link-btn { margin-left: 0; }
.l4cl.s4wi, .l4ft.s4wi { display: block; margin-left: 0; }
.l4ft.s4wi .swiper-outer { overflow: hidden; }
.l4ft.s4wi .swiper-pagination-bullets { display: none; }
#root .l4cl.s4wi li, #root .l4ft.s4wi li { float: none; /*width: 100%;*/ border-left-width: 0; }
#root .l4cl.s4wi.text-justify li, .l4cl.s4wi.text-justify .swiper-slide { width: auto; }
.l4cl .swiper-button-nav, .l4ft .swiper-button-nav { /*overflow: hidden;*/ width: var(--rpp); height: 100%; color: var(--primary_text); }
.l4cl:not(.no-img) .swiper-button-nav, .l4ft:not(.no-img) .swiper-button-nav { height: 0px; }
.l4cl[style*="--fih"]:not([style*="--fih:0"], [style*="--fih: 0"]) .swiper-button-nav, .l4ft[style*="--fih"]:not([style*="--fih:0"], [style*="--fih: 0"]) .swiper-button-nav { height: var(--fih); }
.l4cl.no-img .swiper-button-nav, .l4ft.no-img .swiper-button-nav { top: 0; bottom: var(--dist_a); height: auto; }
.l4cl .swiper-button-next, .l4ft .swiper-button-next { right: var(--rpn); }
.l4cl .swiper-button-prev, .l4ft .swiper-button-prev { left: var(--rpn); }
@media only screen and (min-width: 761px) { /* 760+ */
	#root .l4cl.s4wi li, #root .l4cl.s4wi .check li.wide, #root .l4ft.s4wi li, #root .l4ft.s4wi .check li.wide { width: 100%; }
	#root .l4cl.s4wi .check li { width: auto; }
	/*html:not(.mobile)*/ #root .l4cl:not(.list, .hr) .static .link-btn, #root .l4cl:not(.list, .hr) .static form { display: none; }
}
.l4cl.s4wi.auto-width .swiper-slide { width: auto !important; }
.l4cl.small { --dist_a: 10px; }
.l4cl.small figure { overflow: hidden; border-radius: var(--b2r); border: 1px solid var(--custom_bd); --b2p: 0px; }
.l4cl.small .active figure, .l4cl.small li > input:checked ~ label figure { border-color: var(--secondary_bg); }
.l4cl.small li > input { visibility: hidden; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; z-index: -1; opacity: 0; }
#root .l4cl.small .swiper-button-disabled { display: block; color: inherit; opacity: .1; }

.l4hs { display: none; margin: 0; }
.l4hs, .l4hs-l { list-style: none; padding: 0; counter-reset: counter; --w: 24px; --fg: var(--primary_text); --bg: var(--white); --fg_active: var(--white); --bg_active: var(--primary_text); --hs_size: 32px; --justify_content: text-start; }
.l4hs, .m6fr article > .l4hs { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 99; margin: 0; }
.l4hs > li { position: absolute; width: 44px; height: 44px; margin: -22px 0 0 -22px; border-left-width: 0; }
#root .l4hs > li > a { display: block; position: relative; z-index: 99; width: 44px; height: 44px; color: var(--fg); font-size: calc(var(--hs_size) * 0.375); text-indent: -3000em; text-align: left; direction: ltr; --secondary_bg_btn: var(--bg); }
.l4hs > li > a:before { content: "\e910"; left: 50%; top: 50%; right: auto; bottom: auto; z-index: 9; width: var(--hs_size); height: var(--hs_size); margin: calc(0px - var(--hs_size) * 0.5) 0 0 calc(0px - var(--hs_size) * 0.5); box-shadow: 0 2px 4px rgba(0,0,0,.1); border-radius: 99px; background: var(--bg); line-height: var(--hs_size); }
.l4hs > li:not([style*="horizontal"][style*="vertical"]), .l4hs > li > div { display: none; }
#root .l4hs > li[style*="horizontal"] { left: var(--horizontal); }
#root .l4hs > li[style*="vertical"] { top: var(--vertical); }
.l4hs.dots { --hs_size: 24px; }
.l4hs.dots > li:not(.toggle) > a:before, .l4hs.dots.tabbed > li.toggle > a:before {
	content: "";
	transform: scale(.48);
}
.l4hs.dots > li > a:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; right: auto; bottom: auto; z-index: 1; width: var(--hs_size); height: var(--hs_size); margin: calc(0px - var(--hs_size) * 0.5) 0 0 calc(0px - var(--hs_size) * 0.5); border-radius: 99px; background: var(--bg); opacity: .44; }
/*.l4hs.ol {}*/
.l4hs.ol > li > a:before { counter-increment: counter; content: counters(counter, '.') ' '; box-shadow: none; border: 1px solid var(--bg); background: none; color: var(--bg); font-family: var(--main_ff); font-size: 1em; line-height: 1; }
/*.l4hs-l {}*/
.l4hs-l li { position: relative; z-index: 2; margin: 0 0 6px; padding: 0 0 0 calc(var(--w) + 14px); }
.l4hs-l li:before { counter-increment: counter; content: counters(counter, '.') ' '; display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: calc(var(--main_fz) * var(--main_lh) * 0.5 - var(--w) * 0.5); width: var(--w); height: var(--w); border-radius: var(--w); border: 1px solid var(--fg); color: var(--fg); font-size: 1em/*calc(var(--w) * 0.375em)*/; line-height: 1; }

/* .l4cm */

.l4cn { list-style: none; padding: 0; }
.l4cn li, .l4ad .l4cn li { position: relative; z-index: 2; margin-bottom: 2px; padding: 0 0 0 calc(var(--main_fz) * 2); }
.l4cn i { display: block; position: absolute; left: 0; top: 0; min-width: 21px; font-size: 1.2857142857em; line-height: calc(var(--main_fz) * var(--main_lh)); text-align: center; }
.l4cn .icon-envelope { font-size: 0.7142857143em; }
.l4cn [class*="whatsapp"] { font-size: 1.175em; }
.l4cn .icon-phone { font-size: 1em; }
.l4cn.box { margin-right: -18px; margin-bottom: 13px; text-align: center; }
.l4cn.box li { margin: 0 18px 18px 0; padding: 10px 16px; }
.l4cn.box li:before { border-radius: var(--b2r); border: 1px solid var(--custom_input_bd); background: var(--white); }
.l4cn.box i { display: inline-block; position: relative; top: .1em; margin-right: 2px; line-height: 1px; }
p + .l4cn.box { margin-top: -4px; }
/*.l4cn.plain {}*/
#root .l4cn.plain li { padding: 0; }

.l4cu { list-style: none; margin: 40px 0 0 -80px; padding: 0; color: inherit; text-align: center; }
.l4cu li { position: relative; z-index: 2; margin: 0 0 var(--main_mr); border-left: 80px solid rgba(0,0,0,0); }
.l4cu li > span { display: block; position: relative; z-index: 2; margin: 0 0 15px; font-weight: var(--main_fw_strong); font-size: var(--main_h1); line-height: var(--main_lh_h); }
.l4cu li > span span { margin: 0; }
.l4cu li > span span.main { visibility: hidden; opacity: 0; }
.l4cu li > span span.clone { display: block; position: absolute; left: 0; right: 0; top: 0; z-index: 9; }
.l4cu span.has-plus:after { content: "\002B"; }
.l4cu span.has-exc:after { content: "\0021"; }
.l4cu span.has-usd:not(.after):before, .l4cu span.has-usd.after:after { content: "\0024"; }
.l4cu span.has-eur:not(.after):before, .l4cu span.has-eur.after:after { content: "\20AC"; }
.l4cu span.has-gbp:before { content: "\00A3"; }
.l4cu span.has-jpy:before { content: "\00A5"; }
.l4cu span.has-inr:before { content: "\20B9"; }
.l4cu span.has-per:after { content: "\0025"; }
.l4cu span.has-str:after { content: "\0020\2605"; }
.l4cu.box { margin-left: -16px; }
.l4cu.box li { border-left-width: 16px; }
.l4cu.box li > span { margin-bottom: 8px; padding: 0 24px; border: 0 solid rgba(0,0,0,0); border-top-width: 35px; border-bottom-width: 35px; }
.l4cu.box li > span:before { top: -35px; bottom: -35px; border-radius: 6px; background: var(--coal); opacity: .1; }
.m6wd.overlay-content .l4cu.box li > span:before, .m6wd.overlay-theme .l4cu.box li > span:before { background: var(--white); }

.l4dr { position: relative; z-index: 4; list-style: none; margin-right: calc(0px - var(--dist)); padding: 0; text-align: left; --dist: 30px; }
.l4dr li { position: relative; z-index: 2; min-height: calc(var(--main_lh) * var(--main_fz)); margin-right: var(--dist); }
.l4dr a:not(.inline) { display: block; position: relative; z-index: 2; }
#root .l4dr a:not(.inline):after {
	content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 100%; min-width: 44px; height: 44px; margin: -22px 0 0;
	transform: translateX(-50%);
}
#root .l4dr .l4sc.box a:after {
	left: 0; right: 0; top: 0; bottom: 0; min-width: 0; height: auto; margin: 0;
	transform: none;
}
.l4dr i { display: inline-block; position: relative; margin-right: calc(var(--main_fz) * 0.5714285714); line-height: 1px; }
.l4dr i.icon-share { font-size: 1.1428571429em; }
.l4dr li > span:first-child { margin-right: 8px; }
#root .l4dr li > span.r6rt { margin: 0; }
.l4dr img { border-radius: 0; }
#root .l4dr li > form { margin-bottom: 0; }
#root .l4dr li:not(.toggle) > form { display: none !important; }
.l4dr ul, .l4dr .l4sc.box { display: none; }
.l4dr:empty { display: none; }
.l4dr.col { display: block; }
.l4dr.col li + li { margin-top: calc(var(--main_mr) * 0.45); }

.l4ft { list-style: none; padding: 0; margin: var(--main_mr) 0 35px calc(0px - var(--dist_a)); color: var(--fg); font-weight: var(--main_fw); font-style: var(--main_fs); line-height: var(--main_lh); text-transform: var(--main_tt); letter-spacing: var(--main_ls); --dist_a: 16px; --pt: 34px; --pd: calc(var(--pt) - var(--main_mr_h)); --pr: var(--ps); --pl: var(--ps); --bg: var(--primary_text); --mih: 300px; --fg: var(--white); --ps: 42px; --w: 50%; }
.l4ft li { position: relative; z-index: 3; width: var(--w); margin: 0 0 var(--dist_a); border-left: var(--dist_a) solid rgba(0,0,0,0); --main_mr: var(--main_mr_h); }
.l4ft li.w9, .l4ft.w9 li { --w: 11.1111111111%; }
.l4ft li.w12, .l4ft.w12 li { --w: 12.5%; }
.l4ft li.w14, .l4ft.w14 li { --w: 14.2857142857%; }
.l4ft li.w16, .l4ft.w16 li { --w: 16.6666666666%; }
.l4ft li.w20, .l4ft.w20 li { --w: 20%; }
.l4ft li.w25, .l4ft.w25 li { --w: 25%; }
.l4ft li.w33, .l4ft.w33 li { --w: 33.3333333333%; }
.l4ft li.w37, .l4ft.w37 li { --w: 37.5%; }
.l4ft li.w50, .l4ft.w50 li { --w: 50%; }
.l4ft li.w66, .l4ft.w66 li { --w: 66.6666666666%; }
#root .l4ft li.size-m { --mih: 375px; }
#root .l4ft li.size-l { --mih: 520px; }
.l4ft li.w100, .l4ft li.wide, .l4ft.w100 li, .l4ft.wide li { width: 100%; }
.l4ft li > .main:before, .l4ft .background { z-index: -3; }
.l4ft li > .main:not(.module-color-palette):before, .l4ft .background { background: var(--bg); }
.l4ft li.overlay-content > .main:before { background: var(--sand); }
.l4ft li.bg-dark { color: var(--white); }
.l4ft li.bg-light, .l4ft li.overlay-content { color: var(--primary_text); }
.l4ft .main a:after, .l4ft .main .link-overlay { left: -3000em; right: -3000em; top: -3000em; bottom: -3000em; z-index: 8; }
.l4ft a:after { display: none; }
.l4ft .main .link-btn .link-overlay { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 1; }
.l4ft .main .link-btn .link-overlay:before { display: none; }
.l4ft .link-btn, .l4ft .link { position: relative; z-index: 9; }
.l4ft .link-btn a:after, .l4ft .link a:after { display: none; }
/*.l4ft li > div,*/ .l4ft .main > div { position: relative; z-index: 9; padding: var(--pt) var(--pr) var(--pd) var(--pl); }
.l4ft li.align-top:has(figure .s1lb) .main > div { padding-top: calc(var(--pt) + 8px + var(--sale_label_fz) * var(--main_lh_h) + var(--label_dist)); }
.l4ft li.align-bottom .main > div:has(+.link-btn.text-end) { padding-bottom: var(--btn_circle_size); }
/*[dir="ltr"] .l4ft li.align-bottom .main > div:has(+.link-btn.text-end) { --pr: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh) + var(--label_dist) * 2); }
	[dir="ltr"] .l4ft li.align-bottom .main > div:has(+.link-btn:not(.text-end)) { --pl: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh) + var(--label_dist) * 2); }*/
.l4ft.w16, .l4ft .w16, .l4ft.w14, .l4ft .w14, .l4ft.w12, .l4ft .w12 { --ps: 20px; }
.l4ft.w14, .l4ft .w14, .l4ft.w12, .l4ft .w12 { --pt: 30px; --pb: 18px; }
.l4ft li.align-bottom { --pb: 30px; }
.l4ft li.overlay-content .main > div:first-child { mix-blend-mode: multiply; }
.l4ft li > .main { overflow: hidden; position: relative; z-index: 2; width: 100%; /*min-height: min(100%, var(--mih));*/ min-height: var(--mih); padding: 0; /*--main_mr: var(--main_mr_h);*/ }
.l4ft li > .main:first-child:last-child { height: 100%; flex-grow: 3; }
.l4ft li.inline > .main > div { padding-top: 0; padding-bottom: 0; }
.l4ft li:not(.inline) > .main > figure { position: absolute; z-index: -2; }
.l4ft li > .main > figure img, .l4ft li > .main > figure picture, .l4ft li > .main > figure video, .l4ft li > .main > figure svg { height: 100% !important; }
.l4ft li > .main picture ~ picture, .l4ft li > .main picture ~ video, .l4ft li > .main video ~ picture, .l4ft li > .main video ~ video { display: none; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 2; }
#root .l4ft li.empty { min-height: var(--mih); }
#root .l4ft li.empty > * { display: none; }
.l4ft li > .content { position: relative; z-index: 9999; padding: 20px 0 0; color: var(--fg); --bg: none; --fg: var(--primary_text); }
.l4ft li > .content:before { border-radius: 0 0 var(--b2p) var(--b2p); }
.l4ft li > .content.no-img:before { border-radius: var(--b2p); }
.l4ft li > .content:not(.module-color-palette):before { background: var(--bg); }
/*.l4ft li > .content h1, .l4ft li > .content h2, .l4ft li > .content h3, .l4ft li > .content h4, .l4ft li > .content h5, .l4ft li > .content h6 { font-weight: var(--main_fw_h); line-height: var(--main_lh_h); font-family: var(--main_ff_h); font-style: var(--main_fs_h); letter-spacing: var(--main_ls_h); }*/
/*.l4ft li > .content p, .l4ft li > .content ul, .l4ft li > .content ol { margin-bottom: min(12px, calc(var(--main_mr) * 0.25)); }*/
.l4ft li > .content.box { padding: var(--pt) var(--pr) calc(var(--pt) - var(--main_mr) * 0.25) var(--pl); --fg: var(--primary_text); --bg: var(--sand); }
/*.l4ft li > .content:not(.box) > .link-btn { margin-top: 8px; }*/
.l4ft li > .content > .link-btn:last-child { margin-bottom: /*-8px*/ 0; }
.l4ft li:has(.content.box) > .main > figure, .l4ft li:has(.content.box) > .main:before, .l4ft li:has(.content.box) > .main > figure * { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.l4ft li.has-content-box > .main > figure, .l4ft li.has-content-box > .main:before, .l4ft li.has-content-box > .main > figure * { border-bottom-left-radius: 0; border-bottom-right-radius: 0; }
.l4ft figure { overflow: hidden; z-index: -2; }
.l4ft .img-overlay, figure .img-overlay { z-index: 3; border-radius: 0; background: var(--primary_text); opacity: .3; }
.l4ft figure .background { opacity: .7; }
.l4ft figure:first-child:last-child:before { display: none; }
.l4ft figure a, .l4ft figure img, .l4ft figure picture, .l4ft figure picture img, .l4ft figure video { display: block; z-index: 1; }
.l4ft .s1lb a { display: inline; }
/*.l4ft li.inline {}*/
.l4ft li.inline figure { position: relative; margin-bottom: 10px; padding: 0 16px; }
.l4ft li.inline figure:before, .l4ft li.has-html-background figure:before, .l4ft figure.has-html-background:before, .l4ft li.inline .img-overlay, .l4ft div figure:before, .l4ft div .cols .img-overlay { display: none; }
#root .l4ft li.inline figure img, #root .l4ft li.inline figure picture, .l4ft div figure img, .l4ft div figure picture { display: block; position: relative; margin: 0 auto; object-fit: contain; }
#root .l4ft li.inline > div { padding-top: 0; padding-bottom: 0; }
#root .l4ft.align-top li.inline > div, #root .l4ft li.inline.align-top > div { margin-top: auto; }
.l4ft div figure { position: relative; z-index: 2; }
/*.l4ft .cols {}*/
.l4ft .cols > figure { max-width: 50%; }
.l4ft .cols {
	align-items: center;
	flex-wrap: nowrap;
}
.l4ft h1, .l4ft h2, .l4ft h3, .l4ft h4, .l4ft h5, .l4ft h6 { /*max-width: 380px;*/ margin-top: 0; /*margin: 0 0 calc(var(--main_mr) * 0.4615384615);*/ color: inherit; /*font-size: var(--size_32);*/ }
.l4ft h1 .small, .l4ft h2 .small, .l4ft h3 .small, .l4ft h4 .small, .l4ft h5 .small, .l4ft h6 .small { margin: calc(var(--main_mr) * 0.4615384615) 0 0; opacity: 1; }
/*.l4ft p, .shopify-section-header .l4ft p, #header .l4ft p, .l4cl.category p, .l4st p { margin-bottom: calc(var(--main_mr) * 0.4615384615); }
		.l4ft .link-btn, .shopify-section-header .l4ft .link-btn { margin-top: calc(var(--main_mr) * 0.5384615385); margin-bottom: 4px; }*/
.l4ft .main > .link-btn { position: absolute; left: 0; right: 0; top: auto; z-index: 10; bottom: calc(0px - var(--btn_dist2)); margin-top: 0; margin-bottom: 0; padding: var(--label_dist); pointer-events: none; }
.l4ft p a:hover { color: inherit; }
.l4ft li.inline > *:last-child > p:last-child { margin-bottom: 16px; }
.l4ft + .l4ft { margin-top: -19px; }
.l4ft li { -webkit-backface-visibility: hidden; -webkit-perspective: 1000; transform: translate3d(0,0,0); }
/*.l4ft.cols.w50 {}*/
#root .l4ft.cols li { display: block; --mih: 0px; }
.l4ft.cols .main > div, .l4ft li.overlay .main > div { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; }
#root .l4ft.cols figure, .l4ft.cols figure a, .l4ft.cols img, .l4ft.cols picture, .l4ft li.overlay figure, .l4ft.cols picture img, .l4ft li.overlay, .l4ft li.overlay figure img, .l4ft li.overlay picture, .l4ft li.overlay picture img { display: block; position: relative; left: 0; right: 0; top: 0; bottom: 0; width: calc(100% + 1px) !important; height: auto !important; }
html:not(.mobile) .l4ft.hover-out > li:after { content: ""; display: block; overflow: hidden; visibility: visible; position: absolute; left: 0; top: 0; right: 0; bottom: 0; z-index: 9; margin: 0; border-radius: var(--b2p); background: var(--dark); text-align: left; text-indent: -3000em; direction: ltr; opacity: 0; }
html:not(.mobile) .l4ft.hover-out > li:not(.inline) figure ~ div { opacity: 0; }
[dir="ltr"] .l4ft[style*="--dist_a: 0px"] .content:not(.box) { padding-right: max(20px, var(--dist_a)); }
.l4ft[style*="--dist_a: 0px"] .content:not(.box), .l4ft[style*="--dist_a: 0px"] li .content:not(.box), .l4ft[style*="--dist_a: 0px"] .content:not(.box) { padding-right: max(20px, var(--dist_a)); padding-left: max(20px, var(--dist_a)); }

.l4ft.grid { display: grid; grid: auto-flow dense var(--gih) / repeat(var(--wd), 1fr); /*grid-template-columns: repeat(var(--wd), 1fr); grid-auto-flow: dense;*/ --wd: 2; --gih: calc(var(--mih) + var(--dist_a)); --grid_col: 1; --grid_row: 1; }
.l4ft.grid li { width: 100%; min-width: 0; min-height: 0; grid-row-start: span min(2, var(--grid_row)); grid-column-start: span min(2, var(--grid_col)); }
.l4ft.grid li[style*="--grid_col"] { grid-column: span var(--grid_col); }
.l4ft.grid li[style*="--grid_row"] { grid-row: span var(--grid_row); }
.l4ft.grid li { grid-area: span var(--grid_row) / span var(--grid_col); }

@media only screen and (min-width: 761px) {
	.l4ft.grid:not(.mobile-compact) { display: grid; }
	.l4ft.grid:not(.mobile-compact) li { width: 100%; }
	.l4ft.grid li > .main { min-height: 0; }
	.l4ft.w9, .l4ft.grid-9 { --wd: 9; }
	.l4ft.w12, .l4ft.grid-8 { --wd: 8; }
	.l4ft.w14, .l4ft.grid-7 { --wd: 7; }
	.l4ft.w16, .l4ft.grid-6 { --wd: 6; }
	.l4ft.w20, .l4ft.grid-5 { --wd: 5; }
	.l4ft.w25, .l4ft.grid-4 { --wd: 4; }
	.l4ft.w33, .l4ft.grid-3 { --wd: 3; }
}
@media only screen and (max-width: 1000px) {
	.l4ft.grid.grid-desktop { display: block; }
	.l4ft.grid.grid-desktop.mobile-compact { display: flex; }
}
@media only screen and (max-width: 760px) {
	.l4ft.grid { display: block; }
	.l4ft.grid.mobile-compact { display: flex; }
}

.l4in { list-style: none; margin-right: calc(0px - var(--dist_in)); padding: 0; --dist_in: calc(var(--main_fz) * 1.7142857143); }
.l4in li { position: relative; z-index: 2; margin: 0 var(--dist_in) 0 0; }
.l4in li.title:not(:last-child) { margin-right: calc(var(--dist_in) * 0.5); }
#root .l4in input { display: block; visibility: hidden; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; opacity: 0; }
#root .l4in input + *, .l4in label span + span { margin: 0; }
.l4in input ~ label, .l4in input ~ label * { font-weight: inherit; }
.l4in input:checked ~ label, .l4in input:checked ~ label * { color: var(--secondary_bg); font-weight: var(--main_fw_strong); /*text-decoration: underline;*/ }
.l4in.a { overflow: hidden; --dist_in: calc(var(--main_fz) * 1.2857142857); }
.l4in.a li:before { content: "\e94b"; left: auto; right: 100%; width: var(--dist_in); font-size: 1em; }

.l4ne { list-style: none; margin: min(12px, calc(var(--main_mr) * 0.4615384615)) 0 21px calc(0px - var(--dist)); padding: 0; font-size: var(--main_fz); --ratio: 0.66; --label_dist: 10px; --dist: 16px; }
.l4ne li { position: relative; width: 33.333333333333%; margin: 0 0 24px; border: 0 solid rgba(0,0,0,0); border-left-width: var(--dist); }
.l4ne figure { display: block; overflow: hidden; height: auto; margin: 0 0 16px; padding-top: calc(var(--ratio) * 100%); }
.l4ne figure svg, #root .l4ne figure img, .l4ne figure picture, .l4ne figure a { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; object-fit: cover; }
.l4ne figure svg { width: auto !important; max-width: none !important; }
.l4ne h1, .l4ne h2, .l4ne h3, .l4ne h4, .l4ne h5, .l4ne h6 { margin: 0 0 3px; /*font-size: var(--main_h_small);*/ line-height: var(--main_lh_l); }
.l4ne h1 .small, .l4ne h2 .small, .l4ne h3 .small, .l4ne h4 .small, .l4ne h5 .small, .l4ne h6 .small { margin-bottom: 6px; font-size: var(--main_fz_small); text-transform: var(--main_tt); }
.l4ne h1 a, .l4ne h2 a, .l4ne h3 a, .l4ne h4 a, .l4ne h5 a, .l4ne h6 a { position: relative; z-index: 9; }
.l4ne p { margin-bottom: 2px; }
.l4ne p + * { margin-top: 16px; }
.l4ne .label { position: absolute; left: 10px; top: 10px; right: 10px; z-index: 9; margin-right: -7px; }
.l4ne .label span { margin-right: 7px; }
#root .l4ne p a span { position: relative; z-index: 9; }
.l4ne.featured { display: block; padding-left: 638px; }
.l4ne.featured li { float: left; width: 50%; margin-bottom: 12px; }
.l4ne.featured li:nth-child(2n+4) { clear: left; }
.l4ne.featured li:first-child { left: -100%; float: right; clear: none; width: 638px; margin-left: -638px; }
#root .l4ne.featured li:first-child figure { padding-top: 0; }
.l4ne.featured li:first-child figure svg, #root .l4ne.featured li:first-child figure img, .l4ne.featured li:first-child figure picture, #root .l4ne-figure-before.l4ne figure svg, #root .l4ne-figure-before.l4ne figure img, #root .l4ne-figure-before.l4ne figure picture { position: relative; height: auto !important; min-height: 0 !important; }
/*.l4ne.featured h1, .l4ne.featured h2, .l4ne.featured h3, .l4ne.featured h4, .l4ne.featured h5, .l4ne.featured h6 { font-size: 1em; }*/
.l4ne.featured h1 .small, .l4ne.featured h2 .small, .l4ne.featured h3 .small, .l4ne.featured h4 .small, .l4ne.featured h5 .small, .l4ne.featured h6 .small { margin-bottom: 6px; font-size: var(--main_fz_small); }
/*.l4ne.featured li:first-child h1, .l4ne.featured li:first-child h2, .l4ne.featured li:first-child h3, .l4ne.featured li:first-child h4, .l4ne.featured li:first-child h5, .l4ne.featured li:first-child h6 { margin-bottom: 8px; font-size: 1.7142857143em; }*/
.m6wd > .l4ne.featured:last-child { margin-bottom: 16px; }
.l4ne-figure-before, #root .l4ne-figure-before.l4ne { display: block; list-style: none; margin: 12px 0 2px; padding: 0; }
#root .l4ne-figure-before.l4ne li { left: 0; right: 0; float: none; width: 100%; min-width: 0; max-width: none; margin: 0 0 23px; padding: 0; border-width: 0; }
#root .l4ne-figure-before.l4ne figure { margin-bottom: 16px; padding-top: 0; }
#root .l4ne-figure-before.l4ne h1, #root .l4ne-figure-before.l4ne h2, #root .l4ne-figure-before.l4ne h3, #root .l4ne-figure-before.l4ne h4, #root .l4ne-figure-before.l4ne h5, #root .l4ne-figure-before.l4ne h6 { margin-bottom: 6px; font-size: var(--size_18_f); }
.l4ne-figure-before + .l4ne { margin-top: 12px; }

.l4pm { list-style: none; margin-top: 26px; margin-right: calc(0px - var(--dist)); margin-bottom: 16px; padding: 0; line-height: 1; --dist: 22px; }
.l4pm li { margin: 0 var(--dist) 10px 0; }
.l4pm:first-child { margin-top: 0; }
.l4pm.box, .shopify-section-footer > div .l4pm.box { --dist: 10px; }

.l4pl { list-style: none; padding: 0; }

.l4pr { position: relative; z-index: 2; list-style: none; margin: 0 38px 38px; padding: 0; border: 0 solid rgba(0,0,0,0); text-align: center; --dist: 0px; --dist_li: 8px; --pic_pd: 0; /*--ratio: 0.85;*/ --bx_sh: inset 2px -2px 0 var(--btn_sh_inner_c); }
.l4pr li { position: relative; z-index: 2; margin-bottom: 0; padding-top: calc(var(--ratio) * 100%); --pos: absolute; }
.l4pr li + li { margin-top: var(--main_mr); }
#root .l4pr li.auto, #root .l4pr.auto li { padding-top: 0; }
.l4pr li a { display: block; position: var(--pos); left: 0; right: 0; top: 0; bottom: 0; z-index: 9; width: 100%; height: 100%; }
.l4pr picture { display: block; width: auto !important; object-fit: contain; }
#root .l4pr picture { padding: var(--pic_pd); }
.l4pr li picture, .l4pr li video, .l4pr li iframe { display: block; position: var(--pos); left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; object-fit: contain; }
#root .l4pr .auto a, #root .l4pr.auto a, #root .l4pr .auto picture, #root .l4pr.auto picture, #root .l4pr .auto img, #root .l4pr.auto img, #root .l4pr .auto video, #root .l4pr.auto video, #root .l4pr .auto iframe, #root .l4pr.auto iframe { position: relative; width: 100% !important; height: auto !important; }
.l4pr img { display: block; width: auto !important; max-width: 100% !important; height: auto !important; max-height: 100% !important; border-radius: var(--b2p); object-fit: contain; }
#root #content .l4pr img { position: relative; width: auto !important; }
.l4pr .li a[data-type="video"], .l4pr .li a[data-type="html5video"], .l4pr .swiper-outer { border-radius: var(--b2p); }
#root .l4pr.s4wi .li a[data-type="video"], #root .l4pr.s4wi .li a[data-type="html5video"], #root .l4pr.s4wi .li.cover img, .l4pr .li a[data-type="video"] video, .l4pr .li a[data-type="html5video"] video, .l4pr .li a[data-type="video"] img, .l4pr .li a[data-type="html5video"] img { border-radius: 0; }
.l4pr li .just-poster, .l4pr .li .just-poster { visibility: hidden; opacity: 0; }
.l4pr li.static, .l4pr li.static a { padding-top: 0; --pos: relative; }
#root .l4pr picture { height: 100% !important; }
.l4pr .model-3d { display: block !important; }
.l4pr.th-no-bd-radius .swiper-pagination-bullet * { --b2r: 0px; --b2p: 0px; }
.l4pr.s4wi { margin-bottom: var(--main_mr); }
.l4pr .swiper-outer { display: block; overflow: hidden; position: relative; }
.l4pr .swiper-pagination-bullets { position: relative; left: 0; right: 0; top: 0; bottom: 0; list-style: none; margin: 30px calc(0px - var(--dist_li)) 0 0; padding: 0; --top: 50%; --height: auto; --ob_fit: contain; --pic_pd: calc(var(--ratio) * 100%) 0 0; --img_pos: absolute; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet { display: block; position: relative; z-index: 2; width: var(--pager_w); height: auto; min-height: 0; margin: 0 var(--dist_li) var(--dist_li) 0; padding: 10px; cursor: pointer; }
.l4pr .swiper-pagination-bullets picture { position: relative; z-index: 2; width: 100% !important; }
.l4pr .swiper-pagination-bullets .auto { --pic_pd: 0; --img_pos: relative; --top: 0; --height: 100%; }
#root #content .l4pr .swiper-pagination-bullets .swiper-pagination-bullet picture img, #root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet picture img {
	display: block; position: var(--img_pos); left: 0; right: 0; top: var(--top); width: 100% !important; height: var(--height) !important; max-height: 100% !important;
	object-fit: var(--ob_fit);
	align-self: auto;
}
#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet:not(.auto) picture:not(.cover, [class*="img-multiply"]) > * { transform: translateY(-50%); }
.l4pr picture[class*="img-multiply"], .l4pr picture.cover { --top: 0; --height: 100%; --ob_fit: cover; }
/*#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet[class*="orientation-"] picture { }
			#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet picture > * { display: block; position: relative; left: 0; right: 0; width: 100% !important; height: auto !important; max-height: 100% !important; }
				#root #content .l4pr .swiper-pagination-bullets .swiper-pagination-bullet[class*="orientation-"] picture:not(.cover, [class*="img-multiply"]) > * { position: absolute; top: 0; width: 100% !important; height: 100% !important; }
				#root #content .l4pr .swiper-pagination-bullets .swiper-pagination-bullet[class*="orientation-"] picture.cover > * { top: 0; height: 100% !important; }*/
/*#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet picture.cover > *, #root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet picture[class*="img-multiply"] > * { top: 0; height: 100% !important; }*/
/*#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.auto, #root .l4pr.auto .swiper-pagination-bullets .swiper-pagination-bullet { height: auto; }
		#root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.auto picture, #root .l4pr.auto .swiper-pagination-bullets .swiper-pagination-bullet picture { padding-top: 0; }*/
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet:before { left: 0; right: 0; top: 0; bottom: 0; z-index: -1; width: auto; height: auto; margin: 0; border-radius: var(--b2r); border: 1px solid var(--custom_bd); background: var(--body_bg); opacity: 1; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active:before { border-color: var(--secondary_bg); }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet i { display: none; position: absolute; left: 50%; top: 50%; z-index: 9; width: 20px; height: 20px; margin: -10px 0 0 -10px; border-radius: 10px; background: var(--secondary_bg); color: var(--white); font-size: 10px; line-height: 20px; text-align: center; text-indent: 0; direction: ltr; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet i.icon-play, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet i.icon-cube { display: block; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet i.icon-play:before { content: "\e944"; font-size: 20px; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet img { border-radius: var(--b2p); object-fit: contain; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more { display: block; position: absolute; left: 0; right: 0;top: 0; bottom: 0; z-index: 9; color: var(--white); font-size: var(--size_14_f); font-weight: var(--main_fw_strong); text-indent: 0; text-align: center; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet.has-more i { display: none; }
.l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span:before, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more:before { border-radius: var(--b2r); background: var(--coal); opacity: .4; }
.l4pr .swiper-custom-pagination .model-3d, .l4pr .swiper-custom-pagination .model-3d model-viewer, .fancybox__thumbs .model-3d, .fancybox__thumbs .model-3d model-viewer { display: none !important; visibility: hidden; opacity: 0; }
#root .l4pr li.swiper-outer:first-child, .l4pr img { display: block; }
.l4pr .custom-progressbar { display: block; position: absolute; left: 0; right: 0; bottom: 0; z-index: 9; height: 2px; border-radius: var(--b2r); }
.l4pr .custom-progressbar > * { display: block; width: 0%; height: 100%; background: var(--secondary_bg); }
.l4pr .swiper-button-nav { bottom: auto; width: 67px; height: 540px; }
.l4pr .swiper-button-next { left: 100%; right: auto; }
.l4pr .swiper-button-prev { right: 100%; left: auto; }
.l4pr .li a[href^="#model-3d"] picture img { display: none; }
.l4pr .li a[href^="#model-3d"] .model-3d { display: block !important; }
/*.l4pr.slider-fraction {}*/
.l4pr.slider-fraction .swiper-custom-pagination { padding-top: var(--main_mr_half); }
.l4pr.slider-fraction .swiper-custom-fraction { display: block; }
.l4pr.slider-fraction .swiper-pagination-bullets { display: none; }
.l4pr.slider-fraction .swiper-button-nav { height: 30px !important; }
#root .l4pr li.sticky, .l4pr li:not(.sticky) .m6bx { display: block; position: absolute; left: calc(var(--dist) + 10px); right: auto; bottom: calc(var(--dist) + 10px); top: calc(var(--dist) + 10px); width: auto; max-width: calc(100% - 20px - var(--dist)); pointer-events: none; }
#root .l4pr li.sticky { margin: 0; padding: 0; border-width: 0; }
.l4pr li:not(.sticky) .m6bx { top: auto; }
.l4pr li.sticky a { display: inline; position: relative; height: auto; pointer-events: auto; }
.l4pr .m6bx { z-index: 9; margin: 0; padding-left: 20px; padding-right: 20px; text-align: var(--text_align_start); }
.no-js .l4pr .m6bx:not(:first-child) { margin-top: var(--dist); }
.l4pr .m6bx:before { box-shadow: var(--bx_sh); border-width: 0; border-color: var(--white); background: var(--white); }
.l4pr .li .m6bx-inside { display: none; }
.l4pr .swiper-outer > .m6bx-inside { position: absolute; left: calc(var(--dist) + 10px); right: auto; bottom: calc(var(--dist) + 10px + 2px); max-width: calc(100% - 20px - var(--dist)); }
.l4pr.no-scrollbar .swiper-outer > .m6bx-inside { bottom: calc(var(--dist) + 10px); }
#root .l4pr[class*="thumbs-s"] .swiper-pagination-bullets .swiper-pagination-bullet { display: flex; }
.l4pr[class*="thumbs-s"] .swiper-pagination-bullets .swiper-pagination-bullet span span { display: none; }
.l4pr.thumbs-slider .swiper-pagination-bullets { display: block; margin-left: 0; margin-right: 0; margin-bottom: var(--dist_li);}
.l4pr.thumbs-slider .swiper-pagination-bullets .swiper-slide { width: auto !important; }
.l4pr.thumbs-slider .swiper-pagination-bullets .swiper-wrapper { padding-bottom: 10px; align-items: center; }
.l4pr.thumbs-slider .swiper-pagination-bullets .swiper-wrapper .swiper-pagination-bullet { margin-bottom: 0; }
#root .l4pr.thumbs-slider .swiper-pagination-bullets .swiper-button-nav {
	display: block; visibility: visible; top: 0; bottom: 10px; width: 38px; height: auto !important; color: var(--primary_text); opacity: 1;
	transform: none;
}
#root .l4pr.thumbs-slider .swiper-pagination-bullets .swiper-button-nav.swiper-button-disabled { opacity: .35; }
#root .l4pr.thumbs-slider .swiper-pagination-bullets .swiper-button-nav:after { display: none; }

@media only screen and (min-width: 1101px), only screen and (min-width: 761px) and (max-width: 1000px) {
	.l4pr.aside-pager.s4wi { margin-bottom: var(--main_mr); padding-left: var(--pd); --d2: 16px; --pd: calc(var(--pager_w) + 16px); --fl: right; --fl2: left; --ml: calc(0px - var(--pager_w) - 16px); }
	.l4pr.aside-pager.s4wi > .swiper-outer { float: var(--fl); width: 100%; }
	.l4pr.aside-pager.s4wi > .swiper-pagination-bullets, .l4pr.aside-pager.s4wi > .swiper-custom-pagination { float: var(--fl2); width: var(--pager_w); margin-top: 0; margin-bottom: 0; margin-left: var(--ml); padding-top: 0; }
	.l4pr.aside-pager.s4wi .swiper-pagination-bullets { padding-top: 0; }
	.l4pr.aside-pager.s4wi.slider-fraction .swiper-custom-fraction { display: none; }
	.l4pr.aside-pager.s4wi .swiper-button-nav { width: 44px; color: var(--coal); }
	.l4pr.aside-pager.s4wi .swiper-button-nav:after { top: 50%; bottom: auto; height: 44px; margin-top: -22px; background: var(--white); opacity: .7; }
	[dir="ltr"] #root #content .l4pr.aside-pager.s4wi:not(.inv) .swiper-button-prev { left: calc(var(--pager_w) + var(--d2) + 16px); right: auto; }
	[dir="ltr"] #root #content .l4pr.aside-pager.s4wi.inv .swiper-button-prev { right: calc(var(--pager_w) + var(--d2) + 16px); left: auto; }
	.l4pr.aside-pager.s4wi .swiper-button-next { right: 16px; left: auto; }
	.l4pr.aside-pager.s4wi.inv .swiper-button-next { left: 16px; right: auto; }
	.l4pr.aside-pager.s4wi .swiper-button-prev:after, .l4pr.aside-pager.s4wi .swiper-button-next:after { border-radius: 48px; }
	.l4pr.aside-pager.s4wi .swiper-pagination-bullets { margin-top: 0; }
	[dir="ltr"] #root #content .m6pr .l4pr.aside-pager.s4wi:not(.inv) > .s1lb { left: calc(var(--pager_w) + 16px + var(--label_dist)); }
	[dir="ltr"] #root #content .m6pr .l4pr.aside-pager.s4wi.inv > .s1lb { left: 0; }
	.l4pr.slider-fraction.aside-pager .swiper-pagination-bullets { display: flex; }
	#root .m6pr .l4pr.aside-pager.s4wi { border-left-width: 0; border-right-width: 0; }
	#root .slider-fraction.l4pr.aside-pager .swiper-custom-pagination .swiper-button-nav { display: none; }
	.l4pr.aside-pager.s4wi.inv { padding-left: 0; padding-right: var(--pd); --fl: left; --fl2: right; }
	.l4pr.aside-pager.s4wi.inv > .swiper-pagination-bullets, .l4pr.aside-pager.s4wi.inv > .swiper-custom-pagination { margin-left: 0; margin-right: var(--ml); }
}
@media only screen and (min-width: 761px) and (max-width: 1000px) {
	#root .shopify-section-header .l4us:not(:first-child) { display: none; }
	#root .shopify-section-header .l4us:first-child + .l4us:not(.l4us-mobile) { display: block; height: 100%; }
}

.l4st { list-style: none; margin: 30px 0 16px -16px; padding: 0; --iz: 44px; }
.l4st:not([class*="width-"]) { --width: 25%; }
.l4st li { width: var(--width); margin: 0 0 10px; border-left: 16px solid rgba(0,0,0,0); }
.l4st h1, .l4st h2, .l4st h3, .l4st h4, .l4st h5, .l4st h6 { margin: 0 0 15px; }
.l4st h1 i, .l4st h2 i, .l4st h3 i, .l4st h4 i, .l4st h5 i, .l4st h6 i, .l4st figure { display: block; margin: 0 0 14px; color: var(--secondary_bg); font-size: var(--iz); line-height: 54px; }
.l4st .icon-truck { --iz: 36px; }
.l4st .icon-shop { --iz: 42px; }
.l4st .icon-app { --iz: 54px; }
.l4st:not(.s4wi) .link-btn { margin-top: auto; }
.l4st.text-center img, .l4st .text-center img { margin-left: auto; margin-right: auto; }
.l4st.s4wi { display: block; margin-left: 0; margin-bottom: var(--main_mr); }
.l4st.s4wi .li { width: auto; margin-bottom: 0; border-left-width: 0; }
.l4st .swiper-pagination-bullets { margin-top: -10px; }

.l4ts { list-style: none; margin: 42px 0 0 -16px; padding: 0; text-align: center; --main_mr: 14px; }
.l4ts li { position: relative; z-index: 2; width: 33.33333333333%; margin: 0 0 40px; border-left: 16px solid rgba(0,0,0,0); }
.l4ts p { font-style: inherit; font-size: 1em; }
.l4ts q { display: block; max-width: 430px; margin: 0 auto var(--main_mr); font-style: inherit; font-size: calc(var(--main_fz) * 1.1428571429); }
.l4ts span span { font-weight: var(--main_fw); }
.l4ts .r6rt { display: inline-block; }
.l4ts .r6rt .rating > * .fill { color: var(--secondary_bg); }
.cols + .l4ts, .cols + .l4fs { margin-top: 12px; }
.l4ts.box { margin-bottom: 24px; margin-left: -16px; }
.l4ts.box li { margin-bottom: 16px; padding: 42px 26px 36px; border-left-width: 16px; color: var(--primary_text); }
.l4ts.box li:before, .l4ts.box .r6rt .rating > * .fill, .l4ts.wide.box.s4wi .swiper-outer { background: var(--sand); }
.m6wd .l4ts.box li:before, .m6wd .l4ts.box .r6rt .rating > * .fill, .m6wd .l4ts.wide.box.s4wi .swiper-outer { background: var(--primary_bg); }
/*.l4ts.wide {}*/
.l4ts.wide li, .l4ts li:first-child:last-child, .l4ts.w100 li, .l4ts li.w100 { width: 100%; }
.l4ts.wide q, .l4ts > li:first-child:last-child q, .l4ts.w100 q, .l4ts .w100 q { max-width: 644px; margin-left: auto; margin-right: auto; }
/*.l4ts.w50 {}*/
.l4ts.w50 li, .l4ts li.w50 { width: 50%; }
.l4ts.w50 q, .l4ts .w50 q { max-width: 430px; margin-left: auto; margin-right: auto; }
.l4ts.s4wi { display: block; margin-left: 0; margin-bottom: 40px; }
.l4ts .swiper-outer { overflow: hidden; }
.l4ts.s4wi .li { width: 100%; margin-left: auto; margin-right: auto; margin-bottom: 0; border-left-width: 0; }
.l4ts .swiper-pagination-bullets, .l4ts .swiper-custom-pagination, .l4ts .swiper-custom-fraction { padding-top: 15px; }
.l4ts.box .swiper-pagination-bullets, .l4ts.box .swiper-custom-pagination, .l4ts.box .swiper-custom-fraction { padding-top: 20px; }
#root .l4ts .swiper-custom-pagination .swiper-pagination-bullets { padding-top: 0; }
.l4ts.s4wi.box li:before { display: block; }

.l4sc { list-style: none; margin: 28px calc(0px - var(--dist)) calc(var(--main_mr) - var(--dist_b)) 0; padding: 0; --dist: 30px; --dist_b: 22px; --sz: var(--main_fz); }
.l4sc li { margin-right: var(--dist); margin-bottom: var(--dist_b); }
.l4sc li.title { margin-right: calc(var(--main_fz) * 1.0714285714); }
.l4sc a { display: block; position: relative; }
.l4sc:not(.box) a:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; z-index: 9; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
#root .l4sc:not(.box) a:after { display: block; }
.l4sc i { display: block; position: relative; top: 0; margin: 0; font-size: calc(1.2857142857 * var(--sz)); line-height: 1; }
.l4sc .icon-envelope { top: .1em; font-size: calc(0.7857142857 * var(--sz)); }
.l4sc .icon-twitter { font-size: var(--sz); }
.l4sc .icon-youtube { font-size: calc(1.0714285714 * var(--sz)); }
.l4sc .icon-facebook, .l4sc .icon-vimeo, .l4sc .icon-wechat, .l4sc .icon-weibo { font-size: calc(1.1428571429 * var(--sz)); }
.shopify-section:not(:last-child) > .l4sc:last-child { margin-bottom: 12px; }
#root .m0 + .l4sc { margin-top: -2px; }
.l4sc.size-s { --sz: calc(var(--main_fz) * 1.1111111111); }
.l4sc.size-m { --sz: calc(var(--main_fz) * 1.3888888889); }
.l4sc.size-l { --sz: calc(var(--main_fz) * 1.7777777778); }
.l4sc.box { position: relative; z-index: 2; padding: 14px 3px; }
.l4sc.box:before { box-shadow: 0 0 2px rgba(0,0,0,.06); border-radius: var(--b2r); border: 1px solid var(--custom_bd); background: var(--white); }
#root .l4sc.box li { min-height: 0; margin: 0 0 8px; padding: 0 15px; }
.l4sc.box li a { display: block; overflow: visible; padding: 0; }
#root .l4sc.box li a i { display: block; top: 0; min-width: 0; margin: 0; line-height: var(--size_18_f); }
.l4sc.box .icon-envelope { color: var(--secondary_bg); }
.l4sc.box .icon-facebook { color: var(--facebook); }
.l4sc.box .icon-pinterest { color: var(--pinterest); }
.l4sc.box .icon-twitter { color: var(--twitter); }
.l4sc.box .icon-whatsapp { color: var(--whatsapp); }
.l4sc.box .icon-youtube { color: var(--youtube); }
.l4sc.box .icon-linkedin { color: var(--linkedin); }

.l4tt { list-style: none; padding: 0; font-size: var(--main_fz); }
.l4tt li { margin-bottom: 3px; }
.l4tt li > span:first-child { margin-right: auto; padding-right: 6px; }
.l4tt li.overlay-valid, .l4tt li.overlay-error { display: block; text-align: inherit; }
.l4tt li.size-18 { margin-top: -2px; font-weight: var(--main_fw_strong); font-size: calc(var(--main_fz) * 1.2857142857); }
.l4tt .icon-x-circle { position: relative; top: 1px; }
.table-wrapper + .l4tt, table + .l4tt { padding-left: 20px; padding-right: 20px; }

.l4us { list-style: none; padding: 0; --pd: calc(var(--main_fz) * 1.5); }
.l4us li { position: relative; max-width: 100%; padding-left: var(--pd); }
#nav-top .l4us li:not(.rendered) { overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
.l4us li:before, .l4us li.custom-icon > i:first-child { content: "\e901"; display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; margin: 0; font-size: 0.7em; line-height: calc(var(--main_lh) * var(--main_fz)); }
.l4us li:before { color: var(--lime); }
.shopify-section > .l4us:last-child { margin-bottom: 50px; }
.l4us svg { width: auto; height: var(--main_fz); }
#root .l4us a.next-item { display: none; }
.l4us .next-item:before { content: "\e906"; }
.l4us .cols { justify-content: flex-start; align-items: center; }
.l4us:empty { margin: 0; padding: 0; }
.l4us:empty + hr { display: none; }
.l4us.empty { margin: 0; padding: 0; }
.l4us.empty + hr { display: none; }
.l4us.plus { --pd: calc(var(--main_fz) * 2); --plus_size: calc(var(--main_fz) * 1.3571428571); }
.l4us.plus li:before { content: "\e948"; top: calc(var(--main_fz) * var(--main_lh) * 0.5 - var(--plus_size) * 0.5); width: var(--plus_size); height: var(--plus_size); padding-left: 0; box-shadow: var(--btn_sh_inner); border-radius: var(--plus_size); background: var(--alert_valid); color: var(--white); font-size: var(--plus_size); line-height: var(--plus_size); }
.l4us.plus li.overlay-content:before { content: "\e949"; background: var(--gray); }
@media only screen and (min-width: 761px) {
	#root .l4us a.next-item {
		display: inline-block; position: relative; width: 26px; height: 100%; color: inherit; font-size: 8px; text-decoration: none; text-align: left; text-indent: -3000em; direction: ltr;
		align-self: center;
	}
	#root .l4us.slider-single .swiper-button-next, #root .shopify-section-header .l4us.slider-single .swiper-button-next, #root .l4us.no-arrows a.next-item { display: none; }
	#root .l4us a.next-item:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
	.l4us + .l4us { margin-top: calc(0px - var(--main_mr)); }
}
@media only screen and (max-width: 760px) {
	.l4us:not(.mobile-hide) + .l4us { margin-top: calc(0px - var(--main_mr)); }
}
.l4us.wide { position: relative; z-index: 2; margin-right: -32px; padding-top: 10px; padding-bottom: 10px; }
.l4us.wide:before { background: var(--sand); }
.l4us.wide li { margin-right: 32px; }
.l4us.wide.s4wi { display: block; margin-right: 0; }
.l4us.wide.s4wi .swiper-slide { padding: 0 var(--rpp); }
.l4us.wide.s4wi .swiper-slide:not(.swiper-slide-active) { visibility: hidden; opacity: 0; }
.l4us.wide.s4wi.no-nav .swiper-slide, .shopify-section-header .l4us.wide.s4wi .swiper-slide { padding-left: 0; padding-right: 0; }
.shopify-section-header .l4us.s4wi .swiper-outer { overflow: hidden; }
.shopify-section-header .l4us.s4wi .swiper-button-nav { display: block; width: 20px; height: auto !important; color: inherit; font-size: 8px; }
.shopify-section-header:not(.fixed) .l4us.s4wi .swiper-button-nav { overflow: visible; }
.shopify-section-header .l4us.s4wi .swiper-button-nav:after { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; }
@media only screen and (min-width: 1001px) {
	.shopify-section-header .l4us.s4wi .swiper-button-next:after { margin-left: -22px; }
	.shopify-section-header .l4us.s4wi .swiper-button-prev:after { margin-left: -8px; }
}
@media only screen and (max-width: 1000px) {
	.shopify-section-header .l4us.s4wi:not(.no-arrows) .swiper-button-nav, .shopify-section-header .l4us.s4wi:not(.no-arrows) a.next-item { width: 44px; min-width: 44px; }
	.shopify-section-header .l4us.s4wi:not(.no-arrows) .swiper-button-nav:after { height: 34px; margin-top: -17px; }
	.shopify-section-header #nav-top .l4us.s4wi:not(.no-arrows) { padding-left: 28px; padding-right: 28px; }
	.shopify-section-header .l4us.s4wi:not(.no-arrows) .swiper-button-prev { right: calc(100% - 28px); }
	.shopify-section-header .l4us.s4wi:not(.no-arrows) .swiper-button-next { left: calc(100% - 28px); }
}
.shopify-section-header .l4us.no-arrows .swiper-button-nav { display: none; }
.shopify-section-header .l4us.s4wi .swiper-button-prev { left: auto; right: 100%; }
.shopify-section-header .l4us.s4wi .swiper-button-next { right: auto; left: 100%; }
/*.l4us.no-checks {}*/
.l4us.no-checks li, .l4us li.no-checks { padding-left: 0; padding-right: 0; }
.l4us.no-checks li:before, .l4us li.no-checks:before, .l4us.custom-icon li:before, .l4us li.custom-icon:before { display: none; }
/*.l4us:has(img), .l4us:has(svg) { align-items: center; }
.l4us.has-img, .l4us.has-img { align-items: center; }*/


/*! Forms --------- */
fieldset, hr { min-width: 0; margin: 0; padding: 0; border-width: 0; }
fieldset { width: 100%; }
form p { position: relative; z-index: 1; }
.has-select { position: relative; z-index: 2; }
legend { display: none; width: 100%; max-width: 100%; padding: 0; border: 0; white-space: normal; }
input, select, textarea {
	position: relative; z-index: 1; margin: 0; padding: 0; box-shadow: none; border-radius: 0; font-size: 1em; line-height: normal; text-align: left; text-transform: none; outline-width: 0;
	-moz-appearance: none; -webkit-appearance: none; appearance: none;
}
input, select, textarea, button, input[type="button"], input[type="reset"], input[type="submit"] { box-sizing: border-box; text-transform: none; outline-offset: -2px; }
/*input {}*/
input::-webkit-search-decoration, input::-webkit-search-cancel-button, input::-webkit-outer-spin-button, input::-webkit-inner-spin-button, input::-webkit-clear-button, input::-webkit-calendar-picker-indicator { display: none; -webkit-appearance: none; visibility: hidden !important; height: auto; margin: 0; }
input:-webkit-autofill, input:-webkit-autofill:hover,  input:-webkit-autofill:focus, input:-webkit-autofill:active { -webkit-box-shadow: 0 0 0 30px var(--custom_input_bg) inset !important; }
input::-o-outer-spin-button, input::-o-inner-spin-button { appearance: none; margin: 0; }
input::-webkit-inner-spin-button, input::-webkit-outer-spin-button { height: auto; }
input::-webkit-file-upload-button { -webkit-appearance: button; font: inherit; }
input::-webkit-datetime-edit { display: none; }
[type="search"]::-webkit-search-decoration { -webkit-appearance: none; }
input[type="checkbox"], input[type="radio"] { display: inline-block; width: auto; height: auto; padding: 0; border: 0; background: none; line-height: 1; }
input[type="checkbox"] { -moz-appearance: checkbox; -webkit-appearance: checkbox; appearance: checkbox; }
input[type="radio"] { -moz-appearance: radio; -webkit-appearance: radio; appearance: radio; }
input[type="color"] { padding: 0; cursor: pointer; }
input[type="date"] { display: -webkit-inline-flex; }
input[type="number"] { appearance: textfield; -moz-appearance: textfield; }
input[type="number"]::-webkit-outer-spin-button,  input[type="number"]::-webkit-inner-spin-button { -webkit-appearance: none; margin: 0; }
input[type="number"]::-o-outer-spin-button, input[type="number"]::-o-inner-spin-button { -o-appearance: none; margin: 0; }
textarea { overflow: auto; vertical-align: top; resize: vertical; }
select { line-height: 1; text-overflow: ''; text-indent: 0.01px; }
select::-ms-expand { display: none; }
button, input[type="button"], input[type="reset"], input[type="submit"] { display: block; overflow: visible; position: relative; z-index: 3; height: auto; margin: 0; box-shadow: none; text-transform: none; text-indent: 0; cursor: pointer; outline-width: 0; -webkit-appearance: button; appearance: button; -webkit-font-smoothing: inherit; }
button::-moz-focus-inner, input::-moz-focus-inner { padding: 0; border: 0; outline: none; }
#root button.disabled:not(.visible), button[disabled]:not(.visible), input[type="button"][disabled]:not(.visible), input[type="reset"][disabled]:not(.visible), input[type="submit"][disabled]:not(.visible), button[disabled]:not(.visible), input[type="button"][disabled]:not(.visible), input[type="reset"][disabled]:not(.visible), input[type="submit"][disabled]:not(.visible), .link-btn a.disabled:not(.visible) { opacity: .5; }

::placeholder { opacity: 1; color: var(--custom_input_pl); font-size: var(--placeholder_fz); }
:focus::placeholder { opacity: 0; }

/*form {}*/
label, .label { display: block; position: relative; left: 0; top: 0; z-index: 2; margin: 0 0 4px; color: inherit; font-weight: var(--main_fw_strong); font-size: var(--main_fz); font-family: var(--main_ff); letter-spacing: var(--main_ls); }
label span, .label span { margin-left: 3px; font-weight: var(--main_fw); }
label span + span, .label span + span { margin-left: 20px; }
label span.text-end, .label span.text-end { display: block; float: right; margin-left: 0; margin-right: 0; }
label span.text-end i, .label span.text-end i { display: inline-block; position: relative; line-height: 1px; }
label span.strong, .label span.strong, label .strong span, .label .strong span { font-weight: var(--main_fw_strong);}
label i, .label i, .size-12 i:not(.icon-chevron-down, .icon-chevron-up) { display: inline-block; position: relative; top: .1em; margin-right: 4px; font-size: 1.1666666667em; line-height: 1px; }
label .text-end i, .label .text-end i { margin-left: 12px; }
label i.icon-size, .label i.icon-size { top: 0; font-size: 0.8333333333em; }
label i.icon-ruler, .label i.icon-ruler { top: 0; font-size: 0.7142857143em; }
label picture { width: auto; max-width: 100%; }
#root .data-change-to * { margin: 0; }
input, select, textarea, .bv_atual, #root .bv_mainselect input { display: block; width: 100%; border-radius: var(--b2r); border: 1px solid var(--custom_input_bd); background-position: calc(100% - 15px) center; background-repeat: no-repeat; background-color: var(--custom_input_bg); background-size: auto 9px; color: var(--custom_input_fg); font-weight: var(--main_fw); font-size: var(--main_fz); text-align: var(--text_align_start); }
#root .done select, #root .done > .select-wrapper select, #root .done > .select-wrapper .bv_mainselect .bv_atual { color: var(--custom_input_fg); }
input:focus, select:focus, textarea:focus, #search input:focus { border-color: var(--secondary_bg); }
select:focus { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 5.7' style='enable-background:new 0 0 9 5.7' xml:space='preserve'%3E%3Cpath d='M.6 5.5c.2.1.4.2.6.2s.3-.1.5-.2l2.8-2.8 2.8 2.8c.2.1.3.2.5.2s.4-.1.6-.2l.4-.4c.1-.2.2-.4.2-.6s-.1-.4-.2-.5L5 .2C4.9.1 4.7 0 4.5 0s-.4.1-.5.2L.2 4c-.1.1-.2.3-.2.5s.1.4.2.6l.4.4z' style='fill:%23959595'/%3E%3C/svg%3E"); }
input, select, .bv_atual { height: var(--input_h); padding: 0 calc(var(--main_fz) * 1.1428571429); }
#root .bv_atual { color: var(--custom_input_pl); }
/*
			Connie: removed "size-" for buttons.
			input.size-s, select.size-s, .bv_atual.size-s, .size-s input, .size-s select, .size-s .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 0.65 * 2) }
			input.size-m, select.size-m, .bv_atual.size-m, .size-m input, .size-m select, .size-m .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 1.2 * 2) }
			input.size-l, select.size-l, .bv_atual.size-l, .size-l input, .size-l select, .size-l .bv_atual { --input_h: calc(var(--btn_fz) * var(--main_lh_h) + var(--btn_pv) * 1.35 * 2) }*/
input[type="date"] { line-height: calc(var(--main_fz) * 3.0714285714); }
input ~ .size-12, select ~ .size-12, textarea ~ .size-12, .bv_atual ~ .size-12, .select-wrapper ~ .size-12 { display: block; margin-top: 6px; }
.sl_inner { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
select, .bv_atual, .datepicker-input, input[type="date"] { padding-right: var(--input_h); }
select, .bv_atual { padding-right: calc(var(--main_fz) * 3.5714285714 * 0.75); }
select, .bv_atual { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9 5.7' style='enable-background:new 0 0 9 5.7' xml:space='preserve'%3E%3Cpath d='M8.4.2C8.2.1 8 0 7.8 0s-.3.1-.5.2L4.5 3 1.7.2C1.5.1 1.4 0 1.2 0S.8.1.6.2L.2.6C.1.8 0 1 0 1.2s.1.4.2.5L4 5.5c.*******.5.2s.4-.1.5-.2l3.8-3.8c.1-.1.2-.3.2-.5S8.9.8 8.8.6L8.4.2z' style='fill:%23959595'/%3E%3C/svg%3E"); background-position: calc(100% - var(--main_fz) * 1.3571428571) center; background-size: auto calc(var(--main_fz) * 0.4285714286); line-height: normal; text-overflow: ellipsis; }
.bv_atual { background-position: -3000em -3000em; }
.bv_atual:before {
	display: block; overflow: visible; position: absolute; left: 0; right: 0; top: 50%; bottom: auto; margin: -10px 0 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; font-weight: 400; font-family: i; line-height: 20px; text-align: center; text-indent: 0; letter-spacing: normal;
	content: "\e904"; left: auto; right: 0; width: calc(var(--main_fz) * 3.5714285714); font-size: calc(var(--main_fz) * 0.4285714286);
}
select.disabled { color: var(--custom_input_pl); }
#root input[type="date"], #root .datepicker-input { background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' xml:space='preserve' style='enable-background:new 0 0 14 16' viewBox='0 0 14 16' fill='%23959595'%3E%3Cpath d='M12.3 2H11V.8c0-.5-.3-.8-.7-.8s-.8.3-.8.8V2h-5V.8c0-.5-.3-.8-.7-.8S3 .3 3 .8V2H1.8C.8 2 0 2.8 0 3.8v10.5c0 1 .8 1.8 1.8 1.8h10.5c1 0 1.8-.8 1.8-1.8V3.8c-.1-1-.9-1.8-1.8-1.8zm.2 12.3c0 .1-.1.3-.3.3H1.8c-.1 0-.3-.1-.3-.3V7.5h11v6.8zm0-8.3h-11V3.8c0-.1.1-.3.3-.3h10.5c.1 0 .3.1.3.3V6z'/%3E%3C/svg%3E"); background-position: calc(100% - var(--main_fz)) center; background-size: auto max(18px, calc(var(--input_h) - var(--main_fz) * 4)); line-height: normal; }
textarea { height: calc(var(--main_fz) * 11.5714285714); min-height: calc(var(--main_fz) * 11.5714285714); padding: calc(var(--main_fz) * 0.9285714286) calc(var(--main_fz) * 0.9285714286) calc(var(--main_fz) * 0.9285714286) calc(var(--main_fz) * 1.0714285714); }
aside textarea { height: calc(var(--main_fz) * 9.6428571429); min-height: calc(var(--main_fz) * 9.6428571429); }
input + *, select + *, textarea + *, .input-prefix + * { display: block; margin-top: 6px; }
input.hidden + *, select.hidden + *, textarea.hidden + *, .input-prefix.hidden + * { margin-top: 0; }
input + style, select + style, textarea + style, .input-prefix + style { display: none; }
input[disabled], select[disabled], textarea[disabled] { /*border-color: var(--alto);*/ /*background-color: var(--sand);*/ opacity: .3; }
button, input[type="button"], input[type="reset"], input[type="submit"], .link-btn a { display: block; position: relative; z-index: 3; float: left; width: auto; min-width: var(--btn_miw); min-height: calc(var(--btn_pv) * 2 + var(--btn_fz) * var(--btn_lh)); margin: 0 var(--btn_dist) var(--btn_dist2) 0; padding: var(--btn_pv) var(--btn_ph); box-shadow: var(--btn_sh_inner); border-radius: var(--btn_br); border: 0 solid var(--secondary_bg_btn); background: var(--secondary_bg_btn); color: var(--secondary_btn_text); font-weight: var(--btn_fw); font-style: var(--btn_fs); font-family: var(--btn_ff); font-size: var(--btn_fz); line-height: var(--btn_lh); text-indent: 0; text-align: center; text-decoration: none; text-transform: var(--btn_tt); letter-spacing: var(--btn_ls); cursor: pointer; }
button.no-paddings, input[type="button"].no-paddings, input[type="reset"].no-paddings, input[type="submit"].no-paddings, .link-btn a.no-paddings { --btn_ph: 0px; }
.link-btn > span, .submit > span { line-height: var(--main_lh_l); }
.link-btn > *, .submit > *, .link-btn > a, .submit > button, .link-btn > button, .link-btn > input { margin-right: var(--btn_dist); }
button[class*="overlay"], [class*="overlay"] button { color: var(--secondary_btn_text); }
button, .link-btn a, #totop a { box-shadow: none; background: none; }
#root button, #root .link-btn a { background: none; }
button:before, .link-btn a:before, #nav-user > ul > li > a i span:before, #totop a:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: -1; box-shadow: var(--btn_sh_inner); border-radius: var(--btn_br); border: 0 solid var(--secondary_bg_btn); background: var(--secondary_bg_btn); }
.n6pg li.prev a, .n6pg li.next a, .spr-pagination > div > .spr-pagination-prev a, .spr-pagination > div > .spr-pagination-next a { overflow: visible; }
#root .n6pg li.prev a:before, #root .n6pg li.next a:before, .spr-pagination > div > .spr-pagination-prev a:before, .spr-pagination > div > .spr-pagination-next a:before { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -22px; border-radius: 0; }
button.no-shadow:before, .link-btn a.no-shadow:before { box-shadow: none; }
#root .dynamic-checkout__content li { flex-grow: 3 !important; }
.shopify-payment-button div[role="button"], .shopify-payment-button button { float: none; width: 100%; margin-left: 0; margin-right: 0; margin-bottom: 8px; border-radius: var(--btn_br); }
/*.m6pr .shopify-payment-button div[role="button"], .m6pr .shopify-payment-button button { width: auto; }*/
shopify-buy-it-now-button, dynamic-checkout { width: 100%; }
[data-whatintent="mouse"] .shopify-payment-button div[role="button"]:not(:hover), [data-whatintent="mouse"] .shopify-payment-button button:not(:hover) { animation: none !important; }
#root .submit a.bv_atual { font-weight: var(--main_fw); }

#root input + .size-12, #root select + .size-12, #root textarea + .size-12, #root .select-wrapper + .size-12 { display: block; margin-top: 6px; font-size: var(--main_fz_small); }

.bv_mainselect { display: block; margin: 0; }
.bv_mainselect .bv_ul_inner { display: none !important; }
.bv_atual { overflow: hidden; color: var(--custom_input_pl); line-height: calc(var(--input_h) - 2px); text-decoration: none; text-overflow: ellipsis; white-space: nowrap; }
#root .select-wrapper.done .bv_mainselect .bv_atual { color: var(--custom_input_fg); }

.select-wrapper { display: block; position: relative; z-index: 9; }
.select-wrapper select { display: block; position: relative; left: 0; top: 0; }

.check {
	position: relative; z-index: 2; list-style: none; margin-bottom: var(--main_mr); padding: 0; /*font-size: var(--size_14_f);*/ --check_color_size: 28px; --check_color_dist: 5px; --check_color_space: 8px; --dist_check_li: 8px;
	justify-content: var(--justify_content);
	--box_size: calc(var(--main_fz) * 1.2857142857); --img_s: var(--box_size);
}
.check li, .check > * { position: relative; z-index: 2; margin-bottom: var(--dist_check_li); }
.check label { display: block; position: relative; z-index: 2; margin: 0; padding: 0 0 0 calc(var(--box_size) + 10px); font-weight: var(--main_fw); font-size: var(--main_fz); line-height: var(--main_lh); }
.check label:before, .check label:after { content: ""; /*display: block;*/ overflow: hidden; position: absolute; left: 0; top: calc(var(--main_fz) * var(--main_lh) * 0.5 - var(--main_fz) * 0.6428571429); z-index: 2; width: var(--box_size); height: var(--box_size); border-radius: var(--b2r); border: 1px solid var(--custom_input_bd); background: var(--custom_input_bg); color: var(--white); font-family: i; font-weight: 400; font-size: calc(var(--main_fz) * 1.2857142857 - 2px); line-height: calc(var(--main_fz) * 1.2857142857 - 2px); text-align: center; text-indent: 0; cursor: pointer; }
.check label:after { border-color: rgba(0,0,0,0); background: none; }
.check input:checked ~ label:before { content: "\e92c"; border-color: var(--secondary_bg); background: var(--secondary_bg); }
.check input[disabled]:checked ~ label:before, .check input.disabled:checked ~ label:before { content: "\e92c"; border-color: var(--alto); background: var(--alto); }
.check input[type="radio"] ~ label:before, .check input[type="radio"] ~ label:after { content: ""; overflow: hidden; border-radius: 99px; color: var(--secondary_bg); text-align: left; text-indent: -3000em; direction: ltr; }
.check input[type="radio"]:checked ~ label:before { box-shadow: inset 2px 2px 0 var(--custom_input_bg), inset -2px 2px 0 var(--custom_input_bg), inset 0 3px 0 var(--custom_input_bg), inset 2px -2px 0 var(--custom_input_bg), inset -2px -2px 0 var(--custom_input_bg), inset 0 -3px 0 var(--custom_input_bg), inset 3px 0 0 var(--custom_input_bg), inset -3px 0 0 var(--custom_input_bg); }
.check label span { margin: 0; }
.check.size-12 label:before, .check.size-12 label:after { top: calc(var(--main_fz_small) * var(--main_lh) * 0.5 - var(--main_fz) * 0.6428571429); }
.check input[disabled] ~ label, .check input.disabled ~ label { color: var(--gray); }
.check label[class*="hidden"] { width: var(--box_size); height: var(--box_size); padding: 0; text-align: left; text-indent: -3000em; direction: ltr; }
.check label[class*="hidden"]:before, .check label[class*="hidden"]:after { top: 0; }
.check input { display: block; position: absolute; left: 0; top: 0; z-index: -1; opacity: 0; }
#root .check .s1pr { margin: 0; font-size: 1em; }
.check .s1pr .small { margin-top: 0; color: var(--secondary_bg); }
.check figure { --b2p: var(--b2r); }
.check figure.rounded { --b2r: 9999px; }
.check .limit, .check .limit a { color: inherit; font-weight: var(--main_fw); font-size: var(--main_fz); text-decoration: none; }
.check .wide, #root .l4cl .check .wide { width: 100%; }
#root .l4cl .check .wide:not(:last-child) { margin-bottom: 4px; }
label + .check, .label + .check { margin-top: 10px; }
/*.check label.align-middle {}*/
.check label.align-middle:before, .check label.align-middle:after { top: 50%; margin-top: calc(0px - var(--box_size) * 0.5); }
#root .check label.align-middle > i, .check label.align-middle figure { display: block; top: 0; width: var(--img_s); height: var(--img_s); margin: 5px 10px 5px 0; font-size: var(--img_s); line-height: 1; }
.check label.align-middle img { display: block; height: var(--img_s) !important; }
.check label.align-middle .size-xs { --img_s: 16px; }
.check label.align-middle .size-s { --img_s: 30px; }
.check label.align-middle .size-m { --img_s: 45px; }
.check label.align-middle .size-l { --img_s: 60px; }
.check.inline { margin-right: calc(0px - var(--dist_check)); margin-bottom: calc(var(--main_mr) - var(--dist_check_li)); --dist_check: 16px; }
.check.inline > * { margin-right: var(--dist_check); }
.check.box, .check.color { margin-right: calc(0px - var(--check_color_space)); margin-bottom: calc(var(--main_mr) - var(--check_color_space)); color: var(--custom_input_fg); }
.check.box li, #root .check.color li { width: auto !important; min-width: 0 !important; max-width: none !important; min-height: 0; margin-right: var(--check_color_space); margin-bottom: var(--check_color_space) !important; padding: 0 !important; border-width: 0 !important; }
#root .check.box label { display: block; min-width: 45px; min-height: 45px; padding: 6px 14px; text-transform: var(--btn_tt); }
#root .check.box label:before { content: ""; left: 0; right: 0; top: 0; bottom: 0; z-index: -1; width: auto; height: auto; box-shadow: none; border-radius: var(--b2r); background: var(--custom_input_bg); }
#root .check.box input[disabled] ~ label img, #root .check.wide input[disabled] ~ label ~ *, #root .check.box input.disabled ~ label img, #root .check.wide input.disabled ~ label ~ * { opacity: .35; }
#root .check.box input[disabled] ~ label img, #root .check.box input.disabled ~ label img { z-index: -2; }
#root .check.wide input[disabled] ~ label:before, #root .check.wide input.disabled ~ label:before { border-color: var(--custom_input_bd); }
#root .check.box label img, #root .check.box label picture { display: block; position: relative; z-index: 2; min-width: 33px !important; margin: 0 -6px; padding: 0; border-radius: var(--b2r); object-fit: contain; }
#root .check.box label picture { padding-top: calc(33px * var(--ratio)); }
#root .check.box label picture img { display: block; position: absolute; left: 0; right: 0; top: 0 !important; bottom: 0; width: 100% !important; height: 100% !important; margin: 0; object-fit: contain; object-position: center center; }
#root .check.box .cover label:before { z-index: 9; background: none; pointer-events: none; }
#root .check.box .cover picture { position: static; }
#root .check.box .cover .img-multiply-bg:before { border-radius: var(--b2r); }
.check.box picture + *, .check.box img + * { display: block; padding-left: 18px; }
.check.color { margin-bottom: calc(var(--main_mr) - var(--check_color_space) * 0.5); }
#root .check.color li, #root .check.color li:last-child { width: auto; margin-bottom: calc(var(--check_color_space) * 0.5); }
#root .check.color label { display: block; overflow: hidden; width: calc(var(--check_color_size) + var(--check_color_dist) * 2); height: calc(var(--check_color_size) + var(--check_color_dist) * 2); padding: 0; border-radius: 25px; border: 1px solid rgba(0,0,0,0); cursor: pointer; }
#root .check.color input:checked ~ label { border-color: var(--secondary_bg); }
#root .check.color label i { display: block; overflow: hidden; position: absolute; left: calc(var(--check_color_dist) - 1px); right: calc(var(--check_color_dist) - 1px); top: calc(var(--check_color_dist) - 1px); bottom: calc(var(--check_color_dist) - 1px); width: var(--check_color_size); height: var(--check_color_size); margin: 0; padding: 0; box-shadow: inset 0 1px 2px rgba(0,0,0,.2); border-radius: 99px; background-position: center center !important; background-size: cover !important; font-size: calc(var(--check_color_size) + 2px); line-height: calc(var(--check_color_size) + 2px); text-align: left; text-indent: -3000em; direction: ltr; }
#root .check.color label i:before { display: none; }
#root .check.color input[disabled] ~ label, #root .check.color input.disabled ~ label { border-color: var(--custom_input_bd); }
#root .check.color input[disabled] ~ label { cursor: default; pointer-events: none; }
#root .check.color input.disabled ~ label:after { cursor: default; }
#root .check.color input[disabled] ~ label:after, #root .check.color input.disabled ~ label:after {
	content: ""; display: block; position: absolute; left: -10px; top: 50%; right: -10px; width: auto; height: 1px; margin-top: -0.5px; border-top: 1px solid var(--custom_input_bd); background: none;
	transform: rotate(-45deg);
}
#root .check.color input[disabled][checked] ~ label, #root .check.color input[disabled][checked] ~ label:after, #root .check.color input.disabled[checked] ~ label, #root .check.color input.disabled[checked] ~ label:after { border-color: var(--alert_error); }
.check.color label:before, .check.color label:after, .check.color label span { display: none; }
.check.inside { --bg_s: calc(var(--main_fz) * var(--main_lh)); --bg_c: var(--custom_bd); --bg_ci: inherit; }
.check.inside label { padding-left: 0; padding-right: 0; font-size: 1em; }
.check.inside label:before, .check.inside label:after { display: none; }
.check.inside label > span { display: block; position: relative; z-index: 2; margin-right: 6px; padding: 0 6px 0 calc(var(--bg_s) + 2px); color: var(--bg_ci); font-weight: inherit; }
.check.inside label > span:before { box-shadow: 0 2px 2px rgba(0,0,0,.06); border-radius: 99px; border: 1px solid var(--custom_bd); background: var(--bg_c); }
.check.inside label > span:after { content: ""; display: block; position: absolute; left: 2px; top: 2px; z-index: 9; width: calc(var(--bg_s) - 4px); height: calc(var(--bg_s) - 4px); box-shadow: 0 1px 3px rgba(0,0,0,.15); border-radius: 99px; background: var(--white); cursor: pointer; }
.shopify-section-header .check.inside label > span:before { box-shadow: none; border-width: 0; }
.check.inside input:checked ~ label { --bg_c: var(--secondary_bg); --bg_ci: var(--secondary_text); }
.check.inside input:checked ~ label > span { padding-right: calc(var(--bg_s) + 2px); padding-left: 6px; }
.check.inside input:checked ~ label > span span:not(.hidden) { display: none; }
.check.inside input:checked ~ label > span span.hidden { display: inline; position: relative; left: 0; top: 0; }
.check.inside input:checked ~ label > span:after { left: calc(100% - var(--bg_s) + 2px); }
/*.check.switch {}*/
.check.switch label { display: block; padding: 0 55px 0 0; }
#root .check.switch label:before { content: ""; left: auto; right: 20px; top: 50%; z-index: 2; width: 22px; height: 22px; margin-top: -11px; box-shadow: 0 2px 2px rgba(0,0,0,.06); border-radius: 22px; border: 1px solid var(--custom_bd); background: var(--sand); }
#root .check.switch input:checked ~ label:before { right: 0; box-shadow: var(--btn_sh_inner); border-width: 0; border-color: var(--secondary_bg); background: var(--secondary_bg); }
#root .check.switch label:after { content: ""; left: auto; right: 0; top: 50%; z-index: 1; width: 42px; height: 18px; margin-top: -9px; border-radius: 22px; border-width: 0; border: 1px solid var(--sand); background: /*var(--sand)*/ var(--white); }
/*.check.text-end {}*/
.check.text-end label, .check label.text-end { padding-left: 0; padding-right: 26px; text-align: left; }
.check.text-end label:before, .check label.text-end:before { left: auto; right: 0; }
.check.wide { display: block; }
.check.wide:before { box-shadow: 0 2px 2px rgba(0,0,0,.06); border: 1px solid var(--custom_bd); }
.check.wide > * { display: block; min-height: 59px; margin: 0; padding: 9px 20px; border: 0 solid var(--custom_bd); border-bottom-width: 1px; }
.check.wide .s1pr { text-align: right; }

.input-amount, .input-amount input, .f8ps .input-amount input, .l4ca.compact .input-amount { width: /*50px*/ calc(var(--main_fz) * 4); flex-shrink: 0; }
.input-amount .semantic-amount { display: block; position: relative; }
#root .input-amount a[role="button"] { display: block; overflow: hidden; position: absolute; right: -10px; z-index: 9; width: calc(var(--main_fz) * 2.5); height: calc(50% + 10px); margin: 0; border: 0 solid rgba(0,0,0,0); border-right-width: 10px; /*color: var(--gray_text);*/ color: var(--custom_input_pl); font-size: calc(var(--main_fz) * 0.2857142857); text-align: left; text-indent: -3000em; direction: ltr; }
#root .input-amount .incr { top: -10px; }
#root .input-amount .decr { bottom: -10px; }
#root .input-amount .incr:before { content: "\e908"; top: auto; bottom: -1px; margin: 0; line-height: calc(var(--main_fz) * 0.6428571429); }
#root .input-amount .decr:before { content: "\e904"; top: 1px; bottom: auto; margin: 0; line-height: calc(var(--main_fz) * 0.6428571429); }
#root .input-amount .disabled[role="button"] { color: var(--custom_input_pl); opacity: 0.33; cursor: default; pointer-events: none; }
#root .input-amount input { /*padding-left: 10px;*/ padding-right: calc(var(--main_fz) * 1.4285714286); --custom_input_pl: var(--custom_input_fg); }
.input-amount.is-valid input, .is-valid .input-amount input { background-position: -3000em -3000em; }
.input-amount ~ button { align-self: stretch; }
span ~ .input-amount ~ button { align-self: center; }
.input-amount:has(input[disabled]) a[role="button"], .input-amount:has(input.disabled) a[role="button"] { opacity: .3; pointer-events: none; }
.input-amount.size-m, .input-amount.size-m input { width: calc(var(--main_fz) * 4); --input_h: calc(var(--btn_pv) * 1.2 * 2 + var(--btn_fz) * var(--btn_lh)); }
.input-amount.size-l, .input-amount.size-l input { width: calc(var(--main_fz) * 4.4285714286); --input_h: calc(var(--btn_pv) * 1.35 * 2 + var(--btn_fz) * var(--btn_lh)); }

/*.input-inline {}*/
.input-inline input {
	width: 0% !important; margin-right: 12px;
	flex-grow: 3;
}
.input-inline input:last-child { width: 100%; margin-right: 0; }
.input-inline button, .input-inline .link-btn a { min-width: 15px; /*height: 45px;*/ min-height: var(--input_h); margin-top: 0; margin-bottom: 0; margin-right: 0; /*padding-left: 0; padding-right: 0;*/ }
.input-inline .link-btn a { line-height: 45px; }
.input-inline button i, .input-inline .link-btn a i { font-size: clamp(var(--size_14_f), calc(var(--input_h) * 0.3111111111), var(--size_16_f)); }
.input-inline .link-btn { margin: 0; }

.input-prefix, .input-suffix { display: block; position: relative; z-index: 2; }
.input-prefix > span:first-child, .input-suffix > span:first-child { display: block; position: absolute; left: 0; top: 0; z-index: 9; padding: 0 4px 0 calc(var(--main_fz) * 1.1428571429); color: var(--custom_input_pl); font-size: var(--main_fz); line-height: var(--input_h); }
.input-suffix > span:first-child { left: auto; right: 0; padding-left: 4px; padding-right: 15px; }
[dir="ltr"] .input-prefix > span:first-child + input:not(input[style*="--pdi"]), [dir="rtl"] .input-suffix > span:first-child + input:not(input[style*="--pdi"]) { padding-left: calc(var(--main_fz) * 1.1428571429 * 2); }
[dir="ltr"] .input-suffix > span:first-child + input:not(input[style*="--pdi"]), [dir="rtl"] .input-prefix > span:first-child + input:not(input[style*="--pdi"]) { padding-right: calc(var(--main_fz) * 1.1428571429 * 2); }
[dir="ltr"] .input-prefix input[style*="--pdi"], [dir="rtl"] .input-suffix input[style*="--pdi"] { padding-left: var(--pdi); }
[dir="ltr"] .input-suffix input[style*="--pdi"], [dir="rtl"] .input-prefix input[style*="--pdi"] { padding-right: var(--pdi); }

.input-range { margin-top: 10px; }
.input-range.slider-is-here { margin-left: calc(0px - var(--main_fz) * 3.5714285714); }
.input-range > span { display: block; position: relative; z-index: 2; width: 50%; border-left: calc(var(--main_fz) * 3.5714285714) solid rgba(0,0,0,0); }
.input-range > span label { display: block; position: absolute; left: auto; right: 100%; top: 0; width: calc(var(--main_fz) * 3.5714285714); margin: 0; padding: 0; font-size: var(--main_fz); font-weight: var(--main_fw); line-height: var(--input_h); text-align: center; }
.input-range .range-inner { display: block; width: 100%; border-left: calc(var(--main_fz) * 3.5714285714) solid rgba(0,0,0,0); }

/*.input-show {}*/
.input-show label { display: block; margin-bottom: 14px; font-size: var(--main_fz); }
.js .input-show.toggle label, .js .form-cart .cols > aside > .input-show.toggle:first-child label { margin-bottom: 8px; }
.js .input-show label { padding-right: calc(var(--main_fz) * 1.4285714286); }
.js .input-show label:before { content: "\e908"; left: auto; font-size: calc(var(--main_fz) * 0.4285714286); }
.js .input-show.toggle label:before { transform: rotate(180deg); }
.input-show label a.toggle { top: -13px; bottom: -5px; }
.input-show ::placeholder { opacity: 1; }
.form-cart .cols > aside > .input-show:first-child label { margin-bottom: 20px; }

.has-show + input { padding-right: 45px; padding-left: calc(var(--main_fz) * 1.1428571429); background-position: -3000em -3000em; }
label a.show { display: none; overflow: hidden; position: absolute; right: 0; top: calc(100% + 4px); width: 50px; height: var(--input_h); color: var(--gray_text); font-size: var(--size_16_f); text-align: left; text-indent: -3000em; direction: ltr; }
label a.show:before { content: "\e929"; }
label a.show.show-toggle:before { content: "\e92a"; }

/*.form-cart {}*/
.form-cart aside { position: relative; z-index: 2; margin-bottom: 40px; }
.form-cart aside .l4pm { position: relative; z-index: 2; padding-bottom: 11px; }
.form-cart aside .l4pm:before { right: 22px; border-bottom: 1px solid var(--custom_bd); }
.form-cart aside .l4pm + * { margin-top: 26px; }
.form-cart aside .input-show + * { margin-top: -14px; padding-top: 22px; border-top: 1px solid var(--custom_bd); }

.f8cm:not([class*="w"], .wide) { max-width: calc(var(--main_fz) * 44.4285714286); }
.f8cm h1, .f8cm h2, .f8cm h3, .f8cm h4, .f8cm h5, .f8cm h6 { margin-bottom: var(--main_mr); }
.f8cm.wide .cols p { max-width: none; }

/* form-login */
.f8lg { max-width: 630px; margin-bottom: 40px; }
.f8lg p { max-width: 307px; }
.f8lg .double p, .f8lg .check, .f8lg .submit, .f8lg header p, .f8lg label p, .f8lg .label p { max-width: none; }
.f8lg p, .f8lg .check { margin-bottom: 14px; }
.f8lg .cols.w50 { max-width: 323px; }
.f8lg .submit { margin-top: 20px; }
.f8lg .check { margin-top: 2px; }
.link-btn + .f8lg, .submit + .f8lg { margin-top: 40px; }
/*.f8lg.compact {}*/
.f8lg.compact button { min-width: 0; }
.f8lg .check:first-child, .f8lg .submit:first-child { margin-top: 0; }
@media only screen and (max-width: 760px) {
	.f8lg { max-width: none; margin-bottom: 20px; }
	.f8lg p, .f8lg .cols.w50, .f8cm .cols p { max-width: none; }
	#root .f8lg .cols.w50 > * { width: 50%; }
	.f8lg .submit { text-align: center; }
	.f8lg button { width: 100%; min-width: 0; margin-top: 0; }
	.f8lg button + a { display: block; width: 100%; margin-top: 12px; }
	.f8lg .submit { justify-content: center; }
}

.f8nw { position: relative; z-index: 3; margin: 50px 0; padding: 52px 8px 36px; color: var(--white); }
.f8nw header, .f8nw fieldset { width: 100%; }
.f8nw > *, .f8nw fieldset > * { margin-left: 8px; margin-right: 8px; }
.f8nw > fieldset { margin-left: 0; margin-right: 0; }
.f8nw header { margin-bottom: 2px; text-align: center; }
.f8nw header p, .f8nw button { display: block; margin-left: 0; margin-right: 0; }
.f8nw p { margin-bottom: 16px; }
.f8nw p + .check { margin-top: -8px; }
.f8nw input, .f8nw .check, .f8nw .invalid-feedback { width: 303px; }
.f8nw:before { z-index: -2; background: var(--primary_text); }
.f8nw .background { overflow: hidden; z-index: -1 !important; }
.f8nw.wide { padding-left: 0; padding-right: 0; }

/*.f8pr {}*/
.f8pr > div { position: relative; }
.f8pr p[class*="overlay"] span.strong { margin-left: 2px; }
.f8pr p:not(.link-btn) a { white-space: nowrap; }
#root .f8pr p.m15 { margin-bottom: 17px; }
.f8pr p.m15 + [class*="overlay"], .l4ad p[class*="overlay"] span.strong { margin-top: -2px; }
.f8pr span.check, .f8pr span.check.box { margin-top: 0; margin-bottom: 0; }
/*.f8pr .s1pr { margin-top: -4px; margin-bottom: 20px; }*/
.f8pr .s1pr + .submit, .f8pr .s1pr + .link-btn { margin-top: 20px; }
.f8pr .submit, .f8pr .link-btn { margin-bottom: calc(var(--main_mr) - var(--btn_dist2)); }
.f8pr .m6tb > nav .link-btn a:not(.inline), .f8pr .l4cl .submit button, .f8pr .l4cl .link-btn a:not(.inline) { min-width: 0; }
#root .f8pr .l4cl .link-btn { margin-bottom: 0; }
.f8pr .submit:last-child, .f8pr .link-btn:last-child { margin-bottom: calc(var(--main_mr) - var(--btn_dist2)); }
#root .f8pr .submit.m10:last-child, #root .f8pr .link-btn.m10:last-child { margin-bottom: 12px;  }
#root .f8pr .submit.m10, #root .f8pr .link-btn.m10 { margin-bottom: 2px; }
.f8pr .submit .cols button { margin-right: 0; }
.f8pr .submit + .link-btn, .f8pr .link-btn + .submit { margin-top: -10px; }
.f8pr .submit .input-amount, .f8ps .submit .input-amount, .m6pr-compact .submit .input-amount, .submit .input-amount { position: relative; z-index: 9; margin-right: var(--f8pr_submit_dist); }
.f8pr .submit .input-amount, .f8ps .submit .input-amount, .m6pr-compact .submit .input-amount, .f8pr .submit .input-amount *, .f8ps .submit .input-amount *, .m6pr-compact .submit .input-amount *, .submit .input-amount *, #nav-user { align-self: stretch; }
.f8pr .submit .input-amount input, .f8ps .submit .input-amount input, .m6pr-compact .submit .input-amount input { height: auto; min-height: var(--input_h); }
.f8pr div.submit > div { margin-bottom: 0; align-self: stretch; }
#root .f8pr .submit .input-amount + * ~ *:not([class*="wishlist"]) { flex-basis: 100%; }
.f8pr div.submit > div * span { flex-grow: 0; }
.f8pr div.submit > div, .f8pr div.submit > button { min-width: 200px; flex-basis: 0; }
.f8pr div.submit.nowrap > div, .f8pr div.submit.nowrap > button { min-width: 0; }
.f8pr div.submit > div * { width: 100%; min-width: 0; }
/*.f8pr div.submit > div .shopify-cleanslate { height: 0; }*/
.f8pr div.submit > div button { margin-right: 0; }
.f8pr div.submit > div button * { width: auto; }

.f8ps { display: none; height: 69px; visibility: hidden; opacity: 0; }
html:not(.scrolled.product-scrolled) .f8ps { pointer-events: none; }
#root > .shopify-section .f8ps {
	visibility: hidden; opacity: 0;
	transform: translateY(20px);
}
@media only screen and (min-width: 761px) {
	#root > .shopify-section .f8ps.align-top { transform: translateY(-20px); }
}

/*.f8vl {}*/
.f8vl.submitted > fieldset, .f8vl.submitted > fieldset > footer.hidden { display: block; position: relative; left: 0; top: 0; }

/* Search/menu overlay handler */
.overlay-close, .overlay-close-clipping { display: block; visibility: hidden; overflow: hidden; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 8; text-align: left; text-indent: -3000em; direction: ltr; background: rgba(var(--primary_text_rgb), .2); opacity: 0; }
#nav > .overlay-close, #nav-bar > .overlay-close, .shopify-section-header > .overlay-close { left: 50%; right: auto; width: var(--100vw); margin-left: calc(0px - var(--100vw) * 0.5); }
.search-full:not(.no-search-overlay) #root > .overlay-close, .search-full:not(.no-search-overlay) #nav > .overlay-close, .search-full:not(.no-search-overlay) #nav-bar > .overlay-close, .user-form-active #root > .overlay-close, .user-form-active #nav > .overlay-close, .user-form-active #nav-bar > .overlay-close { visibility: visible; opacity: 1; }
.user-form-active #header-inner #nav-bar > .overlay-close { visibility: hidden; opacity: 0; }
#nav > ul > li.toggle ~ a.close, #nav-bar > ul > li.toggle ~ a.close, [data-whatintent="mouse"] #nav > ul > li.sub:hover ~ a.close, [data-whatintent="mouse"] #nav-bar > ul > li.sub:hover ~ a.close { display: block; }

.shopify-section-header > .overlay-close { z-index: 99; }

@media only screen and (min-width: 1001px) { /* 1000- */
	html.mobile:not(.m2a) #nav > ul > li > a.toggle, html.mobile:not(.m2a) #nav-bar > ul > li > a.toggle { left: 0; right: 0; top: 0; bottom: 0; width: 100%; margin: /*-44px 0 0*/ -100%; }
	html.mobile:not(.m2a) #nav > ul > li > a.toggle:before, html.mobile:not(.m2a) #nav-bar > ul > li > a.toggle:before, html.mobile:not(.m2a) #nav > ul > li.toggle > a.toggle, html.mobile:not(.m2a) #nav-bar > ul > li.toggle > a.toggle { display: none; }
}


/*! Helpers --------- */
summary::-webkit-details-marker { display: none; }
html.no-js .no-js-hidden, html:not(.no-js) .js-hidden { display: none; }
body > .locale-selectors__container, body > div > .locale-selectors__container { display: none; }
#recover { display: none; }
#recover:target { display: block; }
#recover:target ~ #login { display: none; }

.clear { clear: both; }
[data-copy].clicked > span.hidden { display: inline; position: relative; left: 0; top: 0; }
#root .desktop-hide, #root .tablet-only, #root .mobile-only:not(i), html:not(.mobile) #root select.hidden, .input-amount label.hidden, .l4ca footer a i ~ span.hidden, .input-range > span:first-child label, #header-inner > .link-btn .search-compact .hidden { position: absolute; left: 0; top: -30000em; right: auto; bottom: auto; }
#root i.mobile-only { position: absolute; left: 0; right: auto; bottom: auto; }
.hidden, [hidden], template, #search:before, #nav.hidden, #nav-bar.hidden, .shopify-section-header .l4us-mobile, .shopify-section-header #nav > .inner, #nav > ul.inner, #nav-bar > ul.inner, #nav > ul > li > a img, .shopify-section-header li:after, .shopify-section-header.fixed #nav-top > ul, .n6br li:after, .n6br li:first-child:before, #nav-top li:after, #nav li:after, #nav-top > ul > li > a span.hidden, #nav-top > ul > li > label span, /*.l4ch li:after,*/ #nav-user > ul > li:after, #nav-user > ul > li > label span, #nav-user em, #nav-top em, #search label, .shopify-section-footer a.header-toggle, .shopify-section-footer .strong a.header-toggle, .shopify-section-footer .m6cn a.header-toggle, #root .shopify-section-header #nav > .header, #nav ul ul a.toggle, #nav .toggle-back, #root #nav > ul.nav-top, #root #nav > ul.nav-user, #nav-user > ul > li > a ~ a.toggle:before, #nav .m6cn, #nav > a.close, #nav > ul > li > a.toggle-back, .shopify-section-header li.mobile-only, .shopify-section-footer li:after, .l4dr li:after, .l4sc li:after, .l4cn li:after, .shopify-section-footer label.hidden, .l4dr a span.hidden, #search button:after, .link-btn a.inline:after, .link-btn a.inline:before, .shopify-section-footer button i, .invalid-feedback, #root .mobile-only, #top legend, .shopify-section-footer legend, .cols.hidden, .js .input-show.toggle > *, .l4cl.wide figure span, .l4cl.wide .r6rt, .l4cl.wide h1 .small, .l4cl.wide h2 .small, .l4cl.wide h3 .small, .l4cl.wide h4 .small, .l4cl.wide h5 .small, .l4cl.wide h6 .small, .input-inline button i, .input-inline .link-btn a i, #root .l4cl li.link:before, .shopify-section-footer a:after, .shopify-section-header a:after, .no-mobile .select-wrapper select, .mobile .select-wrapper .bv_mainselect, #root .swiper-button-disabled, .l4pr .icon-cube, .tabs-header, #root .l4pr:not(.static) > li, #root .check.box label:after, .l4pr .swiper-outer > .label + .label, #root .check.box label:after, #root .l4ca.compact ul li:after, .shopify-section-header .invalid-feedback, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.show span span, .l4pr a.more, .l4pr .swiper-slide .label, .m6tb.compact > nav:before, .m6fr figure.full:before, .l4us-mobile .swiper-pagination-bullets, .l4us-mobile .swiper-button-nav, #root .l4pr a.more, .l4cl .info p a.hidden, .l4cl.list li:last-child:before, #root .l4cl.list .list-hide, #root .l4cl .list-only, .l4ts .rating-label, .l4ts .swiper-button-nav, .l4st .swiper-button-nav, #root .l4us-mobile, .js m6fr.wide article:before, a.link-more, .js .m6tb > div > .hidden, .m6tb > nav, f8nw button i, #root .f8nw button i, .js a.link-more.hidden, .countdown .simply-section:last-child:before, .countdown.compact .simply-word, .countdown.compact .simply-amount > span:before, #root .s1lb .invalid-feedback, .l4cl a.link-more:after, .link-more-clicked a.link-more span:not(.link-underline, .was-hidden), .js .link-more-clicked a.link-more, .check li.link-more, .n6br li.text-end:before, #root .l4cl.wide li.hidden, .l4ca li.hidden, .table-drop tr.sub, .table-drop a.toggle, .m6tb.static > nav ul li.active:before, .f8vl.submitted > *, .f8vl.submitted > fieldset > *, #root button.inline:before, .shopify-section-footer.hidden, [data-copy].clicked > span, .t1pl .shopify-section-header, #nav-bar > ul > li > a img, #nav-bar > ul > li ul a.toggle, #root .shopify-section-header #header-inner > .link-btn.hidden, #root .m6tb > nav.hidden, .m6tb > nav.hidden *, #background.plain:before, .l4pr.no-scrollbar .custom-progressbar, .n6br > p.mobile-only a:before, .swiper-custom-fraction, button .processing, button .processed, form.processing button .processing ~ *, form.processed button .processed ~ *, #root > a.cookie-close, #root .shopify-section-header #nav .l4sc, #root .link-btn.cols:after, .form-cart .link-btn.mobile-sticky, #background .mobile-only, .l4sc a span, #root .broken-img img, #root img.broken-img, [data-shopify-xr-hidden], .no-nav .swiper-button-nav, .f8pr .submit.unavailable ~ .overlay-quaternary, #root .l4pr a.hidden, #root .s1pr .invalid-feedback, #nav-top > .l4us a.linked, .l4us.slider .inner-text, .countdown.hide-days .simply-days-section, .countdown-container:not(.done), .arrows_bv, #header-outer > .overlay-close, .m2a .shopify-section-header #nav > ul > li.show-all, .f8pr.unavailable .s1pr, .f8pr.unavailable .pickup, .f8pr.unavailable .stock, .f8pr.unavailable p.submit + .overlay-quaternary, .f8pr.unavailable .no-zindex h2.label, .f8pr.unavailable .no-zindex h2.label + ul.check.inline, .f8pr.unavailable p[data-element], #header-inner > .link-btn a:after, .search-compact-cont, #distance-spacer, .l4ca .removed, #root .check:not(.limit-clicked) li.hidden-check, .js .input-range.single input, .input-range-steps, #root .l4ca .hidden, .l4cl.hr .check.color li:before, .l4cl .box-outer, .l4cl.inline-links p a:after, .btn-disabled, .disabled .btn-disabled ~ *, .l4cl img ~ img, .l4cl picture.slider:not(.s4wi) img ~ a, .l4cl picture.slider:not(.s4wi) a ~ img, .l4cn.plain i, #root > .overlay-close ~ .overlay-close, .l4pr .swiper-button-nav:not([style*="height:"]), .l4cl picture.slider > a ~ a, .l4cl figure .check.plain ~ .submit, #nav-top .mobile-nav-only, .datepicker-dropdown, .l4pr .swiper-pagination-bullet.has-more ~ .swiper-pagination-bullet, .l4cl .link-btn .was-hidden, .s1pr .hidden, .l4cl[class*="upsell"] a:after, .s1nw + br, .m6cp, #root .l4cl figure > a.remove span, input:checked ~ label span[data-checked-hide], input:not(:checked) ~ label span[data-checked-show], #root .swiper-pagination-bullets.hidden, .check .limit, .l4ca-empty, #root .link-mute, .l4hs-l li > a.toggle, .l4hs-l li > a.toggle-mobile { display: none; }
@media only screen and (min-width: 1001px){
	#root #nav.hr > ul > li > a, #root #nav-bar.hr > ul > li > a {  color: var(--custom_top_nav_fg); }
	#root #nav.hr > ul > li.active > a, #root #nav-bar.hr > ul > li.active > a { font-weight: var(--custom_top_nav_fw); }
	#root #nav.hr > ul > li.active > a:after, #root #nav-bar.hr > ul > li.active > a:after { width: var(--bd_w); }
	/*a:has(.s1bx):after { --bd_w: 0px; }*/
	a.has-s1bx:after { --bd_w: 0px; }

	#nav > ul > li.show-all, #nav-bar > ul > li.show-all { visibility: hidden; opacity: 0; }
	#nav.text-center > ul > li.show-all, #nav-bar.text-center > ul > li.show-all, #nav.text-justify > ul > li.show-all, #nav-bar.text-justify > ul > li.show-all, #nav.text-end > ul > li.show-all, #nav-bar.text-end > ul > li.show-all { position: absolute; right: 0; top: 0; }
	#nav > ul > li.temp-hidden ~ li.show-all, #nav-bar > ul > li.temp-hidden ~ li.show-all { visibility: visible; position: relative; opacity: 1; }
	#nav > ul > li.temp-hidden:not(.show-all), #nav-bar > ul > li.temp-hidden:not(.show-all), #nav > ul > li.show-all ~ li.show-all { display: none; }
	#nav > ul > li.nav-bar-element-main { display: none; }
	#nav > ul > li.was-temp-hidden:not(.show-all), #nav-bar > ul > li.was-temp-hidden:not(.show-all) { overflow: hidden; width: 0; margin-left: 0; margin-right: 0; }

	#root #header-outer li.show-all { z-index: 2; padding: 0; }
	#header-outer li.show-all:before { content: ""; display: block; position: absolute; left: 0; top: 50%; width: 100%; height: 24px; margin-top: -12px; border-radius: 99px; border: 1px solid var(--custom_top_nav_fg); opacity: .13; }
	#header-outer li.show-all:after { content: "\e994"; font-size: 3px; }
	#root #header-outer li.show-all > a { display: block; position: relative; left: 0; right: 0; top: 0; z-index: 9; width: 44px; margin: 0; background: none !important; text-indent: -3000em; }
}
.l4pr:not(.no-thumbs-mobile) .swiper-pagination-bullets .swiper-pagination-bullet:nth-child(5) ~ *, .l4cl .swiper-pagination-bullets, .img-overlay.plain, .img-overlay.empty, .img-overlay.hidden, .l4al.all-hidden, .l4pr.no-thumbs .swiper-pagination-bullets, .shopify-section-footer > nav > *:before, body .no-js { display: none; }
.visuallyhidden:not(:focus):not(:active) { overflow: hidden; position: absolute; width: 1px; height: 1px; white-space: nowrap; clip: rect(0 0 0 0); clip-path: inset(50%); }

.shopify-payment-button__button--hidden, .input-amount .invalid-feedback, #shopify-svg-symbols, style[data-shopify], script, li.search.mobile-only.hidden, .submit .input-amount ~ .invalid-feedback, .l4cl form .submit .invalid-feedback { display: none !important; }
article, aside, details, dialog, div, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary, .l4sc a, #root .input-show label, .input-amount label.hidden, .l4ca footer a i ~ span.hidden, .not-empty label a.show, label.not-empty a.show, .js .input-prefix > span:first-child, .js .input-prefix > span:first-child, li.link-more.link-more-clicked ~ li, .l4cl.list .info, #root .l4cl.list .list-only, .l4ca h1 a, .l4ca h2 a, .l4ca h3 a, .l4ca h4 a, .l4ca h5 a, .l4ca h6 a, .js #root .l4cl li.link, .js #root .l4cl.wide li.link, .m6tb > div > .hidden, .js .m6tb > nav, #root select.hidden, .check li.hidden ~ .link-more, .check li.was-hidden ~ .link-more, .l4cl .info p.link-more, #root .l4cl .info.link-more-clicked p, .link-more-clicked p.limit, .f8nw header p, #root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.has-more, .shopify-section-footer .check label, #root #logo picture img, form.processing button .processing, form.processed button .processed, .form-cart .link-btn span .strong, .no-js body .no-js, .l4cl li > a.link-more, #nav.text-justify, .shopify-section-header .l4al li:after, #root #nav-user > ul > li > a i span, .disabled .btn-disabled, #header-inner > .link-btn .search-compact .hidden, .no-js .r6rt, #nav-user > ul > li > a > span:not(.hidden), .has-anchor .anchor.hidden[id], .l4st img, .check li.hidden-check ~ .limit { display: block; }
.no-js a[data-panel], .no-js a.link-more, .l4cl .info p.link-more.hidden { display: none; }

.no-js .shopify-section-header li.search { display: block; position: relative; left: 0; top: 0; }

hr { display: block; overflow: hidden; width: 100%; height: 1px; margin: 50px 0; padding: 0; border: 0; border-top: 1px solid var(--custom_bd); background: none; font-size: 0; }
aside hr { margin: var(--main_mr) 0; }

h1 + h1, h1 + h2, h1 + h3, h1 + h4, h1 + h5, h1 + h6, h2 + h1, h2 + h2, h2 + h3, h2 + h4, h2 + h5, h2 + h6, h3 + h1, h3 + h2, h3 + h3, h3 + h4, h3 + h5, h3 + h6, h4 + h1, h4 + h2, h4 + h3, h4 + h4, h4 + h5, h4 + h6, h5 + h1, h5 + h2, h5 + h3, h5 + h4, h5 + h5, h5 + h6, h1:first-child, h2:first-child, h3:first-child, h4:first-child, h5:first-child, h6:first-child, .h1:first-child, .h2:first-child, .h3:first-child, .h4:first-child, .h5:first-child, .h6:first-child, legend:first-child, .link-btn:first-child, figure:first-child, figure.lead:first-child, .n6br + *, .shopify-section-footer > div .l4pm:first-child, .shopify-section-footer > nav > .m6cn:first-child h1, .shopify-section-footer > nav > .m6cn:first-child h2, .shopify-section-footer > nav > .m6cn:first-child h3, .shopify-section-footer > nav > .m6cn:first-child h4, .shopify-section-footer > nav > .m6cn:first-child h5, .shopify-section-footer > nav > .m6cn:first-child h6, .l4sc:first-child, .l4cl:first-child, .l4cl + h1, .l4cl + h2, .l4cl + h3, .l4cl + h4, .l4cl + h5, .l4cl + h6, .l4cl .link-btn:first-child, #root .m0 + *, .l4sc + hr, hr:first-child, .l4ne:first-child, .l4ne + *, legend + h1, legend + h2, legend + h3, legend + h4, legend + h5, legend + h6, .m6fr .link-btn:first-child, .m6wd:first-child, q:first-child, blockquote:first-child, .f8pr .check:first-child, .f8pr .check.color:first-child, .f8pr .check.box:first-child, #root .f8pr p.hidden + [class*="overlay"], .m6tb:first-child, #root .m10 + *, #root .m15 + *, #root .m20 + *, #root .m30 + *, #root .m35 + *, .accordion-a:first-child, .l4cn.box:first-child, .l4tt li.size-18:first-child, .input-range:first-child, .m6pr-compact footer:first-child, .m6pr-compact .submit:first-child, .s1lb + *, .l4cu:first-child, .l4ft:first-child, .m6as:first-child, .l4cl.hr figure + div, .m6ac:first-child, #root .m60 + *, .l4ts:first-child, .l4st:first-child, .f8nw:first-child, #root .l4ne-figure-before.l4ne:first-child, #root .m25 + *, .submit:first-child, .m6cu:first-child, .l4ft .link-btn:first-child, .f8nw p, aside hr:first-child, input[type="hidden"] + *, .recently-viewed-products, header + figure.lead, h1 + figure.lead, h2 + figure.lead, h3 + figure.lead, h4 + figure.lead, h5 + figure.lead, h6 + figure.lead, .shopify-section-footer > div > *, .accordion-a.compact:not(.cp2):first-child, .f8pr + .accordion-a, .shopify-section-header .l4ft .link-btn:first-child, .l4cl.category figure + h1, .l4cl.category figure + h2, .l4cl.category figure + h3, .l4cl.category figure + h4, .l4cl.category figure + h5, .l4cl.category figure + h6, .m6bx + h1, .m6bx + h2, .m6bx + h3, .m6bx + h4, .m6bx + h5, .m6bx + h6, #root .m65 + *, #root .m5 + *, #root .m1 + *, #root [class*="margin-"] + *, summary .h1, summary .h2, summary .h3, summary .h4, summary .h5, summary .h6, .cols + .l4cl { margin-top: 0; }
#search p, #root .m0, #root .margin-0, ul ul, ul ol, ol ol, ol ul, #root .l4dr ul, .l4cl p, .m6fr .swiper-slide article, .table-wrapper table, .m6wd > .l4ne:last-child, .m6wd > .cols:last-child, .m6wd > form:last-child, #content > .m0:last-child, #content > [id*="shopify-section"]:last-child > .m0:last-child, .l4pr.s4wi li, .l4ad .l4as li, .l4pr .li, #root .m6tb.compact .tabs-inner, .m6ac > *:first-child .l4cl, .m6ac .l4ft, .l4cl figure:first-child:last-child, .l4cl .link-overlay:first-child + figure:last-child, .link-btn.m0 > *, #content > .m6fr.wide:last-child, #content > .shopify-section:last-child > .m6fr:last-child, #content > .m6fr:last-child article, #content > .shopify-section:last-child > .m6fr:last-child article, #nav-top .l4us, #root > .shopify-section-footer, .m6pr .accordion-a:last-child, .accordion-a summary label, .accordion-a summary > .label { margin-bottom: 0; }

#root .m1, #root .margin-1 { margin-bottom: 1px; }
#root .m5, #root .margin-5 { margin-bottom: 5px; }
#root .m8, #root .margin-8 { margin-bottom: 8px; }
#root .m10, #root .margin-10 { margin-bottom: 10px; }
#root .m15, #root .margin-15 { margin-bottom: 15px; }
#root .m16, #root .margin-16 { margin-bottom: 16px; }
#root .m20, #root .margin-20 { margin-bottom: 20px; }
#root .m24, #root .margin-24 { margin-bottom: 24px; }
#root .m25, #root .margin-25 { margin-bottom: 25px; }
#root .m30, #root .margin-30 { margin-bottom: 30px; }
#root .m35, #root .margin-35 { margin-bottom: 35px; }
#root .m40, #root .margin-40 { margin-bottom: 40px; }
#root .m50, #root .margin-50 { margin-bottom: 50px; }
#root .m55, #root .margin-55 { margin-bottom: 55px; }
#root .m60, #root .margin-60 { margin-bottom: 60px; }
#root .m65, #root .margin-65 { margin-bottom: 65px; }
#root .m70, #root .margin-70 { margin-bottom: 70px; }
#root .m105, #root .margin-105 { margin-bottom: 105px; }
#root .margin-025 { margin-bottom: calc(var(--main_mr) * 0.25); }
#root .margin-half { margin-bottom: calc(var(--main_mr) * 0.5); }
#root .margin-content { margin-bottom: var(--main_mr); }
#root .margin-header { margin-bottom: var(--main_mr_h); }

#root .margin-top-0 { margin-top: 0; }

span.m5, span.m10, span.m15 { display: block; }


/*! Miscellaneous --------- */
audio, canvas, iframe, img, svg, video { border-width: 0; vertical-align: middle; }
audio, canvas, progress, video { display: inline-block; vertical-align: baseline; }
audio:not([controls]), video[autoplay]:not([muted]) { display: none; }
svg:not(:root) { overflow: hidden; }



/*! Flexbox --------- */
/* flex */ 	.l4hs.ol > li > a:before, .l4hs-l, .l4hs-l li:before, .l4st figure, .l4us, .check.inside label, .check label.align-middle, .text-end .check, .check label:before, .check label:after, .f8pr div.submit > div, .f8pr div.submit > div, .f8pr .submit .cols > *, .l4al.inline.compact, .text-center .check, .n6br p a, #content.fullheight, .l4cn, .l4ft li, .l4ca.compact li, .n6pg li.prev a:after, .spr-pagination > div > .spr-pagination-prev a:after, .n6pg li.next a:after, .spr-pagination > div > .spr-pagination-next a:after, #root .l4pr li.sticky, .m6tb > nav ul a, .m6tb > nav ul li, .s1lb > span.rect, .l4cl div.box, .l4st li, .l4ft.cols .main > div, .l4ft li.overlay .main > div, .l4ft li > .main, #root .m6fr article > figure, #nav-user > ul > li, .heading-has-image, .spr-pagination > div, .check.color, .accordion-a summary, .l4ft.cols, .l4ne .label, .shopify-payment-button, .shopify-payment-button div, #nav-top > .l4us .outer, .shopify-section-footer > nav .m6cn figure, .shopify-section-footer > nav .m6cn figure picture, #root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.has-more, #root .l4pr picture, .l4cl:not(.list) li > div + div:last-child, #root .check.box label, #content.align-center-static, .countdown .simply-amount, .l4us .swiper-slide, .slider-fraction .swiper-custom-pagination, .l4cl li:not(.link-more) > a:not(.link-more), .l4cl .li > a, /*ul.l4ch, ol.l4ch,*/ #content.align-center, .f8pr .submit .input-amount .semantic-amount, .m6pr-compact .submit .input-amount .semantic-amount, .f8pr .submit .input-amount, .m6pr-compact .submit .input-amount, #header-inner, .shopify-section-header #header, .m6fr > article:first-child, .m6fr .swiper-slide article, .f8nw, .f8nw fieldset, .m6cu, .l4pr .label, .countdown, #cookie-inner, .s1lb > span.rounded, .l4us.wide, .l4st, .l4ts, .l4cu, .swiper-pagination-bullets, .m6ac .m6pr-compact, .m6ac, .m6as > div, .m6as > figure, .m6as, .l4ft li > div, .l4ft, .s1lb, .l4cl.list li, .input-range, .l4ca.summary li, .l4cn.box, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span, #root .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet, .l4ca picture, .l4cl picture, .l4ca footer p a, .l4ad .l4cn, .check.wide > *, label span.text-end, .label span.text-end, .r6rt, .spr-starratings, .l4pr .custom-progressbar, .l4pr .swiper-pagination-bullets, .m6tb > nav ul, .m6tb > nav ol, #root .l4pr > li:first-child, .l4pr li, .l4pr li a, .check.box label, .check.box, .l4in, .m6pr, #nav > ul > li > a, #nav, #nav-bar > ul > li > a, #nav-bar, figure, #root .l4cl.wide li, .input-inline, .l4tt li, .l4ca footer, .l4ca figure, .l4ca ul, .l4ca li, .n6pg, .n6pg ul, .n6pg ol, .l4ne, .r6rt .rating, #header-inner > .link-btn, .l4cl li, .l4cl figure, .l4cl, .check.inline, .submit, #root, .shopify-section-footer > nav > .strong h1 ~ .l4sc, .shopify-section-footer > nav > .strong h2 ~ .l4sc, .shopify-section-footer > nav > .strong h3 ~ .l4sc, .shopify-section-footer > nav > .strong h4 ~ .l4sc, .shopify-section-footer > nav > .strong h5 ~ .l4sc, .shopify-section-footer > nav > .strong h6 ~ .l4sc, .l4dr li, .l4dr a:not(.inline), .l4dr, .l4pm, .l4sc, .shopify-section-footer > nav, .shopify-section-footer > div, #nav > ul > li, #nav-bar > ul > li, #nav-top > ul:first-child, #logo, #logo a, #nav > ul, #nav-bar > ul, #nav-top > ul > li > a, #nav-top, #nav-top > ul, #nav-user > ul, .n6br ol, .n6br ul, .n6br, .cols, .link-btn, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet.show:nth-child(5) ~ *, .link-btn.cols, .m6pn .f8pr .submit, #root .l4cl figure .submit.wide { display: flex; flex-wrap: wrap; }
/* flei */ 	span.s1bx, .countdown, figure, .s1bx { display: inline-flex; }

/* f:wn */	.l4hs-l, .m6as > div, .l4us, .check.inside label, .check label.align-middle, .nowrap, .l4cl.list li, .n6br p a, #content.fullheight, .l4cn:not(.box), #root .l4dr ul li a, .l4ft li, .l4ca.compact li, #root .l4pr li.sticky, .m6cu.text-center, .s1lb > span.rect, .l4cl div.box, .l4st li, .l4ft.cols .main > div, .l4ft li.overlay .main > div, .l4ft li > .main, .l4ca, .l4cl figure, #root .m6fr article > figure, #root .m6cu, #nav-user > ul > li, #header-inner > .link-btn, .heading-has-image, .accordion-a summary, #nav-top > .l4us .outer, #root .l4cl form .submit, .l4cl li > div + div:last-child, .l4cl li, #root .check.box label, .l4tt li, #nav-top > ul:first-child, #nav-top, #nav-top > ul, #content.align-center-static, .cols, .countdown .simply-amount, .l4us .swiper-slide, .slider-fraction .swiper-custom-pagination, /*.l4ch,*/ #content.align-center, #header-inner, #nav-user > ul, .shopify-section-header #header, .l4ca footer p a, .l4ca p a, .m6cu, .link-btn.single, .l4ft li > div, #cookie-bar .link-btn, #cookie-inner, .m6ac .m6pr-compact, .m6ac, .m6as, .check.wide > *, #nav > ul > li > a, #nav-bar > ul > li > a, #nav > ul, #nav-bar > ul, #root .l4cl.wide li, .input-inline, .l4ca footer, .l4ca li, .r6rt .rating, .shopify-section-footer > div, #nav-top > ul > li > a, .l4ca.summary li, .m6fr > article:first-child, .m6fr .swiper-slide article { flex-wrap: nowrap; }

/* f:dr */	.l4us.wide, #nav-top > ul, .m6fr figure, .l4cl.list li, #root .l4cl.wide li { flex-direction: row; }
/* f:dv */	.cols.inv, .m6ac.inv, .m6as.inv { flex-direction: row-reverse; }
/* f:dc */	.l4hs-l, .l4us, #content.fullheight, .l4cn:not(.box), .l4ft li, #root .l4pr li.sticky, .m6cu.text-center, .s1lb > span.rect, .l4cl div.box, .l4st li, .l4ft.cols .main > div, .l4ft li.overlay .main > div, .l4ft li > .main, .l4ca, .l4cl li > div + div:last-child, #content.align-center-static, .f8nw, .l4cl .li > a, .l4cl li:not(.link-more) > a:not(.link-more), /*.l4ch,*/ #content.align-center, .link-btn.single, .s1lb > span.rounded, .m6as > div, .l4ft li > div, .l4ft li, figure, .l4cl li, .l4cl figure, #root { flex-direction: column; }

/* f:js */	.cols.text-start, .l4ft .main > .link-btn:not(.text-end), #root .l4ft li > .content, #root .l4ft li, .l4cl.hr figure, .text-center .text-start .submit, .text-center .text-start .link-btn, .shopify-section-header .l4us .swiper-slide, .l4st.text-start, .text-start .l4st, .m6as.align-top > div, /*.l4ch,*/ .l4ft li.align-top, .link-btn.text-start, #root figure .link-btn.visible, #root figure .link-btn.visible.text-start, #root .m6fr article > .link-btn, #root .m6fr article > .link-btn.text-start, .l4ft li.align-top, .l4ft li.align-top > div { justify-content: flex-start; }
/* f:jc */	.l4hs.ol > li > a:before, .l4hs-l li:before, #root .l4ft li.align-middle > .content, .check label:before, .check label:after, .s1bx, .shopify-section-footer > nav.align-center, .l4sc.text-center, .text-center .l4sc, .l4cl.justify-center, .m6tb > nav ul.text-center, .n6pg li.prev a:after, .spr-pagination > div > .spr-pagination-prev a:after, .n6pg li.next a:after, .spr-pagination > div > .spr-pagination-next a:after, .m6tb > nav ul a, .m6cu.text-center, .s1lb > span.rect, .l4cl div.box, .l4ft.cols li:not([class*="align-"]) .main > div, .l4ft li > .main, .m6tb.btn > nav.text-center ul, .m6cu .link-btn, .n6pg .text-center .link-btn, .n6pg .text-center.link-btn, .spr-pagination > div, figure.text-center, .n6pg ol, .n6pg ul, #root .l4pr picture, .l4cl figure .submit, #content.align-center-static, .shopify-section-header .l4us.text-center .swiper-slide, .l4ft li.align-center, .l4ft li.align-center div, .l4us .swiper-slide, .l4st, .l4ts, .slider-fraction .swiper-custom-pagination, #content.align-center, #nav.text-center, #nav-bar.text-center, .text-end .text-center.link-btn, .shopify-section-header.text-center, #header.text-center, .shopify-section-header.text-center #header-inner, #header.text-center #header-inner, .text-center figure, .f8nw, .f8nw fieldset, /*.link-btn.text-center, .text-center.submit, .text-center .submit,*/ .text-center .link-btn:not(.text-start, .text-end), .l4cl.inline li, .s1lb > span.rounded , .l4us.wide, .text-center.l4cu, .text-center .l4cu, .swiper-pagination-bullets, .m6as > div, .l4ft li, .l4ft li > div, .l4cn.box li, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet, .l4ca picture, .l4cl picture, .l4pr .swiper-pagination-bullets, .l4pr li a, .check.box label, .l4pm.text-center, .text-center .l4pm, .l4ca figure, #nav.text-center > ul, #nav-bar.text-center > ul, #nav-bar.text-center > ul, .n6pg, .l4cl figure, #nav-top > ul, .l4cl figure .link-btn:not(.text-start, .text-end) { justify-content: center; }
/* f:je */	.text-end .check, #nav.text-end > ul, #nav-bar.text-end > ul, #nav.text-end, #nav-bar.text-end, #root figure .link-btn.visible.text-end, #root .m6fr article > .link-btn.text-end, .s1lb.align-end, #root .l4pr li.sticky, .shopify-section-footer > nav .m6cn figure:not(.text-start, .text-center, .inline), .shopify-section-footer > nav .m6cn figure:not(.text-start, .text-center, .inline) picture, .shopify-section-footer .l4pm, .l4st.text-end, .text-end .l4st, .text-end.l4cu, .text-end .l4cu, .m6as.align-bottom > div, .text-center .text-end.link-btn, .shopify-section-header.text-end, #header.text-end, .shopify-section-header.text-end #header-inner, #header.text-end #header-inner, /*.link-btn.text-end, .text-end .link-btn, .submit.text-end, .text-end .submit,*/ .l4ft li.align-bottom, .l4ft li.align-bottom div, .n6pg.text-end, .submit.text-end, .l4tt li { justify-content: flex-end; }
/* j:jb */	/*.link-btn.text-justify,*/ .shopify-section-footer > nav, .shopify-section-header.text-justify, #header.text-justify, .shopify-section-header.text-justify #header-inner, #header.text-justify #header-inner, .m6cu, .check.wide > *, .m6pr, #nav, #nav-bar, .l4ca li, #nav-top, .n6br, .cols, .link-btn.cols, #nav.text-justify > ul, #nav-bar.text-justify > ul { justify-content: space-between; }

/* f:as */	.l4us, .m6as.align-top, .l4ca section ul, .l4ne, /*.l4ch,*/ .l4ft.cols, .f8nw, .f8nw fieldset, .countdown, .m6ac.align-top, .m6pr, figure, .shopify-section-footer form, .shopify-section-footer fieldset, .cols.aside, .l4cl figure > span, .shopify-section-header, .l4ca figure, .l4ca picture { align-items: flex-start; }
/* f:ac */	.l4hs.ol > li > a:before, .l4hs-l.text-center, .text-center .l4hs-l, .l4hs-l li:before, .text-center.l4us, .text-center .l4us, .l4us.align-middle, .text-center .l4us, /*.l4ch .cols,*/ #nav-top .l4us.slider .swiper-wrapper, #nav-top > .l4us .longer .outer, .check.inside label, .check label.align-middle, .check label:before, .check label:after, .m6as.align-center,  .s1lb.align-center, .n6br p a, .l4cl.small, #nav-top > ul, .n6pg li.prev a:after, .spr-pagination > div > .spr-pagination-prev a:after, .n6pg li.next a:after, .spr-pagination > div > .spr-pagination-next a:after, .r6rt, .l4ca.summary li, #nav-top, .check.color, .m6tb .tabs-header, .m6tb > nav ul a, span.s1bx, #nav-user > ul, .search-compact, .heading-has-image, .spr-pagination > div, figure.text-center, .accordion-a summary, #logo a, #root .l4pr picture, #root .check.box label, .cols.align-middle, .l4ft li.text-center > div, .slider-fraction .swiper-custom-pagination, /*.text-center .l4ch, .text-center.l4ch,*/ .l4cl.s4wi.text-justify .swiper-wrapper, .check.box, .l4pr .swiper-pagination-bullets, .text-center figure, .m6cu, .s1lb > span.rounded, .l4cl.inline li, #cookie-inner, .m6ac .m6pr-compact, .m6ac, .l4cl.align-center, .s1lb, .l4cl.list li, .l4cn.box li, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet, .l4cl picture, .l4ca.compact li, .l4ca footer p a, .check.wide > *, .l4pr li, .l4pr li a, .check.box label, #nav > ul > li > a, #nav-bar > ul > li > a, #root .l4cl.wide li, .l4ca footer, .l4ca li, .n6pg ol, .n6pg ul, .n6pg, .submit, .l4dr li, .l4dr a, .shopify-section-footer > div, .l4pm, .l4sc, #logo, #logo a, #nav-top > ul > li > a, .link-btn, .submit { align-items: center; }
/* f:ae */	.l4hs-l.text-end, .text-end .l4hs-l, .text-end.l4us, .text-end .l4us, .m6as.align-bottom, .s1lb.align-end, .l4ft li.text-end > div, .m6ac.align-bottom/*, .text-end .l4ch, .text-end.l4ch,*/ { align-items: flex-end; }
/* f:aa */	.align-baseline, .l4ca.compact .cols:not([class*="align"]), h1.cols, h2.cols, h3.cols, h4.cols, h5.cols, h6.cols { align-items: baseline; }
/* f:ab */	.m6ac.align-stretch, .m6as { align-items: stretch; }

/* f:s0 */	#logo, .wishlist-whale-button-container, .m6cu .link-btn, .l4al .cols > *:has(.s1bx) { flex-shrink: 0; }
/* f:b0 */	.l4cu.box li, .m6fr figure:not(.has-l4hs) > *:not([style], [class*="width-"]) { flex-basis: 0; }
/* f:g0 */	.text-center .check label, .f8nw p.check label, #nav-top > ul.l4us, .f8pr .f8cm button, .l4cl .f8pr button, .f8pr .link-btn.text-justify button, .text-end .check label { flex-grow: 0; }
/* f:g3 */	.l4cl figure .submit.wide > *, .l4cu.box li, .l4cl figure .submit, .l4ft li > .content, #nav.text-justify > ul, #nav-bar.text-justify > ul, /*.l4cl li > div + div:last-child, .l4cl .li > div + div:last-child,*/ .shopify-payment-button div:not(.shopify-cleanslate), .shopify-payment-button__button div.shopify-cleanslate, #header-inner, .m6pr-compact .submit button, .l4cn.box li, .check label, #root .shopify-section-header > .wide, #root #header > .wide, .l4cl button.w160, .l4cl .link-btn a.w160, button.wide, .link-btn a.wide, .m6fr figure:not(.has-l4hs) > *:not([style], [class*="width-"]), .link-btn > .wide, .submit > .wide, .f8pr .submit.wide a:not(.inline), .f8pr div.submit.wide > div, .f8pr div.submit.wide > div *, .f8pr .link-btn.wide a:not(.inline), .f8pr .submit.wide button, .f8pr .link-btn.wide button, .f8pr .wide button /*, #nav-top > .l4us:first-child ~ ul:last-child*/ { flex-grow: 3; }


/*! Icons --------- */
/*@font-face { font-family: i; src: url('styles/icons/xtra.woff2') format('woff2'), url(styles/'icons/xtra.woff') format('woff'); font-display: block; }*/

[class*="icon"] { font-style: normal; }
[class*="icon-"] { font-family: i !important; speak: none; font-style: normal; font-weight: 400; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale; }
.icon-app:before { content: "\e94f"; }
.icon-arrow-down:before { content: "\e97d"; }
.icon-arrow-left:before { content: "\e950"; }
.icon-arrow-right:before { content: "\e96c"; }
.icon-arrow-up:before { content: "\e96d"; }
.icon-basket:before { content: "\e962"; }
.icon-cart-basket2:before { content: "\e98a"; }
.icon-box:before { content: "\e926"; }
.icon-cart:before { content: "\e903"; }
.icon-cart-bag:before { content: "\e963"; }
.icon-cart-bag-cube:before { content: "\e964"; }
.icon-cart-bag-rounded:before { content: "\e97c"; }
.icon-cart-bag-wide:before { content: "\e965"; }
.icon-cart-empty:before { content: "\e902"; }
.icon-cart-full:before { content: "\e966"; }
.icon-cart-wide:before { content: "\e967"; }
.icon-check:before { content: "\e901"; }
.icon-check-inside:before { content: "\e953"; }
.icon-check-light-circle:before { content: "\e977"; }
.icon-check-light-circle-inside:before { content: "\e978"; }
.icon-check-light:before { content: "\e979"; }
.icon-checkbox:before { content: "\e92c"; }
.icon-chevron-down:before { content: "\e904"; }
.icon-chevron-left:before { content: "\e907"; }
.icon-chevron-right:before { content: "\e906"; }
.icon-chevron-light-down:before { content: "\e969"; }
.icon-chevron-light-left:before { content: "\e96a"; }
.icon-chevron-light-right:before { content: "\e96b"; }
.icon-chevron-light-up:before { content: "\e968"; }
/*.icon-chevron-thin-right:before { content: "\e988"; }
	.icon-chevron-thin-up:before { content: "\e989"; }
	.icon-chevron-thin-down:before { content: "\e98a"; }
	.icon-chevron-thin-left:before { content: "\e98b"; }*/
.icon-chevron-strong-up:before { content: "\e98f"; }
.icon-chevron-strong-right:before { content: "\e990"; }
.icon-chevron-strong-left:before { content: "\e991"; }
.icon-chevron-strong-down:before { content: "\e992"; }
.icon-chevrons:before { content: "\e905"; }
.icon-chevron-up:before { content: "\e908"; }
.icon-circle:before { content: "\e94a"; }
.icon-cookie:before { content: "\e951"; }
.icon-cube:before { content: "\e935"; }
/*.icon-dark:before { content: "\e942"; }*/
.icon-discord:before { content: "\e95b"; }
.icon-document:before { content: "\e995"; }
.icon-dot:before { content: "\e998"; }
.icon-download:before { content: "\e999"; }
.icon-edit:before { content: "\e98b"; }
.icon-edit-off:before { content: "\e927"; }
.icon-ellipsis:before { content: "\e994"; }
.icon-envelope:before { content: "\e90b"; }
.icon-envelope-wide:before { content: "\e954"; }
.icon-error:before { content: "\e95f"; }
.icon-external:before { content: "\e93f"; }
.icon-eye:before { content: "\e929"; }
.icon-eye-closed:before { content: "\e92a"; }
.icon-eye-outline:before { content: "\e981"; }
.icon-facebook:before { content: "\e90a"; }
.icon-facebook-circle:before { content: "\e942"; }
.icon-filter:before { content: "\e934"; }
.icon-fullscreen:before { content: "\e900"; }
.icon-google:before { content: "\e960"; }
.icon-google-plus:before { content: "\e909"; }
.icon-heart:before { content: "\e97a"; }
.icon-heart-outline:before { content: "\e97b"; }
.icon-info:before { content: "\e95e"; }
.icon-info-circle:before { content: "\e975"; }
.icon-info-circle-inside:before { content: "\e976"; }
.icon-instagram:before { content: "\e90c"; }
.icon-kuaishou:before { content: "\e958"; }
.icon-label:before { content: "\e984"; }
.icon-line:before { content: "\e95c"; }
.icon-linkedin:before { content: "\e90f"; }
.icon-list-dot:before { content: "\e94b"; }
.icon-location:before { content: "\e96e"; }
.icon-lock:before { content: "\e987"; }
.icon-logout:before { content: "\e928"; }
.icon-menu:before { content: "\e922"; }
.icon-menu-center:before { content: "\e988"; }
.icon-menu-left:before { content: "\e97f"; }
.icon-menu-left-wide:before { content: "\e99a"; }
.icon-menu-right:before { content: "\e989"; }
.icon-menu-right-wide:before { content: "\e99b"; }
.icon-messenger:before { content: "\e90e"; }
.icon-minus:before { content: "\e90d"; }
.icon-minus-inside:before { content: "\e949"; }
.icon-minus-strong:before { content: "\e936"; }
.icon-minus-thin:before { content: "\e946"; }
.icon-moon:before { content: "\e94c"; }
.icon-odnoklassniki:before { content: "\e957"; }
.icon-paper-plane:before { content: "\e925"; }
.icon-pause:before { content: "\e986"; }
.icon-phone:before { content: "\e923"; }
.icon-pin:before { content: "\e913"; }
.icon-pinterest:before { content: "\e912"; }
.icon-play:before { content: "\e911"; }
.icon-play-pause-aligned:before { content: "\e985"; }
.icon-play-inner:before { content: "\e944"; }
.icon-play-inner2:before { content: "\e980"; }
.icon-plus:before { content: "\e910"; }
.icon-plus-inside:before { content: "\e948"; }
.icon-plus-strong:before { content: "\e920"; }
.icon-plus-thin:before { content: "\e945"; }
.icon-print:before { content: "\e93b"; }
.icon-qzone:before { content: "\e95a"; }
.icon-radio:before { content: "\e92b"; }
.icon-reddit:before { content: "\e92f"; }
.icon-refresh:before { content: "\e924"; }
.icon-ruler:before { content: "\e98d"; }
.icon-sale:before { content: "\e997"; }
.icon-sale-inside:before { content: "\e996"; }
.icon-share:before { content: "\e940"; }
.icon-shopify:before { content: "\e932"; }
.icon-size:before { content: "\e941"; }
.icon-skype:before { content: "\e914"; }
.icon-snapchat:before { content: "\e917"; }
.icon-snapchat-outline:before { content: "\e94e"; }
.icon-star:before { content: "\e933"; }
.icon-store:before { content: "\e98c"; }
.icon-success:before { content: "\e95d"; }
.icon-sun:before { content: "\e94d"; }
.icon-text-size:before { content: "\e952"; }
.icon-text-size-check:before { content: "\e97e"; }
.icon-tiktok:before { content: "\e930"; }
.icon-time:before { content: "\e982"; }
.icon-trash:before { content: "\e93c"; }
.icon-trash-openable:before { content: "\e93d"; }
.icon-trash-opened:before { content: "\e93a"; }
.icon-trustpilot:before, .icon-trustpilot-star:before { content: "\e921"; }
.icon-trustpilot-shadow:before { content: "\e93e"; }
.icon-tumblr:before { content: "\e916"; }
.icon-twitter:before { content: "\e915"; }
.icon-twitter-old:before { content: "\e993"; }
.icon-user:before { content: "\e918"; }
.icon-user-comment:before { content: "\e943"; }
.icon-user-comment-inv:before { content: "\e947"; }
.icon-shop:before { content: "\e950"; }
.icon-telegram:before { content: "\e959"; }
.icon-truck:before { content: "\e94e"; }
.icon-upload:before { content: "\e983"; }
.icon-viber:before { content: "\e955"; }
.icon-view-grid:before { content: "\e91b"; }
.icon-view-grid-outline:before, .outline .icon-view-grid:before { content: "\e92d"; }
.icon-view-list:before { content: "\e91a"; }
.icon-view-square:before { content: "\e98e"; }
.icon-view-square-outline:before, .outline .icon-view-square:before { content: "\e937"; }
.icon-vimeo:before { content: "\e919"; }
.icon-volume-off:before { content: "\e99c"; }
.icon-volume-on:before { content: "\e99d"; }
.icon-wechat:before { content: "\e931"; }
.icon-weibo:before { content: "\e92e"; }
.icon-whatsapp:before { content: "\e91c"; }
.icon-whatsapp-inside:before { content: "\e938"; }
.icon-whatsapp-overlay:before { content: "\e939"; }
/*.icon-whatsapp-strong:before { content: "\e96c"; }*/
.icon-x:before { content: "\e91f"; }
.icon-x-circle:before { content: "\e961"; }
.icon-x-light-circle:before { content: "\e96f"; }
.icon-x-light-circle-inside:before { content: "\e970"; }
.icon-x-light-circle-outline:before { content: "\e971"; }
.icon-x-light:before { content: "\e972"; }
.icon-x-small:before { content: "\e973"; }
.icon-x-thin:before { content: "\e974"; }
.icon-xing:before { content: "\e956"; }
.icon-youtube:before { content: "\e91e"; }
.icon-zoom:before { content: "\e91d"; }

.icon-text-size { display: inline-block; position: relative; z-index: 2; }

.icon-check, .icon-x, .icon-print, .icon-label { display: inline-block; position: relative; top: -.125em; margin-right: 3px; font-size: 0.7em; line-height: 1px; }
.icon-x { top: 0; }
h1 .icon-check, h2 .icon-check, h3 .icon-check, h4 .icon-check, h5 .icon-check, h6 .icon-check, h1 .icon-x, h2 .icon-x, h3 .icon-x, h4 .icon-x, h5 .icon-x, h6 .icon-x, h1 .icon-print, h2 .icon-print, h3 .icon-print, h4 .icon-print, h5 .icon-print, h6 .icon-print { top: 0; font-size: 0.625em; }
.icon-label { top: 0; font-size: .8em; }
.link-btn i.icon-check, .link-btn i.icon-x .link-btn i.icon-print { margin-right: 3px; }
.icon-chevron-left, .icon-chevron-right { display: inline; position: relative; top: -.125em; font-size: 0.5714285714em; line-height: 1px; }
.icon-chevron-left { margin-right: 5px; }
.icon-chevron-right { margin-left: 5px; }
p a .icon-chevron-right { margin-right: 15px; }
p a:last-child .icon-chevron-right, p a.last-child .icon-chevron-right { margin-right: 0; }
.icon-chevron-down, .icon-chevron-up { display: inline-block; position: relative; top: -.275em; margin-left: 3px; font-size: 0.4285714286em; line-height: 1px; }
/*i.icon-circle {}*/
.s12 i.icon-circle { top: -.2em; display: inline-block; position: relative; top: -.2em; margin-right: 4px; font-size: 6px; line-height: 1;}
.icon-play { display: inline-block; position: relative; top: .2em; margin-right: 9px; font-size: 1.4285714286em; }
.icon-print { top: .175em; font-size: 1.1428571429em; opacity: .53; }
.icon-cart-empty { display: inline-block; position: relative; top: .3em; margin-right: 15px; color: var(--gray_text); font-size: 3.**********em; line-height: 1; }
.icon-sale { position: relative; z-index: 2; }
.icon-sale:after { content: "\e996"; display: block; position: absolute; left: 0; right: 0; top: 0; }

i[class*="icon-info"] { display: inline-block; position: relative; top: calc(var(--main_fz) * 0.**********); margin-left: 3px; font-size: 1.2857142857em; line-height: 1px; }

p .size-18[class*="icon-"] { display: inline-block; position: relative; top: .15em; line-height: 1px; }
p .size-18.icon-upload[class*="icon-"] { top: .05em; }


/* Swiper, Copyright: Vladimir Kharlampidi, License: MIT, Version: 7.0.2, URL: https://github.com/nolimits4web/swiper */
.s4wi { position: relative; z-index: 1; }
.swiper, .l4st .swiper-outer { overflow: hidden; position: relative; z-index: 1; list-style: none; padding: 0; }
.swiper-vertical > .swiper-wrapper { flex-direction: column; }
.swiper-wrapper { position: relative; z-index: 1; width: 100%; height: 100%; }
.swiper-wrapper, .swiper-pagination-bullets { display: flex; flex-wrap: nowrap; }
.swiper-wrapper, .swiper-slide { -webkit-transition-property: transform; transition-property: transform; }
.swiper-wrapper { align-items: stretch; }
.s4wi:not(.l4cl) .swiper-android .swiper-slide, .s4wi:not(.l4cl) .swiper-wrapper { transform: translate3d(0px,0,0); }
/*.swiper-pointer-events { touch-action: pan-y; }
.swiper-pointer-events.swiper-vertical { touch-action: pan-x; }*/
.s4wi:not(.l4cl) .swiper-horizontal { -ms-touch-action: pan-y; touch-action: pan-y; }
/*.s4wi:not(.l4cl) .swiper-pointer-events.swiper-vertical { -ms-touch-action: pan-x; touch-action: pan-x; }*/
.swiper-slide { position: relative; width: 100%; height: 100%; }
.swiper-slide { flex-shrink: 0; }
.swiper-slide-invisible-blank {	visibility: hidden; opacity: 0; }
.swiper-autoheight, .swiper-autoheight .swiper-slide { height: auto; }
.swiper-button-lock, .swiper-pagination-lock { display: none; }
.swiper-autoheight .swiper-wrapper { align-items: flex-start; -webkit-transition-property: transform, height; transition-property: transform, height; }
.swiper-pagination-bullets { position: relative; left: 0; right: 0; bottom: 0; z-index: 9; }
.swiper-pagination-bullets { flex-wrap: wrap; justify-content: center; }
.swiper-pagination-bullet { display: block; position: relative; width: var(--dots_dist); height: var(--dots_dist); text-align: left; text-indent: -3000em; direction: ltr; cursor: pointer; pointer-events: auto; }
/*.swiper-pagination-bullet.portrait:not(.cover) img { height: auto !important; }*/
.swiper-pagination-bullet:before { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 8px; height: 8px; margin: -4px 0 0 -4px; border-radius: 8px; background: var(--gray); opacity: .53; }
.swiper-pagination-bullet.swiper-pagination-bullet-active:before {
	background: var(--secondary_bg);
	transform: scale(1.75);
}
.swiper-pagination-bullet:after { content: ""; display: block; position: absolute; left: 0; right: 0; top: -10px; bottom: -10px; z-index: 9; }
.swiper-button-nav { display: block; /*overflow: hidden;*/ position: absolute; top: 0; bottom: 0; z-index: 9; width: 27px; color: var(--gray_text); font-size: var(--size_12_f); text-align: left; text-indent: -3000em; direction: ltr; cursor: pointer; }
.swiper-button-prev { left: -10px; }
.swiper-button-next { right: -10px; }
.swiper-button-prev:before { content: "\e907"; }
.swiper-button-next:before { content: "\e906"; }


/* Global border-radius --------- */
.l4cu.box li > span:before, .m6wd .l4ts.box li:before, .m6wd .l4ts.box .r6rt .rating > * .fill, .m6wd .l4ts.wide.box.s4wi .swiper-outer, .f8nw:not(.wide):before, .f8nw:not(.wide) .background, .m6bx:before, .l4al li:before, /*button, input[type="button"], input[type="reset"], input[type="submit"], .link-btn a, button:before, .link-btn a:before,*/ .n6pg li.prev a:before, .n6pg li.next a:before, #nav-user > ul > li > a i span:before, .countdown .simply-amount > span:before, .recommendation-modal__container, .l4al li:before, .l4al li:after, .s1lb > span, #root .l4cl.list figure span.rounded, .l4cn.box li:before, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet:before, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span span:before, .l4pr .swiper-pagination-bullets .swiper-pagination-bullet span a.more:before, .l4pr .custom-progressbar, .l4sc.box:before, .check label:before, .check label:after, #root .check.box label:before, .m6ca:before, .m6ca .l4cl li:before, #root .l4dr ul, #root #nav-top > ul > li > ul, #root #nav-user > ul > li > ul, #root #nav-top > ul > li > form, .l4cl .link-btn.sticky:before, .l4cl form.sticky:before { border-radius: var(--b2r); }
.m6fr.s4wi .swiper-outer/*, .m6fr:not(.wide) article:not(.wide) > figure , #root .m6fr article.aside.has-border figure, #root .m6fr:not(.wide) figure picture*/ { border-radius: var(--b2p); }
.m6as.overlay:before, .l4ft li > .main:before, .l4ft figure, .img-overlay, .l4ca figure *, #root .cover img, .l4ft .background, .l4cl figure, .l4ne figure, #root .m6as > figure, /*#root .m6fr.s4wi .swiper-outer article.aside.has-border figure,*/ .l4ca img, .l4ca.compact img, .l4pr li img, .l4pr li picture, .l4pr li video, .l4pr li iframe, .l4cl figure picture, .l4ft .img-overlay, figure .img-overlay, picture svg { border-radius: var(--b2p); }
input, select, textarea, .bv_atual, #root .bv_mainselect input { border-radius: var(--b2i); }
::-webkit-scrollbar-thumb { border-radius: var(--b2r); }
#root .m6as.overlay > figure img, #root .m6as.no-border > figure img, #root .m6as.overlay > figure .img-overlay, #root .m6as.no-border > figure .img-overlay, .no-border, .no-border *, .l4cl.no-border img, .l4cl .no-border img, .l4pm img/*, .m6fr.wide article > figure .img-overlay, .m6fr.wide article > figure picture, #root .m6fr.s4wi figure .img-overlay, #root .m6fr.s4wi figure picture*/ { border-radius: 0; }
.shopify-payment-button div[role="button"]:after, .shopify-payment-button div[role="button"], .shopify-payment-button button { border-radius: var(--btn_br); }
@media only screen and (min-width: 761px) {
	#root .m6as.overlay:not(.wide) > figure, #root .m6as.no-border:not(.wide) > figure,
	#root .m6as.overlay:not(.wide) > figure img, #root .m6as.no-border:not(.wide) > figure img,
	#root .m6as.overlay:not(.wide) > figure picture, #root .m6as.no-border:not(.wide) > figure picture,
	#root .m6as.overlay:not(.wide) > figure video, #root .m6as.no-border:not(.wide) > figure video,
	#root .m6as.overlay:not(.wide) > figure svg, #root .m6as.no-border:not(.wide) > figure svg,
	#root .m6as.overlay:not(.wide) > figure iframe, #root .m6as.no-border:not(.wide) > figure iframe,
	#root .m6as.overlay:not(.wide) > figure .img-overlay, #root .m6as.no-border:not(.wide) > figure .img-overlay { border-top-right-radius: 0; border-bottom-right-radius: 0; border-top-left-radius: var(--b2p); border-bottom-left-radius: var(--b2p); }
	#root .m6as.overlay.inv:not(.wide) > figure, #root .m6as.no-border.inv:not(.wide) > figure,
	#root .m6as.overlay.inv:not(.wide) > figure img, #root .m6as.no-border.inv:not(.wide) > figure img,
	#root .m6as.overlay.inv:not(.wide) > figure picture, #root .m6as.no-border.inv:not(.wide) > figure picture,
	#root .m6as.overlay.inv:not(.wide) > figure video, #root .m6as.no-border.inv:not(.wide) > figure video,
	#root .m6as.overlay.inv:not(.wide) > figure svg, #root .m6as.no-border.inv:not(.wide) > figure svg,
	#root .m6as.overlay.inv:not(.wide) > figure iframe, #root .m6as.no-border.inv:not(.wide) > figure iframe,
	#root .m6as.overlay.inv:not(.wide) > figure .img-overlay, #root .m6as.no-border.inv:not(.wide) > figure .img-overlay { border-top-left-radius: 0; border-bottom-left-radius: 0; border-top-right-radius: var(--b2p); border-bottom-right-radius: var(--b2p); }
	/*#root .m6fr article.aside.inv figure,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure picture,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure img,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure iframe,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure video,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure svg,
	#root .m6fr:not(.wide, .s4wi) article.aside.inv figure .img-overlay { border-top-left-radius: var(--b2p); border-bottom-left-radius: var(--b2p); border-top-right-radius: 0; border-bottom-right-radius: 0; }*/
}
@media only screen and (max-width: 760px) {
	/*#root .m6fr article.aside figure, #root .m6fr article.aside figure picture, #root .m6fr article.aside figure .img-overlay { border-top-left-radius: 0; border-bottom-left-radius: 0; }*/
	#root .m6as.overlay > figure, #root .m6as.no-border > figure
	#root .m6as.overlay > figure img, #root .m6as.no-border > figure img,
	#root .m6as.overlay > figure picture, #root .m6as.no-border > figure picture,
	#root .m6as.overlay > figure video, #root .m6as.no-border > figure video,
	#root .m6as.overlay > figure svg, #root .m6as.no-border > figure svg,
	#root .m6as.overlay > figure iframe, #root .m6as.no-border > figure iframe,
	#root .m6as.overlay > figure .img-overlay { border-bottom-right-radius: 0; border-bottom-left-radius: 0; border-top-right-radius: var(--b2p); border-top-left-radius: var(--b2p); }
	#root .m6as.overlay > figure picture { overflow: hidden; }
	#root .l4ft.outer-radius:not(.fullwidth, .mobile-compact) { overflow: hidden; border-radius: var(--b2p); }
	#root .l4ft.outer-radius:not(.fullwidth, .mobile-compact) { --dist_a: 0px; }
	#root .l4ft[style*="dist_a: 0"] figure, .l4ft.outer-radius:not(.mobile-compact) figure, #root .l4ft[style*="dist_a: 0"] li:before, .l4ft.outer-radius:not(.mobile-compact) .main:before, #root .l4ft[style*="dist_a: 0"] li:after, .l4ft.outer-radius:not(.mobile-compact) li:after, .l4ft.outer-radius.fullwidth:not(.mobile-compact) { border-radius: 0; --b2p: 0px; }
	#root .l4ft.outer-radius:not(.mobile-compact) .background, #root .l4ft.outer-radius:not(.mobile-compact) .img-overlay, #root .l4ft.outer-radius:not(.mobile-compact) figure, #root .l4ft.outer-radius:not(.mobile-compact) .main:before { border-radius: 0; }
	#root .l4ft.outer-radius:not(.mobile-compact) > li:last-child { margin-bottom: 0 !important; }
}
#search > p, #search fieldset > p { z-index: 10 !important; }


/* Responsive --------- */
@media only screen and (min-width: 1357px) {
	#content > h1:first-child, #content > h2:first-child, #content > h3:first-child, #content > h4:first-child, #content > h5:first-child, #content > h6:first-child, #content > .shopify-section:first-child > h1:first-child, #content > .shopify-section:first-child > h2:first-child, #content > .shopify-section:first-child > h3:first-child, #content > .shopify-section:first-child > h4:first-child, #content > .shopify-section:first-child > h5:first-child, #content > .shopify-section:first-child > h6:first-child { margin-top: 25px; }
}
@media only screen and (max-width: 1356px) {
	.cols.b50 { --cols: 24px; }
	.w720 .cols.b50 { --cols: 50px; }
	/*.m6fr.wide {}*/
	.m6fr.size-m article, .m6fr article.size-m { --mih: 37.8571428571vw; }
	.m6fr.size-l article, .m6fr article.size-l { --mih: 50.76628352vw; }
	.m6fr.wide article, .m6fr.wide .swiper-slide article, #root .m6fr.wide article.aside { max-width: none; margin-left: 0; margin-right: 0; }
	.m6fr.wide .swiper-button-nav { width: 30px; }
	.m6fr.wide .swiper-button-prev { left: 0; margin-left: 0; }
	.m6fr.wide .swiper-button-next { right: 0; margin-right: 0; }
	.m6fr.wide > article.aside figure { right: var(--rpn) }
	.m6fr.wide > article.aside.inv figure { left: var(--rpn) }
	.m6fr .play-pause:before { left: var(--rpp); text-align: left; }
	/*.n6br {}*/
	.w940 .n6br { margin-left: 0; margin-right: 0; }
}
@media only screen and (min-width: 761px) and (max-width: 1356px) {
	/*.l4ft {}*/
	.l4ft li.w12, .l4ft li.w15, .l4ft li.w16, .l4ft li.w20, .l4ft li.w25, .l4ft.w12 li, .l4ft.w14 li, .l4ft.w16 li, .l4ft.w20 li, .l4ft.w25 li { --mih: 16vw; }
}
@media only screen and (max-width: 1300px) {
	:root {
		--rpp: 20px;
		--rpn: -20px;

		--size_70_f: 5.46875vw;
	}

	#nav.fixed { left: var(--rpp); right: var(--rpp); }
	#nav-top > ul.l4us, #nav-top > ul { margin-right: -20px; }
	#nav-top > ul.l4us li, #nav-top > ul > li { margin-right: 20px; }
	#nav-top .l4us.slider { margin-right: 20px; }
	#nav-top > ul.l4us ~ ul:not(.l4us) { padding-left: 20px; }

	.m6fr.wide .swiper-button-nav { display: none; }
}
@media only screen and (max-width: 1200px) {
	/*.shopify-section-header {}*/
	#logo, #header-inner > .link-btn, #search.text-start, .shopify-section-header .link-btn ~ #search.text-start, #root .shopify-section-header .link-btn ~ #search.text-start { margin-right: 24px; }
	#nav > ul, #nav-bar > ul { font-size: var(--main_fz); }
	/*#cookie {}*/
	#cookie-bar .link-btn { --btn_dist: 16px; }

	.cols.b75 { --cols: 24px; }
	.l4cl.list { --pr_dist: var(--rpp); --img_dist: var(--rpp); }
	.l4ne.featured { padding-left: calc(50% + 8px); }
	.l4ne.featured li:first-child { left: -100%; width: 100%; }
	.l4pm, .shopify-section-footer > div .l4pm { --dist: 22px; }
	.l4st, .l4ts, .m6ac { margin-left: -16px; }
	.l4st li, .l4ts li, .m6ac > * { border-left-width: 16px; }
	/*.m6ac {}*/
	#root .m6ac .l4cl.hr { left: 0; margin-right: 0; }
	.m6pr { --cols: 24px; }

	#cookie-bar .icon-cookie { display: none; }
}
@media only screen and (max-width: 1100px) {
	/*.shopify-section-header {}*/
	#nav-user > ul, #nav-top > ul, #nav-top > ul.l4us { margin-right: -16px; }
	#nav-user > ul > li, #nav-top > ul > li, #nav-top > ul.l4us li { margin-right: 16px; }
	#nav-top > ul.l4us ~ ul:not(.l4us) { padding-left: 16px; }
	#nav-user > ul > li > a { overflow: visible; }
	#nav-user > ul > li > a:after {
		content: ""; display: block; position: absolute; left: 50%; top: 50%; z-index: 9; width: 44px; height: 44px; margin: -22px 0 0 -22px;
		transform: none;
	}
	/*.shopify-section-footer {}*/
	.shopify-section-footer > nav { margin-left: -30px; padding-bottom: 16px; }
	.shopify-section-footer > nav > * { border-left-width: 30px; }
	.shopify-section-footer form { width: 100%; max-width: none; }
	.shopify-section-footer input { max-width: calc(var(--main_fz) * 20); }
	.shopify-section-footer > div p { margin-right: 24px; }

	label .text-end i, .label .text-end i { margin-left: 8px; }
	.l4ca { --img_d: 16px; --img_w: 90px; }
	.l4cn.box { margin-right: -16px; }
	.l4cn.box li { margin-right: 16px; margin-bottom: 16px; }
	.l4dr, .l4sc { --dist: 24px; }
	.l4ft { --ps: 30px; --pt: var(--ps); }
	.l4ft.w25, .l4ft .w25, .l4ft.w20, .l4ft .w20, .l4ft.w16, .l4ft .w16, .l4ft.w14, .l4ft .w14, .l4ft.w12, .l4ft .w12 { --ps: 20px; }
	.l4ft li.align-bottom { --pb: 12px; }
	/*.l4pr {}*/
	.l4pr .swiper-button-nav { width: 47px; }
	/*.m6pr {}*/
	.m6pr .l4pr.s4wi { border-left-width: 0; border-right-width: 28px; }
	.m6pr .l4pr .swiper-button-prev { width: var(--rpp); }
	.m6pr .l4pr.s4wi:not(.slider-fraction) > .s1lb { left: var(--label_dist); }

	#nav-user > ul > li:has(>a:first-child>i):not(.currency, .cart) > a span { display: none; }
}
@media only screen and (min-width: 1001px) {
	html[dir="ltr"]:not(.m2a) #nav > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle), [dir="ltr"] #nav-bar > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle), html[dir="ltr"]:not(.m2a) #nav > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle), [dir="ltr"] #nav-bar > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle) { padding-right: 16px; }
	html[dir="rtl"]:not(.m2a) #nav > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle), [dir="rtl"] #nav-bar > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle), html[dir="rtl"]:not(.m2a) #nav > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle), [dir="rtl"] #nav-bar > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle) { padding-left: 16px; }
	html:not(.m2a) #nav > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle):before, #nav-bar > ul > li.sub:not(.show-all, .no-arrow) > a:not(.toggle):before, html:not(.m2a) #nav > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle):before, #nav-bar > ul > li.sub-static:not(.show-all, .no-arrow) > a:not(.toggle):before { content: "\e904"; left: var(--lar0); right: var(--l0ra); width: auto; font-size: 0.4285714286em; }

	.js .m6cu:not(.text-center) .countdown:not(.compact) .simply-word { position: absolute; left: 0; right: 0; top: 100%;  }
	.l4cl.list li > div [class*="overlay-"], .l4cl.list .s1pr { white-space: nowrap; }

	.shopify-section-footer > nav.text-center > * { min-width: 20%; }

	#nav > .has-img, #root .m6pr > .desktop-hide, #nav-user > ul > li.desktop-hide, #nav > li.has-img { display: none; }
}
@media only screen and (max-width: 1000px) {
	:root {
		--rpp: 16px;
		--rpn: -16px;

		--content_p: 25px;
		--logo_h: var(--logo_h_m);
		--pager_w: 55px;
		--pager_p: 6px;
	}

	#root { overflow: hidden; }
	.shopify-section-header { --dist_main: 16px; }
	.shopify-section-header #header { width: calc(100% + 15px); margin-right: -15px; }
	.shopify-section-header #header > #distance-counter { display: none; }
	#logo { margin-right: 15px; font-size: var(--size_18); }
	#header-outer > .overlay-close { display: block; z-index: 100; left: var(--rpn); right: var(--rpn); }
	#search.text-start, .shopify-section-header .link-btn ~ #search.text-start, #root .shopify-section-header .link-btn ~ #search.text-start { margin-right: 15px; }
	#root .shopify-section-header #nav {
		display: block; overflow-x: hidden; overflow-y: auto; visibility: hidden; position: fixed; left: 0; right: auto; top: 0; bottom: 0; z-index: 9999; width: 100%; max-width: 360px; height: 100%; margin: 0; padding: 48px 0 0; box-shadow: none; border-radius: 0; background: var(--custom_drop_nav_head_bg); opacity: 0;
		transform: translateX(-10px);
		align-self: stretch;
	}
	html:not(.m2a) #root .shopify-section-header #nav { pointer-events: none; }
	/* NAV_TABLET_EXT */
	#nav.fixed { left: var(--rpp); right: var(--rpp); }
	#nav-top > ul > li > a i.icon-text-size { font-size: 1.1666666667em; }
	#nav-top > ul, #nav-top > ul.l4us { margin-right: -10px; }
	#nav-top > ul { margin-right: -10px; }
	#nav-top > ul > li, #nav-top > ul.l4us li { margin-right: 10px; }
	#nav-top .l4us.slider { margin-right: 0; margin-left: 0; }
	#nav-top > ul.l4us ~ ul:not(.l4us) { padding-left: 10px; }
	#nav-top > ul > li > a i.icon-trustpilot { top: -1px; }
	#nav-top > ul.l4us li { padding-left: 16px; }
	#nav-top > ul.l4us li.no-checks, #nav-top > ul.l4us.no-checks li { padding-left: 0; padding-right: 0; }
	#root #nav-top > ul.l4us > li.swiper-wrapper { margin: 0; padding: 0; border-width: 0; }
	#root #nav-top > ul.l4us > li.swiper-wrapper ul.swiper-slide { display: block; position: relative; left: 0; right: 0; top: 0; width: auto; height: auto; margin: 0; padding: 0; box-shadow: none; border-radius: 0; background: none; color: inherit; font-size: 1em; line-height: inherit; white-space: normal; }
	#root .shopify-section-header #nav > ul.nav-top > li.sub > a.toggle { overflow: visible; }
	.shopify-section-header .l4us.s4wi .swiper-button-nav { width: var(--rpp); }
	#nav-top > ul:nth-child(2):nth-last-child(2), #nav-top > ul:nth-child(2):nth-last-child(3) { position: relative; }
	#nav-user { font-size: var(--main_fz); }
	#nav-user > ul { font-size: 0.9285714286em; }
	#nav > a.close { display: block; position: absolute; right: 0; top: 0; z-index: 9; width: 46px; height: 48px !important; color: var(--custom_top_main_fg); font-size: 12px; text-indent: -3000em; text-align: left; direction: ltr; }
	#nav > a.close:before { content: "\e91f"; padding-right: var(--rpp); text-align: right; }
	#root .shopify-section-header #nav-top > ul.text-start, #root .shopify-section-header #nav-top > ul.text-end, #root .shopify-section-header #nav-top > ul.text-center {
		position: relative; left: 0; right: auto; top: 0; margin-right: 0; margin-left: 0;
		transform: none;
	}
	#root #nav-bar { display: none; position: fixed; left: 0; right: 0; bottom: 0; top: auto; }
	#search, #root #search { position: relative; left: 0; right: 0; top: 0; z-index: 12; float: right; width: auto; }
	#header-inner > .link-btn {
		position: relative; z-index: 11; height: 45px;
		flex-shrink: 0;
	}
	#header-inner > .link-btn a { min-height: 0; }
	#header-inner > .link-btn a:first-child { display: block; overflow: visible; min-width: 0; width: 23px; height: auto; min-width: 0; margin: 0; padding: 0; box-shadow: none; border-radius: 0; background: none; text-indent: -3000em; text-align: left; direction: ltr; }
	#header-inner > .link-btn a:first-child i { display: none; }
	.shopify-section-header #header-inner > .link-btn a:first-child:before { content: ""; display: block; position: absolute; left: 50%; top: 50%; width: 44px; height: 44px; margin: -22px 0 0 -32px; box-shadow: none; border-radius: 0; border-width: 0; background: none; }
	#root #header-inner > .link-btn a:first-child { color: var(--custom_top_main_fg); }
	#root #header-inner > .link-btn a:first-child:before { box-shadow: none; border-width: 0; background: none; transition: none; }
	html[dir="rtl"] .shopify-section-header #header-inner > .link-btn a:first-child:before { margin-left: -12px; }
	.shopify-section-header #header-inner > .link-btn a:first-child:after { content: "\e97f"; display: block; font-size: var(--size_14_f); text-align: var(--text_align_start); }
	.shopify-section-header #header-inner > .link-btn a.mobile-wide:first-child:after { content: "\e99a"; }
	.shopify-section-header #header-inner > .link-btn a.text-center:first-child:after { content: "\e988"; }
	.shopify-section-header #header-inner > .link-btn a.text-justify:first-child:after { content: "\e922"; }
	.shopify-section-header #header-inner > .link-btn a.text-end:first-child:after { content: "\e989"; }
	.shopify-section-header #header-inner > .link-btn a.text-end.mobile-wide:first-child:after { content: "\e99b"; }
	.shopify-section-header > .link-btn a:before, #header > .link-btn a:before, #header-inner > .link-btn a:before { display: none; }
	#header-inner > .link-btn.hidden { display: block; }
	#header > .close { display: block; visibility: hidden; position: fixed; top: 0; bottom: 0; z-index: 100; height: auto; opacity: 0; }
	/*#content {}*/
	.js.t1cl #content { position: static; z-index: auto; }
	/*.shopify-section-footer {}*/
	.shopify-section-footer > div p { max-width: 30%; }
	.shopify-section-footer > nav { position: relative; z-index: 2; margin-left: -20px; }
	#root .shopify-section-footer > nav > *, .shopify-section-footer > nav > .m6cn { width: auto; min-width: 0; max-width: none; margin-left: 0; margin-right: 0; border-left-width: 20px; }
	#root .shopify-section-footer > nav:not(.align-center) > * { flex-grow: 3; }

	/*.accordion-a {}*/
	.accordion-a details { padding-left: var(--rpp); padding-right: 50px; }
	.accordion-a summary { margin-left: var(--rpn); margin-right: -50px; padding-right: 50px; padding-left: var(--rpp); }
	.accordion-a summary:before { right: var(--rpp); width: auto; font-size: var(--size_18_f); }
	/*.cols {}*/
	.cols > .w40 { width: 45%; }
	.cols > .w60 { width: 55%; }
	.cols > .w64 { width: 55%; }
	.cols > .w36 { width: 45%; }
	.cols > .t10 { width: 10%; max-width: none; }
	.cols > .t15 { width: 15%; max-width: none; }
	.cols > .t20 { width: 20%; max-width: none; }
	.cols > .t25 { width: 25%; max-width: none; }
	.cols > .t30 { width: 30%; max-width: none; }
	.cols > .t33 { width: 33.33333333333%; max-width: none; }
	.cols > .t35 { width: 35%; max-width: none; }
	.cols > .t36 { width: 36%; max-width: none; }
	.cols > .t40 { width: 40%; max-width: none; }
	.cols > .t42 { width: 42%; max-width: none; }
	.cols > .t45 { width: 45%; max-width: none; }
	.cols > .t50 { width: 50%; max-width: none; }
	.cols > .t55 { width: 55%; max-width: none; }
	.cols > .t58 { width: 58%; max-width: none; }
	.cols > .t60 { width: 60%; max-width: none; }
	.cols > .t64 { width: 64%; max-width: none; }
	.cols > .t65 { width: 65%; max-width: none; }
	.cols > .t66 { width: 66.66666666666%; max-width: none; }
	.cols > .t70 { width: 70%; max-width: none; }
	.cols > .t75 { width: 75%; max-width: none; }
	.cols > .t80 { width: 80%; max-width: none; }
	.cols > .t85 { width: 85%; max-width: none; }
	.cols > .t90 { width: 90%; max-width: none; }
	.cols > .t95 { width: 95%; max-width: none; }
	.cols > .t100 { width: 100%; max-width: none; }
	.cols .f8cm .cols > * { width: 100%; }
	.cols.b30 { --cols: 30px; }
	.cols.b50 { --cols: 20px; }
	.cols.aside.b50 { --aside: 280px; }
	.js .countdown { --w: 28px; --h: 37px; --dist2: 18px; --fz: 18px; }
	label .text-end i, .label .text-end i { margin-left: 22px; }
	/*.input-inline {}*/
	.input-inline button, .input-inline .link-btn a { width: var(--input_h); min-width: var(--input_h); max-width: var(--input_h); text-indent: -3000em; text-align: left; direction: ltr; }
	.l4as.caption { padding-top: 72px; padding-left: 0; padding-right: 0; }
	.l4ca { --img_w: 80px; }
	.l4ca li > *:first-child, .l4ca li > footer { padding-left: 0; }
	.l4ca h1 .small, .l4ca h2 .small, .l4ca h3 .small, .l4ca h4 .small, .l4ca h5 .small, .l4ca h6 .small { margin-bottom: 3px; }
	.l4ca figure, .l4ca.summary figure { left: 0; }
	/*.l4ca.summary {}*/
	.l4ca.summary h1, .l4ca.summary h2, .l4ca.summary h3, .l4ca.summary h4, .l4ca.summary h5, .l4ca.summary h6 { font-size: var(--main_fz); }
	/*.l4cl {}*/
	.l4cl li, .l4cl.w16 li, .l4cl.w20 li, .l4cl.w25 li, .l4cl li.w16, .l4cl li.w20, .l4cl li.w25, .l4cl li, .l4cl.w16 li, .l4cl.w20 li, .l4cl li.w16, .l4cl li.w20 { width: 33.3333333%; }
	.l4cl li.w12, .l4cl.w12 li, .l4cl.w12 li { width: 25%; }
	.l4cl li.w14, .l4cl.w14 li, .l4cl.w14 li { width: 25%; }
	.l4cl li.w16, .l4cl.w16 li, .l4cl.w16 li { width: 25%; }
	.l4cl li.w20, .l4cl.w20 li, .l4cl.w20 li { width: 33.3333333%; }
	.l4cl li.w25, .l4cl.w25 li, .l4cl.w25 li { width: 33.3333333%; }
	.l4cl li.w33, .l4cl.w33 li, .l4cl.w33 li { width: 50%; }
	.l4cl li.w50, .l4cl.w50 li, .l4cl.w50 li { width: 50%; }
	.l4cl li.w66, .l4cl.w66 li, .l4cl.w66 li { width: 50%; }
	.l4cl.inline.w16 li { width: 16.6666666666%; }
	.l4cl .link-btn { visibility: visible; position: relative; top: 0; margin-top: auto; opacity: 1; transform: none; }
	.l4cl figure .text-overlay { font-size: var(--size_24_f); }
	/*.l4cl.list {}*/
	.l4cl.list li > div:not(:last-child) { width: calc(100% - var(--img_w) - var(--img_dist)); min-width: calc(100% - var(--img_w) - var(--img_dist)); }
	.l4cl.mobile-compact {
		overflow-x: auto; overflow-y: hidden; margin-top: 0; margin-left: var(--rpn); margin-right: var(--rpn); --li_w: 320px; --li_b: var(--dist_a);
		flex-wrap: nowrap;
	}
	.l4cl.mobile-compact.inline { justify-content: space-between; }
	#root .l4cl.mobile-compact li {
		width: var(--li_w); border-left-width: var(--li_b);
		flex-shrink: 0;
	}
	#root .l4cl.mobile-compact.inline li, #root .inline.l4cl.mobile-compact li:last-child { width: auto; min-width: auto; max-width: none; }
	#root .l4cl.mobile-compact.s4wi .li { border-left-width: var(--li_b); }
	[dir="ltr"] #root .l4cl.mobile-compact > li:first-child { width: calc(var(--li_w) - var(--li_b) + var(--rpp)); border-left-width: var(--rpp); }
	[dir="ltr"] #root .l4cl.mobile-compact > li:last-child { width: calc(var(--li_w) + var(--rpp)); border-right-width: var(--rpp); }
	.l4cl.mobile-compact .link-btn a { position: relative; left: 0; min-width: 0; }
	.l4cl.mobile-compact + p:last-child { margin-bottom: 25px; }
	.l4cl.mobile-compact .info { overflow: hidden; z-index: 1; max-height: 54px; }
	.l4cl.mobile-compact + .n6pg { margin-top: -17px; }
	.l4cl.mobile-compact + .link-btn { margin-top: 0; }
	.l4cl.mobile-compact.s4wi .swiper-wrapper {
		height: auto !important;
		transform: none !important;
	}
	.l4cl.mobile-compact.s4wi .swiper-slide { width: auto !important; margin: 0 !important; }
	#root .l4cl.mobile-compact .swiper-slide-duplicate, #root .l4cl.mobile-compact .swiper-button-nav { display: none; }
	/*.l4cl .swiper-outer { -ms-touch-action: auto; touch-action: auto; }*/
	.l4cl.hr.mobile-compact { --li_b: 33px; }
	#root .l4cl.hr.mobile-compact li { min-height: calc(var(--img_w) * var(--ratio)); padding-top: 0; }
	#root .l4cl.hr.mobile-compact .check li { min-height: 0; }
	#root .l4cl.hr.mobile-compact figure { top: 0; bottom: 0; }
	.l4cl.hr.mobile-compact li:before { left: calc(0px - var(--li_b) / 2); border-width: 0; border-left-width: 1px; }
	.l4cl.hr.l4cl-banner.mobile-compact { margin-top: -10px; padding-top: 10px; padding-right: calc(var(--rpp) + var(--pd) * 0.5); }
	#root .l4cl.hr.l4cl-banner.mobile-compact > li { border-left-width: var(--pdd); }
	#root .l4cl.hr.l4cl-banner.mobile-compact > li:has(figure) { --li_w: calc(300px + var(--img_w)); }
	#root .l4cl.hr.l4cl-banner.mobile-compact > li:before { display: block; left: 0; right: calc(0px - var(--pd)); border-width: 0; }
	#root .l4cl.hr.l4cl-banner.mobile-compact li:last-child { margin-bottom: var(--pdd); }
	.l4cn.box { margin-top: 0; }
	.l4dr, .l4sc, .l4pm, .shopify-section-footer > div .l4pm { --dist: 14px; }
	.shopify-section-footer > div p { margin-right: 14px; }
	.l4ft { --ps: 20px; }
	.l4ft + p, .l4ne + p { margin-top: -6px; }
	.l4ft li, .l4ft li.w20, .l4ft li.w16, .l4ft li.w14, .l4ft li.w12, .l4ft li.w25, .l4ft.w20 li, .l4ft.w12 li, .l4ft.w14 li, .l4ft.w16 li, .l4ft.w25 li { min-height: 30vw; }
	.l4ft li.w33 h1 + p, .l4ft li.w33 h2 + p, .l4ft li.w33 h3 + p, .l4ft li.w33 h4 + p, .l4ft li.w33 h5 + p, .l4ft li.w33 h6 + p, .l4ft li.w37 h1 + p, .l4ft li.w37 h2 + p, .l4ft li.w37 h3 + p, .l4ft li.w37 h4 + p, .l4ft li.w37 h5 + p, .l4ft li.w37 h6 + p { margin-top: 4px; }
	.l4ft.cols.w50 { padding-top: 0; }
	.l4ft.cols.w50 li:first-child { margin-top: 0; }
	.l4ft.mobile-compact { overflow-x: auto !important; overflow-y: hidden !important; position: relative !important; height: auto !important; margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: 16px; --li_w: 314px; }
	.l4ft.mobile-compact, .l4ft.mobile-compact .swiper-wrapper { display: flex; flex-wrap: nowrap; }
	#root .l4ft.mobile-compact li {
		position: relative !important; left: 0 !important; right: 0 !important; top: 0 !important; width: var(--li_w) !important; height: auto; min-height: var(--mih); margin-left: 0 !important; margin-right: 0 !important; padding: 0; border: 0 solid rgba(0,0,0,0); border-left-width: var(--dist_a); border-right-width: 0;
		flex-shrink: 0;
	}
	[dir="ltr"] #root .l4ft.mobile-compact > li:first-child { width: calc(var(--li_w) - var(--dist_a) + var(--rpp)) !important; border-left-width: var(--rpp); }
	[dir="ltr"] #root .l4ft.mobile-compact > li:last-child { width: calc(var(--li_w) + var(--rpp)) !important; border-right-width: var(--rpp); }
	#root .l4ft.mobile-compact > li:first-child:last-child, #root .l4ft.mobile-compact > .swiper-slide:first-child:last-child { flex-grow: 3; }
	[dir="ltr"] #root .l4ft.mobile-compact .swiper-slide:first-child { margin-left: calc(var(--rpp) - var(--dist_a)) !important; }
	[dir="ltr"] #root .l4ft.mobile-compact .swiper-slide:last-child { margin-right: var(--rpp) !important; }
	[dir="ltr"] #root .l4ft.mobile-compact > li.empty:last-child { display: block; width: var(--rpp) !important; border-left-width: 0; }
	/*#root #content .l4ft.mobile-compact.fullwidth > li, #root#content  .l4ft.mobile-compact.fullwidth .swiper-slide { width: var(--li_w) !important; border-left-width: 0; border-right-width: 0; }*/
	#root .l4ft.mobile-compact li.w33, #root .l4ft.mobile-compact.w33 li, #root .l4ft.mobile-compact li.w50, #root .l4ft.mobile-compact.w50 li { width: var(--li_w) !important; }
	#root .l4ft.mobile-compact li { transform: none !important; }
	.l4ft.mobile-compact { --pt: 28px; --pb: 17px; --ps: var(--rpp); }
	.l4ft.mobile-compact li > figure a, .l4ft.mobile-compact li > picture a { display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; }
	.l4ft.mobile-compact .swiper-outer, .l4ft.mobile-compact .swiper-slide { display: block !important; overflow: visible !important; width: auto !important; height: auto !important; margin: 0 !important; transform: none !important; }
	/*.l4ft.mobile-compact[style*="--dist_a: 0"] {}*/
	.l4ft.mobile-compact[style*="--dist_a: 0"] > li, .l4ft.mobile-compact[style*="--dist_a: 0"] .swiper-slide, .l4ft.mobile-compact[style*="--dist_a:0"] > li, .l4ft.mobile-compact[style*="--dist_a:0"] .swiper-slide { --rpp: 0px; }
	.l4in.slider {
		overflow-x: auto; overflow-y: hidden; margin-right: 0; white-space: nowrap;
		flex-wrap: nowrap;
	}
	.l4in.slider li { margin-right: 0; border: 0 solid rgba(0,0,0,0); border-left-width: 24px; }
	.l4in.slider li:first-child { border-width: 0; }
	/*.l4pr {}*/
	.l4pr .swiper-pagination-bullets { margin-top: 20px; }
	.l4pr .swiper-pagination-bullets .swiper-pagination-bullet { padding: 6px; }
	/*.l4st {}*/
	.l4st:not([class*="width-"]) { --width: 33.3333333333333%; }
	.l4st h1 i, .l4st h2 i, .l4st h3 i, .l4st h4 i, .l4st h5 i, .l4st h6 i { margin-bottom: 18px; }
	/*.l4ts {}*/
	.l4ts li { width: 50%; }
	/*.l4ts.box {}*/
	.l4ts.box .swiper-pagination-bullets { padding-top: 0; }
	.l4ts.box .swiper-button-prev { left: 0; }
	.l4ts.box .swiper-button-next { right: 0; }
	.l4ts.box .swiper-pagination-bullets, .l4ts.box .swiper-custom-pagination { padding-top: 4px; }
	.l4ts.box.slider-fraction .swiper-custom-pagination { padding-top: 12px; }
	.l4ts q { max-width: 690px; }
	.m6as { --d: 32px; }
	.m6as:has(.l4cl.mobile-compact) { flex-direction: column;}
	.m6as:has(.l4cl.mobile-compact) .l4cl.hr { margin-right: var(--rpn); }
	#root .m6as:has(.l4cl.mobile-compact) > * { width: auto; padding-left: 0; padding-right: 0; }
	.m6ac { display: block; }
	#root .m6ac > *, #root .m6ac > [class] { width: 100%; }
	.m6ac .l4cl, .m6ac > *:first-child .l4cl, .m6ac .l4ft { margin-bottom: var(--dist_a); }
	.m6ac .l4cl[style*="--dist_a: 0px"], .m6ac > *:first-child .l4cl[style*="--dist_a: 0px"], .m6ac .l4ft[style*="--dist_a: 0px"] { margin-bottom: var(--rpp); }
	.m6ac .l4cl:last-child { margin-bottom: -6px; }
	.m6bx { padding-left: 25px; padding-right: 25px; }
	.m6cu, #root .m6cu { text-align: center; }
	#root .m6cu > * { max-width: none; }
	.m6cu h1, .m6cu h2, .m6cu h3, .m6cu h4, .m6cu h5, .m6cu h6 { width: 100%; }
	.m6fr { --pda: 28px; --mih: 250px; }
	.m6fr .swiper-pagination-bullets { bottom: 20px; }
	/*.m6fr.s4wi .swiper-slide article > div, .m6fr.s4wi article.aside, .m6fr.wide.s4wi article.aside > div { padding-bottom: calc(62px - var(--pdc) + var(--main_mr)); }*/
	.m6fr article.aside:not(.inv) > div { padding-right: calc(var(--w) + var(--pd) / var(--pdb)); padding-left: var(--pd); }
	.m6fr article.aside.inv > div { padding-left: calc(var(--w) + var(--pd) / var(--pdb)); padding-right: var(--pd); }
	.m6fr.slider-fraction .swiper-custom-pagination { bottom: 16px; padding-left: 20px; padding-right: 20px; }
	.m6fr .play-pause:before { bottom: 17px; }
	/*.m6fr.wide {}*/
	.m6fr.wide article, .m6fr.wide .swiper-slide article { padding-left: var(--rpp); padding-right: var(--rpp); }
	.m6pn.align-bottom {
		left: 0; right: 0; max-width: none; top: auto; bottom: 0; max-height: 100vh;
		transform: translateY(20px);
	}
	.m6pr, #root .m6pr { position: relative; z-index: 2; padding-left: 0; padding-right: 0; }
	.m6pr > *, #root .m6pr > * { position: relative; top: 0; width: 100%; min-width: 0; max-width: none; margin-left: 0; margin-right: 0; }
	#root .m6pr .l4pr-container .l4pr { max-width: none; margin-right: 0; }
	.m6pr > * > header:first-child, .m6pr > footer { margin-top: 0; }
	#root .m6pr .l4pr.s4wi { border-left-width: 28px; border-right-width: 28px; }
	.m6pr .l4pr .swiper-button-prev { width: 47px; }
	.m6pr .l4pr.s4wi:not(.slider-fraction) > .s1lb { left: -28px; }
	/*.m6tb {}*/
	.m6tb > nav { margin-bottom: 30px; }
	.table-wrapper, table { width: calc(100% + var(--rpp) + var(--rpp)); margin-left: var(--rpn); margin-right: var(--rpn); }
	.table-wrapper table { width: 100%; margin-left: 0; margin-right: 0; }
	.table-wrapper { overflow-x: auto; overflow-y: hidden; }
	.cols .table-wrapper, .cols table { width: 100%; margin-left: 0; margin-right: 0; }
	#content.w940, .w940.align-center, .w940.text-center { max-width: none; }

	/* NAV_TABLET_2 */
	#root .desktop-hide.link-btn, .shopify-section-footer > nav, .m6ac.mobile-inv { display: flex; flex-wrap: wrap; }
	#root .m6cu/*, .shopify-section-footer > nav*/ { flex-wrap: nowrap; }
	#root .m6cu { flex-direction: column; }
	.m6ac.mobile-inv { flex-direction: column-reverse; }
	.shopify-section-footer > nav { justify-content: flex-start; }
	.m6tb > nav ul, #root .m6cu { justify-content: center; }
	#search { flex-grow: 3; }

	.input-inline button i, .input-inline .link-btn a i, .shopify-section-header .toggle > #localization_form, #root .shopify-section-header #header-inner > .link-btn.hidden { display: block; }

	#root #nav-top > ul.l4us > li.swiper-wrapper:before, #root #nav-top > ul.l4us > li.swiper-wrapper:after, #header-inner > .link-btn a:after, #header > .link-btn a:before, #header-inner > .link-btn a:before, #root .desktop-only.link-btn, .l4ca footer p a span.mobile-hide, .shopify-section-header #localization_form, .l4cl .link-btn.sticky, #root .m6pr footer.desktop-only, #nav-user > ul > li.link-btn { display: none; }
	#root .desktop-hide, #root .tablet-only { position: relative; left: 0; top: 0; }
	#root .desktop-only, #root .tablet-hide { position: absolute; left: -3000em; top: -3000em; right: auto; bottom: auto; }
}
@media only screen and (min-width: 1001px) {
	#nav > ul > li > a, #nav-bar > ul > li > a { font-family: var(--custom_top_nav_ff); font-size: var(--custom_top_nav_fz); font-style: var(--custom_top_nav_fs); font-weight: var(--custom_top_nav_fw); letter-spacing: var(--custom_top_nav_ls); }
	#nav-user > ul > li.sub.no-sub > a { padding-left: 0; padding-right: 0; }

	.shopify-section-header a:has(.s1bx) { --bd_w: 0; }

	.l4cl .swiper-horizontal { -ms-touch-action: pan-y; touch-action: pan-y; }
	.l4cl .swiper-outer { overflow: hidden; }
	.l4pr.static {
		margin: 0 0 var(--mb) calc(0px - var(--dist)); --width: 50%; --dist: 20px; --c: 0.5; --mb: calc(38px - var(--dist));
		display: flex; flex-wrap: wrap;
		align-items: flex-start;
	}
	.l4pr.static.w20, .l4pr.static .w20 { --c: 0.2; --width: 20%; }
	.l4pr.static.w25, .l4pr.static .w25 { --c: 0.25; --width: 25%; }
	.l4pr.static.w33, .l4pr.static .w33 { --c: 0.33; --width: 33.33333333%; }
	.l4pr.static.w100, .l4pr.static .w100 { --c: 1; --width: 100%; }
	.l4pr.static li { width: calc(var(--width) - var(--dist)); margin: 0 0 var(--dist) var(--dist); padding-top: calc(var(--ratio) * 100% * var(--c) - var(--dist)); }
	.l4pr.static .model-3d { position: absolute; left: 0; right: 0; top: 0; top: 0; bottom: 0; }
	.m6pr .l4pr.static { width: calc(100% + var(--dist)); }

	.l4cl.list li { flex-wrap: nowrap; }
	.link-btn.desktop-hide, #root .l4hs > li > a.desktop-hide, #nav-user > ul > li.sub.no-sub > a:before { display: none; }
}
@media only screen and (max-width: 1000px) {
	#root .l4pr.static > li:not(:first-child), .link-btn.desktop-only, #root .l4hs > li > a.desktop-only { display: none; }
}
@media only screen and (min-width: 761px) {
	#logo .mobile-only { display: none; }
	#nav-user > ul > li > a.mobile-only { display: none !important; }

	[dir="ltr"] #root #header-inner > .link-btn > .mobile-only:first-child + *, [dir="ltr"] #root #header-inner > .link-btn > .desktop-hide:first-child + * { margin-left: 0; }
	[dir="rtl"] #root #header-inner > .link-btn > .mobile-only:first-child + *, [dir="rtl"] #root #header-inner > .link-btn > .desktop-hide:first-child + * { margin-right: 0; }

	h1.cols > .small, h2.cols > .small, h3.cols > .small, h4.cols > .small, h5.cols > .small, h6.cols > .small { margin-bottom: 0; }

	/*.cols {}*/
	.cols > .link-btn { margin-right: 0; }
	.shopify-section-breadcrumbs { margin-left: min(calc(var(--rpn) + var(--rpp)), calc(0px - 50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5 + var(--rpp))); margin-right: min(calc(var(--rpn) + var(--rpp)), calc(0px - 50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5 + var(--rpp))); }
	.shopify-section-breadcrumbs .n6br { width: 100%; max-width: var(--ghw); margin-left: auto; margin-right: auto; }
	.shopify-section-breadcrumbs + * { margin-top: 0; }
	/*.l4ca {}*/
	.l4ca footer p, .l4ca .s1pr { margin-bottom: var(--pt); }
	.l4ca .cols .s1pr { margin-bottom: var(--mr_i); }
	.l4cl.small.s4wi { margin-left: 20px; margin-right: 20px; }
	/*.l4ft {}*/
	.l4ft li.w12, .l4ft.w12 li, .l4ft.w12 { --mih: 120px; }
	.l4ft li.w14, .l4ft.w14 li, .l4ft.w14 { --mih: 140px; }
	.l4ft li.w16, .l4ft.w16 li, .l4ft.w16 { --mih: 180px; }
	.l4ft li.w20, .l4ft.w20 li, .l4ft.w16 { --mih: 220px; }
	.l4ft li.w25, .l4ft.w25 li, .l4ft.w16 { --mih: 275px; }
	.l4ft.fullwidth, .l4cl.fullwidth { margin-left: min(var(--rpn), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5)); margin-right: min(calc(var(--rpn) + var(--dist_a)), calc(-50vw + var(--glw) * 0.5) + var(--dist_a) + var(--scrollbar_width) * 0.5); }
	.l4ft.fullwidth.s4wi, .l4cl.fullwidth.s4wi { margin-left: min(calc(var(--rpn) + var(--dist_a)), calc(-50vw + var(--glw) * 0.5 + var(--scrollbar_width) * 0.5 + var(--dist_a))); }
	.m6ac .l4ft.fullwidth { margin-left: calc(0px - var(--dist_a)); margin-right: 0; }
	.l4ft.outer-radius { --dist_a: 0px; }
	.l4ft[style*="dist_a: 0"]:not(.fullwidth), .l4ft.outer-radius { overflow: hidden; border-radius: var(--b2p); }
	#root .l4ft[style*="dist_a: 0"]:not(.fullwidth) li, .l4ft.outer-radius li { transform: none; }
	#root .l4ft[style*="dist_a: 0"]:not(.fullwidth) figure, .l4ft.outer-radius figure, #root .l4ft[style*="dist_a: 0"]:not(.fullwidth) li:before, .l4ft.outer-radius .main:before, #root .l4ft[style*="dist_a: 0"]:not(.fullwidth) li:after, .l4ft.outer-radius li:after, .l4ft.outer-radius.fullwidth { border-radius: 0; --b2p: 0px; }
	.l4cl.text-justify { justify-content: space-between; align-content: center; }
	.l4cl.text-justify .swiper-wrapper, .l4cl.align-center .swiper-wrapper { align-items: center; }
	.l4cl.align-center .swiper-slide { height: auto; }
	.l4cl.text-justify li { width: auto; }
	.l4cl .swiper-android .swiper-slide, .l4cl .swiper-wrapper { transform: translate3d(0px,0,0); }
	.l4cl:not(.mobile-compact) .swiper-horizontal { -ms-touch-action: pan-y; touch-action: pan-y; }
	.l4cl:not(.mobile-compact) .swiper-outer { overflow: hidden; }
	.l4cl.static-height .swiper-wrapper { height: auto !important; }
	.l4ne.featured.landscape figure, .l4ne.featured .landscape figure { padding-top: 55%; }
	.l4pr .sticky .m6bx { position: sticky; left: 0; right: 0; bottom: 10px; }
	.l4pr.no-thumbs-desktop .swiper-button-nav { bottom: 0; height: auto !important; }
	#root .s1lb > span.rounded { --size: calc(var(--main_fz) * 11.5714285714); }
	/*.m6as.compact-img {}*/
	.m6as.compact-img > figure { height: auto; min-height: 0; }
	.m6as.compact-img > figure img, .m6as.compact-img > figure picture, .m6as.compact-img > figure video, .m6as.compact-img > figure svg { position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 5; min-height: 0 !important; }
	.m6as.compact-img > figure .img-overlay { z-index: 6; }
	.m6as.compact-img.compact-img-stretch > figure { align-self: stretch; }
	/*.m6as.img-compact {}*/
	.m6as.wide { --offset: min(var(--rpn), calc(-50vw + var(--glw) * 0.5) + var(--scrollbar_width) * 0.5); }
	[dir="ltr"] .m6as.wide:not(.inv) > figure, [dir="rtl"] .m6as.wide.inv > figure { width: calc(var(--w_f) - var(--offset)); margin-left: var(--offset); }
	[dir="ltr"] .m6as.wide:not(.inv) > figure img, [dir="rtl"] .m6as.wide.inv > figure img,
	[dir="ltr"] .m6as.wide:not(.inv) > figure video, [dir="rtl"] .m6as.wide.inv > figure video,
	[dir="ltr"] .m6as.wide:not(.inv) > figure svg, [dir="rtl"] .m6as.wide.inv > figure svg,
	[dir="ltr"] .m6as.wide:not(.inv) > figure iframe, [dir="rtl"] .m6as.wide.inv > figure iframe,
	[dir="ltr"] .m6as.wide:not(.inv) > figure .img-overlay, [dir="rtl"] .m6as.wide.inv > figure .img-overlay { border-top-left-radius: 0; border-bottom-left-radius: 0; }
	[dir="ltr"] .m6as.wide.inv > figure, [dir="rtl"] .m6as.wide:not(.inv) > figure { width: calc(var(--w_f) - var(--offset)); margin-right: var(--offset); }
	[dir="ltr"] .m6as.wide.inv > figure img, [dir="rtl"] .m6as.wide:not(.inv) > figure img,
	[dir="ltr"] .m6as.wide.inv > figure video, [dir="rtl"] .m6as.wide:not(.inv) > figure video,
	[dir="ltr"] .m6as.wide.inv > figure svg, [dir="rtl"] .m6as.wide:not(.inv) > figure svg,
	[dir="ltr"] .m6as.wide.inv > figure iframe, [dir="rtl"] .m6as.wide:not(.inv) > figure iframe,
	[dir="ltr"] .m6as.wide.inv > figure .img-overlay, [dir="rtl"] .m6as.wide:not(.inv) > figure .img-overlay { border-top-right-radius: 0; border-bottom-right-radius: 0; }
	/*.m6fr {}*/
	#root .m6fr figure picture[style*="--size"], #root .m6fr figure video[style*="--size"] { width: var(--size) !important; }

	.slider-fraction[data-active-content*="aside"] .swiper-custom-pagination, .m6as.align-top > div, .m6fr article.align-top { justify-content: flex-start; }
	.slider-fraction[data-active-content*="aside"][data-active-content*="inv"] .swiper-custom-pagination { justify-content: flex-end; }
	.slider-fraction:has(.swiper-slide-active>.aside) .swiper-custom-pagination { justify-content: flex-start; }
	.slider-fraction:has(.swiper-slide-active>.aside.inv) .swiper-custom-pagination { justify-content: flex-end; }

	#root .l4cl li.link ~ li, .l4pr.no-thumbs-desktop .swiper-pagination-bullets, .link-btn.mobile-only, #root .m6fr figure .mobile-only, .media-flexible-mobile { display: none; }
	.shopify-section-footer .mobile-only { display: none !important; }
}
@media only screen and (min-width: 761px) and (max-width: 1000px) {
	:root {
		--custom_top_search_h: min(calc(var(--header_mih_c) - 10px), calc(var(--btn_fz) * var(--main_lh_h) + min(var(--btn_pv),20px) * 2));
	}
	.shopify-section-footer > nav .m6cn figure { right: 0; left: 0; bottom: 0; max-width: none; }
	.shopify-section-footer > nav .m6cn figure img { max-width: 162px !important; }
	#root .shopify-section-footer > nav > *:not(.m6cn) { max-width: calc(25% - 6px); }

	#root .l4ft.mobile-compact li:nth-child(1):nth-last-child(2), #root .l4ft.mobile-compact li:nth-child(2):nth-last-child(1) { flex-grow: 3; width: 50% !important; min-width: 0 !important; max-width: none !important; border-right-width: 0; }
	#nav-user > ul > li.tablet-hide { display: none; }
}
@media only screen and (max-width: 760px) {
	:root {
		--size_70_f: 12vw;
		--main_fz: var(--mob_fz);
		--btn_fz: var(--btn_fz_mob);
		--btn_miw: min(100%, 120px);

		--main_h_small: var(--mob_h_small);
		--main_h1: var(--mob_h1);
		--main_h2: var(--mob_h2);
		--main_h3: var(--mob_h3);
		--main_h4: var(--mob_h4);
		--main_h5: var(--mob_h5);
		--main_h6: var(--mob_h6);
		--main_lead: var(--mob_lead);

		--content_p: 20px;
		--header_mih_c: var(--header_mih_m);
		--sticky_offset: var(--sticky_offset_m);
		--nav_top_h: var(--nav_top_h_m);
		--placeholder_fz: max(var(--size_16_f),var(--main_fz));
	}
	#content, .shopify-section-footer { width: 100%; max-width: none; margin-left: 0; margin-right: 0; }
	body, label, .label { font-size: var(--mob_fz); font-family: var(--main_ff); letter-spacing: var(--main_ls); }

	/*h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6, legend { margin-top: 24px; }*/
	/*figure {}*/
	h1 + figure, h2 + figure, h3 + figure, h4 + figure, h5 + figure, h6 + figure, .h1 + figure, .h2 + figure, .h3 + figure, .h4 + figure, .h5 + figure, .h6 + figure, hr + * { margin-top: 0; }
	figure + h3, figure + h4, figure + h5, figure + h6, figure.lead { margin-top: 12px; }
	figure + h1, figure + h2 { margin-top: 30px; }
	.mobile-size-10, .m6as .mobile-size-10, .check.mobile-size-10, .l4ft .mobile-size-10, .m6fr .mobile-size-10, .l4cl.category .mobile-size-10 { font-size: var(--size_10_f); }
	.mobile-size-12, .m6as .mobile-size-12, .check.mobile-size-12, .l4ft .mobile-size-12, .m6fr .mobile-size-12, .l4cl.category .mobile-size-12 { font-size: var(--size_12_f); --main_fz: var(--size_12_f); }
	.mobile-size-13, .m6as .mobile-size-13, .check.mobile-size-13, .l4ft .mobile-size-13, .m6fr .mobile-size-13, .l4cl.category .mobile-size-13 { font-size: var(--size_13_f); }
	.mobile-size-14, .m6as .mobile-size-14, .check.mobile-size-14, .l4ft .mobile-size-14, .m6fr .mobile-size-14, .l4cl.category .mobile-size-14 { font-size: var(--size_14_f); }
	.mobile-size-16, .m6as .mobile-size-16, .check.mobile-size-16, .l4ft .mobile-size-16, .m6fr .mobile-size-16, .l4cl.category .mobile-size-16 { font-size: var(--size_16_f); }
	.mobile-size-18, .m6as .mobile-size-18, .check.mobile-size-18, .l4ft .mobile-size-18, .m6fr .mobile-size-18, .l4cl.category .mobile-size-18 { font-size: var(--size_18_f); }
	.mobile-size-20, .m6as .mobile-size-20, .check.mobile-size-20, .l4ft .mobile-size-20, .m6fr .mobile-size-20, .l4cl.category .mobile-size-20 { font-size: var(--size_20_f); }
	.mobile-size-22, .m6as .mobile-size-22, .check.mobile-size-22, .l4ft .mobile-size-22, .m6fr .mobile-size-22, .l4cl.category .mobile-size-22 { font-size: var(--size_22_f); }
	.mobile-size-24, .m6as .mobile-size-24, .check.mobile-size-24, .l4ft .mobile-size-24, .m6fr .mobile-size-24, .l4cl.category .mobile-size-24 { font-size: var(--size_24_f); }
	.mobile-size-26, .m6as .mobile-size-26, .check.mobile-size-26, .l4ft .mobile-size-26, .m6fr .mobile-size-26, .l4cl.category .mobile-size-26 { font-size: var(--size_26_f); }
	.mobile-size-28, .m6as .mobile-size-28, .check.mobile-size-28, .l4ft .mobile-size-28, .m6fr .mobile-size-28, .l4cl.category .mobile-size-28 { font-size: var(--size_28_f); }
	.mobile-size-30, .m6as .mobile-size-30, .check.mobile-size-30, .l4ft .mobile-size-30, .m6fr .mobile-size-30, .l4cl.category .mobile-size-30 { font-size: var(--size_30_f); }
	.mobile-size-32, .m6as .mobile-size-32, .check.mobile-size-32, .l4ft .mobile-size-32, .m6fr .mobile-size-32, .l4cl.category .mobile-size-32 { font-size: var(--size_32_f); }
	.mobile-size-34, .m6as .mobile-size-34, .check.mobile-size-34, .l4ft .mobile-size-34, .m6fr .mobile-size-34, .l4cl.category .mobile-size-34 { font-size: var(--size_34_f); }
	.mobile-size-36, .m6as .mobile-size-36, .check.mobile-size-36, .l4ft .mobile-size-36, .m6fr .mobile-size-36, .l4cl.category .mobile-size-36 { font-size: var(--size_36_f); }
	.mobile-size-38, .m6as .mobile-size-38, .check.mobile-size-38, .l4ft .mobile-size-38, .m6fr .mobile-size-38, .l4cl.category .mobile-size-38 { font-size: var(--size_38_f); }
	.mobile-size-40, .m6as .mobile-size-40, .check.mobile-size-40, .l4ft .mobile-size-40, .m6fr .mobile-size-40, .l4cl.category .mobile-size-40 { font-size: var(--size_40_f); }
	.mobile-size-46, .m6as .mobile-size-46, .check.mobile-size-46, .l4ft .mobile-size-46, .m6fr .mobile-size-46, .l4cl.category .mobile-size-46 { font-size: var(--size_46_f); }
	.mobile-size-48, .m6as .mobile-size-48, .check.mobile-size-48, .l4ft .mobile-size-48, .m6fr .mobile-size-48, .l4cl.category .mobile-size-48 { font-size: var(--size_48_f); }
	.mobile-size-50, .m6as .mobile-size-50, .check.mobile-size-50, .l4ft .mobile-size-50, .m6fr .mobile-size-50, .l4cl.category .mobile-size-50 { font-size: var(--size_50_f); }
	.mobile-size-52, .m6as .mobile-size-52, .check.mobile-size-52, .l4ft .mobile-size-52, .m6fr .mobile-size-52, .l4cl.category .mobile-size-52 { font-size: var(--size_52_f); }
	.mobile-size-56, .m6as .mobile-size-56, .check.mobile-size-56, .l4ft .mobile-size-56, .m6fr .mobile-size-56, .l4cl.category .mobile-size-56 { font-size: var(--size_56_f); }
	.mobile-size-60, .m6as .mobile-size-60, .check.mobile-size-60, .l4ft .mobile-size-60, .m6fr .mobile-size-60, .l4cl.category .mobile-size-60 { font-size: var(--size_60_f); }
	.mobile-size-70, .m6as .mobile-size-70, .check.mobile-size-70, .l4ft .mobile-size-70, .m6fr .mobile-size-70, .l4cl.category .mobile-size-70 { font-size: var(--size_70_f); }
	.mobile-text-uppercase { text-transform: uppercase; --btn_tt: uppercase; }
	hr { margin-top: 26px; }
	aside hr { margin: 20px 0; }
	.mob-h1 { font-size: var(--mob_h1); }
	.mob-h2 { font-size: var(--mob_h2); }
	.mob-h3 { font-size: var(--mob_h3); }
	.mob-h4 { font-size: var(--mob_h4); }
	.mob-h5 { font-size: var(--mob_h5); }

	#root { overflow: hidden; }
	.shopify-section-header { z-index: 99; }
	/*.shopify-section-header.fixed { z-index: 99; }*/
	.shopify-section-header #header { position: static; width: 100%; height: auto; margin-right: 0; padding-left: 0; padding-right: 0; }
	#root .shopify-section-header #header { padding-left: 0; padding-right: 0; }
	.shopify-section-header #header > *, #root .shopify-section-header #header-inner > * { margin-right: 0; padding-right: 0; border-right-width: 0; }
	#root .shopify-section-header #header-inner > .link-btn { margin-right: var(--dist_main); }
	[dir="rtl"] #root .shopify-section-header #header-inner > .link-btn { margin-left: var(--dist_main); margin-right: 0; }
	#root #header > #header-inner:first-child:last-child { margin-right: 0; }
	#root .shopify-section-header:has(#nav.no-bd, #nav-bar.no-bd) { --custom_top_nav_bd: rgba(0,0,0,0); }
	#logo { display: block; position: relative; z-index: 11; font-size: var(--size_14); }
	/*#header-inner:not(.text-center-mobile) #logo { max-width: 50% !important; }*/
	#logo a { overflow: hidden; height: 100%; }
	[dir="ltr"] #logo.mobile-text-end { margin-left: auto; }
	[dir="ltr"] #logo.mobile-text-end ~ #nav-user { margin-left: 0; }
	#root #search { position: absolute; left: 0; right: 0; top: -3000em; bottom: auto; z-index: 2; max-width: none; margin: 0; padding: var(--search_mob_pd) 0; }
	/* moved to async-search.css: .search-compact-active #root #search { top: 100%; }*/
	#root .shopify-section-header #header-inner > #search { max-width: none; }
	#search:before { display: block; left: var(--rpn); right: var(--rpn); width: auto; background: var(--custom_top_search_bg_cont); transform: none; }
	#search input { padding-right: 45px; box-shadow: 0 2px 2px rgba(0,0,0,.02); }
	#search.has-text input { padding-right: 80px; }
	#search button { bottom: 0; width: 44px; height: var(--custom_top_search_h); min-height: 0; font-size: var(--main_fz); }
	#search button:before { font-size: 1.2602071429em; }
	#root #search:not(.compact-handle, .compact-handle-mobile) ~ #nav-user > ul > li.search { display: block; }
	#header > .link-btn, #header-inner > .link-btn { margin-top: -12px; margin-bottom: 0; }
	#nav-user { position: static; z-index: 10; margin-top: -12px; margin-bottom: 0; margin-right: 0; padding-left: var(--dist_main); }
	#nav-user.has-form { left: auto; }
	#nav-user > ul > li.sub > a:before { display: none; }
	#root #nav-user { margin-right: 0; }
	#nav-user > ul > li { position: static; z-index: 1; }
	#nav-user > ul > li.user-login { position: static; z-index: 2; }
	#nav-user > ul > li.has-form { position: static; z-index: 2; }
	#root #nav-user > ul > li.has-form > a ~ a.toggle { display: none !important; }
	#nav-user > ul > li > a.mobile-hide { display: none !important; }
	#nav-user, #nav-user > ul > li > a, #root #nav-user > ul > li > a i { line-height: 48px; }
	#nav-top > ul > li.sub > a, #nav-user > ul > li.sub > a { padding-right: 0; }
	#nav-top, #nav-top * { white-space: nowrap; }
	#nav-top > ul { position: relative; z-index: 9; margin-left: 20px; }
	#nav-top > ul.l4us { margin-left: 0; }
	#nav-top > ul.l4us li { border-width: 0; }
	#nav-top > ul.l4us > li ~ li { display: none; }
	#nav-top > ul.text-start, #root .shopify-section-header #nav-top > ul.text-start { margin-right: -10px; margin-left: 0; }
	#nav-top > .l4us.slider:first-child { margin-right: 0; }
	#nav-top .l4us { max-width: 100%; min-width: 0; height: 100%; margin-right: 0; }
	#nav-top .l4us:first-child, #nav-top .l4us:first-child + .l4us { margin-left: 0; margin-right: -14px; }
	#nav-top .l4us.s4wi:first-child, #nav-top .l4us:first-child + .l4us.s4wi { margin-right: 0; }
	#nav-top > .l4us-mobile { display: block; width: 100%; max-width: none; z-index: 10; margin-bottom: 0 }
	#nav-top > .l4us-mobile li { display: block; overflow: hidden; border: 0 solid rgba(0,0,0,0); text-overflow: ellipsis; white-space: nowrap; }
	#nav-top > .l4us:first-child + .l4us:last-child, #nav-top > .l4us:first-child + .l4us:nth-last-child(2), #nav-top > .l4us:first-child:last-child, #nav-top > .l4us:first-child:nth-last-child(2) { flex-grow: 3; }
	#header-inner > .link-btn { bottom: 0; }
	/*#search {}*/
	#search.text-start, .shopify-section-header .link-btn ~ #search.text-start, #root .shopify-section-header .link-btn ~ #search.text-start { margin-left: 0; margin-right: 0; }
	/*#content {}*/
	#content > *:last-child, #content > [id*="shopify-section"]:last-child > *:last-child { margin-bottom: var(--main_mr); }
	#content > .shopify-section-footer:last-child { margin-top: var(--main_mr); margin-bottom: 0; }
	#content > .cols:last-child, #content > form:last-child, #content > [id*="shopify-section"]:last-child > .cols-a:last-child, #content > [id*="shopify-section"]:last-child > form:last-child { margin-bottom: 6px; }
	#cookie-bar { padding-top: 13px; padding-bottom: 5px; --mih: 70px; }
	#cookie-inner { padding-left: 44px; }
	#cookie-bar .icon-cookie { display: block; position: absolute; left: 0; top: 7px; font-size: 30px; line-height: 30px; }
	#cookie-bar p { margin-bottom: 12px; }
	#root #cookie-bar .link-btn { padding-left: 0; padding-right: 0; --btn_dist: 30px; }
	/*.shopify-section-footer {}*/
	.shopify-section-footer > nav {
		/*display: block;*/ padding: 12px 0 .1px; text-align: var(--text_align_start);
		flex-direction: column;
		flex-wrap: nowrap;
	}
	.shopify-section-footer > nav + nav, .shopify-section-footer > nav + hr + nav { margin-top: -25px; }
	#root .shopify-section-footer > nav > * {
		position: relative; z-index: 2; width: calc(100% + var(--rpp) * 2); min-width: 0; max-width: none; margin: 0 var(--rpn); padding: 0 var(--rpp) 1px;
		order: 1;
	}
	#root .shopify-section-footer > nav > *:before { display: block; border-bottom: 1px solid var(--custom_footer_fg); opacity: .1; }
	#root .shopify-section-footer > nav > *:last-child:before { display: none; }
	#root .shopify-section-footer > nav:has(>.m6cn:last-child) > *:nth-last-child(2):before { display: none; }
	.shopify-section-footer > nav h1, .shopify-section-footer > nav h2, .shopify-section-footer > nav h3, .shopify-section-footer > nav h4, .shopify-section-footer > nav h5, .shopify-section-footer > nav h6 { margin: 0; padding: var(--pd_f_h) 24px var(--pd_f_h) 0; /*font-size: max(var(--size_16_f), calc(var(--main_fz) * 1.1428571429));*/ text-align: var(--text_align_start); }
	.shopify-section-footer > nav h1:last-child, .shopify-section-footer > nav h2:last-child, .shopify-section-footer > nav h3:last-child, .shopify-section-footer > nav h4:last-child, .shopify-section-footer > nav h5:last-child, .shopify-section-footer > nav h6:last-child { padding-left: 0; padding-right: 0; }
	.js .shopify-section-footer > nav h1 ~ *, .js .shopify-section-footer > nav h2 ~ *, .js .shopify-section-footer > nav h3 ~ *, .js .shopify-section-footer > nav h4 ~ *, .js .shopify-section-footer > nav h5 ~ *, .js .shopify-section-footer > nav h6 ~ * { display: none; }
	.shopify-section-footer > nav:first-child { margin-top: -12px; }
	#root .shopify-section-footer > nav > .toggle, #root .shopify-section-footer > nav .m6cn, #root .shopify-section-footer > nav > .strong { padding-bottom: max(0.1px, calc(var(--rpp) - 8px)); }
	#root .shopify-section-footer > nav .m6cn {
		padding-top: var(--rpp); padding-right: 80px;
		order: 0;
	}
	#root .shopify-section-footer > nav .m6cn .l4cn { padding-right: 50px; }
	.shopify-section-footer > nav .m6cn figure { display: block; position: absolute; right: -20px; left: -20px; bottom: 0; margin: 0; }
	.shopify-section-footer > nav .m6cn figure picture, .shopify-section-footer > nav .m6cn figure img { max-height: 100% !important; object-fit: contain; }
	.shopify-section-footer > nav .m6cn figure img { max-width: 162px !important; }
	.shopify-section-footer > div { margin-right: -22px; text-align: center; }
	.shopify-section-footer > div figure { width: 100%; margin-bottom: 9px; }
	.shopify-section-footer > div figure img { display: block; max-height: 30px !important; margin-left: auto; margin-right: auto; object-fit: contain; }
	.shopify-section-footer > div p, .shopify-section-footer > div .l4dr { width: auto; max-width: none; }
	.shopify-section-footer > div p, .shopify-section-footer > div figure, .shopify-section-footer > div > * { width: 100%; max-width: none; margin-right: 22px; }
	.shopify-section-footer > div .l4dr { margin-right: 8px; }
	.shopify-section-footer > div .l4pm { width: calc(100% + 8px); min-width: calc(100% + 8px); max-width: calc(100% + 8px); margin-top: 0; margin-left: 0; margin-right: 0; }
	#root .shopify-section-footer > div .l4pm.box { position: relative; left: -6px; margin-right: 0; padding-left: 0; padding-right: 0; }
	.shopify-section-footer h1, .shopify-section-footer h2, .shopify-section-footer h3, .shopify-section-footer h4, .shopify-section-footer h5, .shopify-section-footer h6, .shopify-section-footer > nav .m6cn h1, .shopify-section-footer > nav .m6cn h2, .shopify-section-footer > nav .m6cn h3,
	.shopify-section-footer > nav .m6cn h4, .shopify-section-footer > nav .m6cn h5, .shopify-section-footer > nav .m6cn h6 { margin-bottom: 8px; padding: 0; }
	.shopify-section-footer a.header-toggle { overflow: visible; }
	.shopify-section-footer a.header-toggle:before { content: "\e904"; left: auto; font-size: 0.4285714286em; }
	.shopify-section-footer :last-child > a.header-toggle { display: none; }
	.shopify-section-footer .toggle a.header-toggle:before { transform: rotate(180deg); }
	#root .shopify-section-footer > nav > .strong { border-bottom-width: 0; }
	.shopify-section-footer > nav > .strong h1, .shopify-section-footer > nav > .strong h2, .shopify-section-footer > nav > .strong h3, .shopify-section-footer > nav > .strong h4, .shopify-section-footer > nav > .strong h5, .shopify-section-footer > nav > .strong h6 { margin-bottom: 8px; padding-bottom: 0; font-size: max(var(--size_16_f), calc(var(--main_fz) * 1.1428571429)); }
	.shopify-section-footer > *:not(.column) figure, .shopify-section-footer > *:not(.column) p, .shopify-section-footer > *:not(.column) ul, .shopify-section-footer > *:not(.column) .m6cn { --main_mr: 8px; }
	.shopify-section-footer .l4dr li.toggle > ul {
		left: 50%; right: auto;
		transform: translateX(-50%);
	}
	.shopify-section-footer form, .shopify-section-footer fieldset { position: relative; width: 100%; max-width: none; }
	.shopify-section-footer p { width: calc(100% - 55px); }
	.shopify-section-footer input { width: 100%; max-width: none; }
	#root .shopify-section-footer input ~ button.mobile-only { float: right; width: 45px; margin-top: calc(0px - var(--input_h)); margin-right: -55px; }
	.shopify-section-footer p.check { width: 100%; margin-bottom: 8px; }
	.shopify-section-footer p.submit { width: 100%; }
	.shopify-section-footer p.submit { position: absolute; right: 0; bottom: 0; top: auto; z-index: 100 !important; width: 45px; height: var(--input_h); margin-left: 10px; margin-right: 0; }
	.shopify-section-footer button { width: 100%; min-width: 0; height: var(--input_h); margin: 0; padding: 0; text-align: left; text-indent: -3000em; direction: ltr; }
	#root .shopify-section-footer button i { display: block; top: 50%; font-size: 10px; }
	.shopify-section-footer > nav .m6cn.mobile-no-img p { width: 100%; }
	#root .shopify-section-footer > nav .m6cn.mobile-no-img { padding-right: var(--rpp); padding-left: var(--rpp); }
	#root .shopify-section-footer > nav .m6cn.mobile-no-img .l4cn { padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0; }
	#root .shopify-section-footer > nav .m6cn.mobile-no-img figure { display: none; }
	.shopify-section-footer > nav .m6cn figure.static { left: var(--rpp); right: var(--rpp); bottom: var(--rpp); }
	.shopify-section-footer > nav .m6cn figure.text-start { position: relative; left: 0; right: 0; bottom: 0; margin-bottom: var(--main_mr); }
	.shopify-section-footer > nav.m0-mobile { padding-bottom: 0; }
	#root .shopify-section-footer > nav.m0-mobile > *:last-child:before { border-bottom-width: 0; }
	#totop { padding-bottom: 0 !important; }

	input, select, textarea, .input-prefix > span:first-child, .input-suffix > span:first-child { font-size: max(var(--size_16_f), var(--main_fz)); }

	.accordion-a { margin: 16px var(--rpn) 32px; }
	.accordion-a summary { padding-top: var(--pd_f_h); padding-bottom: var(--pd_f_h); font-size: var(--main_h_small); }
	.accordion-a summary:before { font-size: 1em; }
	/*.accordion-a.compact {}*/
	#root .accordion-a.compact details { padding-left: var(--rpp); padding-right: var(--rpp); }
	.accordion-a.compact summary { padding-right: 20px; }
	.accordion-a.compact summary:before { right: 0; width: auto; min-width: 10.13px; }
	.m6pr .accordion-a { margin-bottom: 0; }
	.m6pr .accordion-a + * { margin-top: 26px; }
	.m6pr .accordion-a.compact + .accordion-a { margin-top: -10px; }
	.m6pr .accordion-a.compact + .accordion-a.compact { margin-top: -1px; }
	.m6pr .accordion-a + .m6tb, .m6pr .accordion-a + .accordion-a, .m6tb + .accordion-a { margin-top: -1px; }
	.m6pr .accordion-a + .accordion-a > details:first-child:before, .m6tb + .accordion-a > details:first-child:before { border-top-width: 0; }
	/*.accordion-a.compact {}*/
	.accordion-a.compact details[open] summary { margin-bottom: 0; }
	.accordion-a.compact p:not(.link-btn, .s1pr, .r6rt), .accordion-a.compact ul, .accordion-a.compact ol { margin-bottom: var(--main_mr); }
	blockquote, #root blockquote { padding: 0; }
	q, blockquote { font-size: var(--size_16_f); }
	blockquote p + .size-14 { margin-top: -7px; }
	/*.check {}*/
	.check input[type="radio"]:checked ~ label:after { box-shadow: inset 0 -3px 0 var(--white), inset 0 3px 0 var(--white), inset 3px 0 0 var(--white), inset -3px 0 0 var(--white); outline: none; }
	.check.box.mobile-scroll { overflow-x: auto; overflow-y: hidden; margin-left: var(--rpn); margin-right: var(--rpn); white-space: nowrap; flex-wrap: nowrap; }
	.check.box.mobile-scroll > * { flex-shrink: 0; }
	[dir="ltr"] .check.box.mobile-scroll > *:first-child { margin-left: var(--rpp); }
	[dir="ltr"] .check.box.mobile-scroll > *:last-child, [dir="ltr"] .check.box.mobile-scroll > .last-child { margin-right: var(--rpp); }
	.check.box.mobile-scroll .invalid-feedback { display: none !important; }
	#root .cols.aside { width: auto; padding-left: 0; padding-right: 0; }
	#root .cols.aside > *, #root .cols:not(.cols-mobile, .link-btn) > * { width: 100%; margin-left: 0; margin-right: 0; }
	#root .link-btn.cols > * { width: auto; }
	.cols.cols-mobile > .w20 { min-width: 96px; }
	.cols.cols-mobile > .link-btn { margin-right: 0; }
	.cols.b30 { --cols: 20px; }
	/*.cols.b50 {}*/
	#root .cols.b50 > *, #root .cols > *:first-child:last-child:not([class*="align"]), #root .f8lg .cols > *, .l4ne li, #root .f8cm .cols > *, #root .cols.b75 > * { float: none; width: 100%; }
	/*.countdown {}*/
	.countdown .simply-section { margin-bottom: var(--main_mr); }
	.js .countdown, .js #root .countdown { width: 100%; margin-left: auto; margin-right: auto; }
	.js #root span.countdown { margin-bottom: 0; }
	.js .countdown { margin-top: 12px; }
	span.countdown { margin-left: 0; margin-right: 0; }
	.js .countdown.compact, .js #root .countdown.compact { width: auto; margin: 0; }
	/*.form-cart {}*/
	.form-cart aside .input-show + * { margin-top: -8px; padding-top: 18px; }
	.form-cart aside .input-show.toggle + * { margin-top: -18px; }
	.form-cart .link-btn.mobile-sticky { display: block; position: fixed; left: 0; right: 0; bottom: 0; z-index: 997; margin: 0; padding: 6px var(--rpp) .1px; box-shadow: 0 -3px 6px rgba(0,0,0,.06); background: var(--white); }
	.form-cart .link-btn.mobile-sticky > * { display: none; float: none; width: 100%; margin: 0 0 6px; }
	.form-cart .link-btn.mobile-sticky > *:first-child { display: block; }
	/*.f8cm {}*/
	.f8cm h1, .f8cm h2, .f8cm h3, .f8cm h4, .f8cm h5, .f8cm h6 { margin-bottom: var(--main_mr); }
	.f8nw { margin-top: 32px; margin-bottom: 32px; }
	/*.f8pr {}*/
	.f8pr .submit, .f8pr .link-btn { max-width: none; }
	hr { margin-bottom: 25px; }
	.l4sc + hr { margin-top: -5px; }
	/*.input-show {}*/
	.input-show label, .js .input-show.toggle label { margin-bottom: 4px; }
	label .text-end i, .label .text-end i { margin-left: 12px; }
	/*.link-btn {}*/
	.link-btn a.wide, .submit .wide, .link-btn a.mobile-wide, button.mobile-wide, .btn-mobile-wide .link-btn a, .btn-mobile-wide button, .btn-mobile-wide input[type="button"], .btn-mobile-wide input[type="reset"], .btn-mobile-wide input[type="submit"] { min-width: 0; width: calc(100% - var(--btn_dist)); }
	.btn-mobile-wide .link-btn { min-width: calc(100% + var(--btn_dist)); }
	.link-btn + h1, .link-btn + h2, .link-btn + h3, .link-btn + h4, .link-btn + h5, .link-btn + h6, .link-btn.tags + h1, .link-btn.tags + h2, .link-btn.tags + h3, .link-btn.tags + h4, .link-btn.tags + h5, .link-btn.tags + h6, .link-btn.tags + .mobile-hide + h1, .link-btn.tags + .mobile-hide + h2, .link-btn.tags + .mobile-hide + h3, .link-btn.tags + .mobile-hide + h4, .link-btn.tags + .mobile-hide + h5, .link-btn.tags + .mobile-hide + h6, .link-btn + .mobile-hide + h1, .link-btn + .mobile-hide + h2, .link-btn + .mobile-hide + h3, .link-btn + .mobile-hide + h4, .link-btn + .mobile-hide + h5, .link-btn + .mobile-hide + h6 { margin-top: 24px; }
	.link-btn a.inline-mobile { min-width: 0; padding: 0; box-shadow: none; border-radius: 0; border-width: 0; background: none; color: var(--secondary_bg); font-size: 1em; }
	#root .link-btn a.inline-mobile { animation: none; }
	[data-whatintent="mouse"] #root .link-btn a.inline-mobile:hover { background: none; text-decoration: underline; }
	#root .link-btn .mobile-hide + i, #root button .mobile-hide + i, #root .submit .mobile-hide + i { margin-left: 0; margin-right: 0; }
	.link-btn.tags, .link-btn.mobile-compact { overflow-x: auto; overflow-y: hidden; margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: calc(var(--main_mr) - 8px); white-space: nowrap; }
	.link-btn.tags a, .link-btn.mobile-compact > * { flex: 10 0 auto; }
	[dir="ltr"] .link-btn.tags a:last-child, [dir="ltr"] .link-btn.mobile-compact > *:last-child { margin-right: var(--rpp); }
	[dir="ltr"] .link-btn.tags a:first-child, [dir="ltr"] .link-btn.mobile-compact > *:first-child { margin-left: var(--rpp); }
	/*.l4ad {}*/
	.l4ad li { margin-bottom: var(--main_mr); }
	#root .l4ad h1, #root .l4ad h2, #root .l4ad h3, #root .l4ad h4, #root .l4ad h5, #root .l4ad h6 { margin-bottom: 13px; font-size: 1.0714285714em; }
	.l4ad p, .l4ad ul { margin-bottom: 11px; }
	.l4ad .l4cn { margin-bottom: 2px; }
	/*.l4al {}*/
	.l4al.fixed { left: 0; right: 0; bottom: 0; top: auto; width: auto; min-width: 0; }
	#root .l4al.fixed li { margin: 0; }
	#root .l4al.fixed li:after, #root .l4al.fixed li:before { border-radius: 0; }
	.l4al + .l4ca { margin-top: -8px; }
	.l4as.caption { padding-left: 92px; padding-top: 0; }
	.l4ca { --img_w: 50px; }
	.l4ca li, .l4ca.summary li, .popup-a .l4ca li { display: block; }
	#root .l4ca li > * { padding-left: 0; padding-right: 0; }
	.l4ca li:last-child { border-bottom-width: 0; }
	.l4ca figure, .l4ca.summary figure, .popup-a .l4ca figure { float: var(--text_align_start); width: var(--img_w); }
	.l4ca figure ~ *, #root .l4ca figure ~ .s1pr { clear: var(--text_align_end); float: var(--text_align_end); width: calc(100% - var(--img_w) - var(--img_d)); }
	.l4ca h1, .l4ca h2, .l4ca h3, .l4ca h4, .l4ca h5, .l4ca h6 { overflow: hidden; margin-bottom: 4px; padding: 0; /*text-overflow: ellipsis; white-space: nowrap;*/ }
	/*.l4ca header { margin-bottom: 16px; }*/
	.l4ca header h1, .l4ca header h2, .l4ca header h3, .l4ca header h4, .l4ca header h5, .l4ca header h6, .l4ca header h1 a, .l4ca header h2 a, .l4ca header h3 a, .l4ca header h4 a, .l4c aheader h5 a, .l4ca header h6 a { overflow: visible; text-overflow: inherit; white-space: normal; }
	.l4ca header .s1pr { margin-bottom: 0; }
	.l4ca h1 a, .l4ca h2 a, .l4ca h3 a, .l4ca h4 a, .l4ca h5 a, .l4ca h6 a { display: block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
	.l4ca p { margin-bottom: var(--main_mr_h); }
	/*.l4ca section { margin-bottom: var(--mr_i); }*/
	.l4ca section ul, .l4ca section p { margin-bottom: 2px; }
	#root .l4ca .s1pr, #root .l4ca.compact .cols .s1pr { display: block; position: relative; left: 0; right: 0; top: 0; width: 100%; padding-left: 0; padding-right: 0; /*line-height: var(--main_lh_l);*/ }
	#root .l4ca:not(.compact) footer { margin-bottom: calc(var(--pt) - var(--mr_i)); }
	#root .l4ca footer > * { margin-right: 14px; margin-bottom: var(--mr_i); padding-left: 0; padding-right: 0; }
	.l4ca footer .s1pr { text-align: var(--text_align_end); }
	.l4ca footer p a { display: block; color: var(--coal); }
	.l4ca footer p a i { display: inline-block; position: relative; top: .15em; margin-right: 4px; line-height: 1px; }
	.l4ca:not(.compact, .summary) + *, .l4ca:not(.compact, .summary) + h1, .l4ca:not(.compact, .summary) + h2, .l4ca:not(.compact, .summary) + h3, .l4ca:not(.compact, .summary) + h4, .l4ca:not(.compact, .summary) + h5, .l4ca:not(.compact, .summary) + h6 { margin-top: 0; padding-top: 25px; border-top: 1px solid var(--custom_bd); }
	.l4ca .has-l4ca:not(li) + * { margin-top: 0; padding-top: 0; border-top-width: 0; }
	.l4ca:not(.compact, .summary) + .l4ca { margin-top: calc(0px - var(--main_mr)); padding-top: 0; border-top-width: 0; }
	.l4ca:last-child { margin-bottom: 9px; }
	/*.l4ca li.align-middle {}*/
	.l4ca li.align-middle > section:first-child { width: 100%; }
	.l4ca li.align-middle figure ~ *, #root .l4ca li.align-middle figure ~ .s1pr { margin-left: auto; margin-right: 0; }
	.l4ca li.align-middle h1:last-child, .l4ca li.align-middle h2:last-child, .l4ca li.align-middle h3:last-child, .l4ca li.align-middle h4:last-child, .l4ca li.align-middle h5:last-child, .l4ca li.align-middle h6:last-child { margin-bottom: var(--pt); }
	/*.l4ca.compact {}*/
	.l4ca.compact footer { /*margin-bottom: 0;*/ padding-top: 0; }
	.l4ca.compact figure ~ * { width: 100%; }
	.l4ca.compact figure ~ footer { width: calc(100% + 14px); }
	.l4ca.compact p { margin-bottom: 6px; }
	.l4ca.compact .cols { display: block; position: static; margin-bottom: 2px; }
	.l4ca.compact .cols > * { float: none; }
	.l4ca.summary { margin-bottom: 0; }
	.l4ca.summary li { padding-right: 0; }
	.l4ca.summary section { margin-bottom: 10px; padding-bottom: .1px; }
	.l4ca.summary h1, .l4ca.summary h2, .l4ca.summary h3, .l4ca.summary h4, .l4ca.summary h5, .l4ca.summary h6 { padding-left: 0 !important; padding-right: 0 !important; }
	#root .l4ca.summary .s1pr { padding-left: 0; padding-right: 0; }
	.l4cl { overflow-x: auto; overflow-y: hidden; margin-top: 0; margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: 16px; flex-wrap: nowrap; --li_w: min(220px, 75vw); --li_b: var(--dist_a); --w2: 50%; }
	.l4cl .swiper-outer { touch-action: auto; }
	.l4cl:not([style*="--dist_a"]) { --li_b: 16px; }
	#root .l4cl.mobile-compact li { width: var(--li_w); }
	#root .l4cl li { width: var(--li_w); margin-bottom: var(--dist_a); flex-shrink: 0; }
	#root .l4cl li.has-text { width: auto !important; }
	#root .l4cl.s4wi .li { border-left-width: var(--li_b); }
	#root .l4cl.s4wi[style*="--dist_a: 0"] .li { --li_b: 0px; }
	[dir="ltr"] #root .l4cl:not(.mobile-scroll, .mobile-wide) > li:first-child, [dir="ltr"] #root .l4cl.s4wi:not(.mobile-scroll, .mobile-wide) .swiper-slide:first-child .li, [dir="ltr"] #root .l4cl.mobile-compact:not(.mobile-scroll, .mobile-wide) > li:first-child, [dir="ltr"] #root .l4cl.mobile-compact.s4wi:not(.mobile-scroll, .mobile-wide) .swiper-slide:first-child .li { width: calc(var(--li_w) - var(--li_b) + var(--rpp)); border-left-width: var(--rpp); }
	[dir="ltr"] #root .l4cl:not(.mobile-scroll, .mobile-wide) > li:last-child, [dir="ltr"] #root .l4cl.s4wi:not(.mobile-scroll, .mobile-wide) .swiper-slide:last-child .li, [dir="ltr"] #root .l4cl.mobile-compact:not(.mobile-scroll, .mobile-wide) > li:last-child, [dir="ltr"] #root .l4cl.mobile-compact.s4wi:not(.mobile-scroll, .mobile-wide) .swiper-slide:last-child .li { width: calc(var(--li_w) + var(--rpp)); border-right-width: var(--rpp); }
	[dir="ltr"] #root .l4cl.mobile-scroll > li:last-child, [dir="ltr"] #root .l4cl.s4wi.mobile-scroll .swiper-slide:last-child .li, [dir="ltr"] #root .l4cl.mobile-compact.mobile-scroll > li:last-child, [dir="ltr"] #root .l4cl.mobile-compact.s4wi.mobile-scroll .swiper-slide:last-child .li { border-right-width: 0; }
	#root #content .l4cl.auto-width:not(.mobile-scroll, .mobile-wide) > li, #root #content .l4cl.auto-width:not(.mobile-scroll, .mobile-wide) .swiper-slide, #root #content .l4cl.auto-width:not(.mobile-scroll, .mobile-wide) .swiper-slide .li { width: auto !important; }
	.l4cl + p, .l4ft + p, .l4ne + p { margin-top: -6px; }
	.l4cl + .n6pg { margin-top: -17px; }
	.l4cl + .link-btn { margin-top: -12px; }
	#root .l4cl[style*="--dist_a: 0px"] + .link-btn { margin-top: 0; }
	/*.cols + .l4cl, .cols + .l4ne { margin-top: 10px; }*/
	.cols + *:has(>.l4cl:first-child), .cols + *:has(>.l4ne:first-child) { margin-top: 10px; }
	.l4cl figure { margin-bottom: var(--dist_a); }
	.l4cl figure .text-overlay { font-size: var(--size_18_f); }
	.l4cl figure + h1, .l4cl figure + h2, .l4cl figure + h3, .l4cl figure + h4, .l4cl figure + h5, .l4cl figure + h6, .l4cl figure + div { margin-top: calc(0px - var(--dist_a) + 14px); }
	.l4cl.fullwidth figure .s1lb:not(:has(.wide)), .l4ft.fullwidth figure .s1lb:not(:has(.wide)), .l4ft.fullwidth-m figure .s1lb:not(:has(.wide)) { --label_dist: var(--rpp); }
	.l4cl .info { margin-bottom: 0; }
	.l4cl .link-btn.wide:not(.text-start, .text-end) > * { width: 100%; }
	.l4cl .link-btn.wide { width: calc(100% + var(--btn_dist)); }
	#root .l4cl figure picture .swiper-button-nav, #root .l4cl:not(.category) figure .link-btn { display: none !important; /*top: 0; bottom: 0;*/ }
	#root .l4cl figure .check.color { /*min-height: var(--input_h);*/ margin-top: var(--check_color_space); margin-bottom: calc(0px - var(--check_color_space)); padding-top: 0; }
	.l4cl:not(.list, .hr) figure.overlay-static form { padding-top: 0; }
	/*.l4cl li > .cols > *:first-child figure { width: auto; }*/
	#root .l4cl figure form.align-stretch.mobile-only { position: absolute; left: 0; right: 0; bottom: 0; }
	.l4cl.hr { margin-left: 0; margin-right: 0; }
	.l4cl.hr .submit { max-width: none; }
	#root .l4cl.mobile-scroll { overflow: visible; margin-left: calc(0px - var(--dist_a)); margin-right: 0; flex-wrap: wrap; }
	#root .f8sr ~ .l4cl.mobile-scroll.w25:not(.list, .w100-mobile), #root .f8sr ~ .l4cl.mobile-scroll.w50-mobile:not(.list) { --w2: 50%; }
	#root .f8sr ~ .l4cl.mobile-scroll.w33:not(.list, .w50-mobile), #root .f8sr ~ .l4cl.mobile-scroll.w100-mobile:not(.list) { --w2: 100%; }
	#root .l4cl.mobile-scroll > .swiper-outer > .swiper-wrapper { flex-wrap: wrap; }
	#root .l4cl.mobile-scroll .swiper-wrapper .swiper-slide, #root .l4cl.mobile-scroll .swiper-wrapper .li, #root .l4cl.mobile-scroll .swiper-wrapper .li li { width: 100% !important; }
	#root .l4cl.mobile-scroll .swiper-wrapper .check li { width: auto !important; }
	#root .l4cl.mobile-scroll > li, #root #content .l4cl.mobile-scroll > li { width: 50%; min-width: 0; max-width: none; margin-bottom: var(--dist_a); border-left-width: var(--dist_a); border-right-width: 0; }
	#root .l4cl.mobile-scroll > li, #root .l4cl.mobile-scroll > .swiper-outer > .swiper-wrapper > .swiper-slide { width: var(--w2) !important; margin-left: 0 !important; margin-right: 0 !important; }
	.l4cl.hr.l4cl-banner { margin-right: var(--pd); }
	.l4cl.w25 { --w2: 25%; }
	.l4cl.w33 { --w2: 33.3333333333%; }
	.l4cl.w50 { --w2: 50%; }
	.l4cl.w25-mobile { --w2: 25%; }
	.l4cl.w50-mobile { --w2: 50%; }
	.l4cl.w100-mobile { --w2: 100%; --li_w: calc(100vw - var(--rpp) * 2); }
	.l4cl.list, .l4cl.w100 { --w2: 100%; }
	/*.l4cl.inline {}*/
	#root .l4cl.inline li { width: auto; min-width: inherit; max-width: none; }
	.l4cl.inline img { max-width: none !important; }
	.l4cl.hr:not(.mobile-compact), #root .m6ac .l4cl.hr:not(.mobile-compact) { display: block; overflow: visible; max-height: none; margin-left: 0; }
	#root .l4cl.hr:not(.mobile-compact) li, #root .l4cl.hr:not(.mobile-compact) li:first-child, #root .l4cl.hr:not(.mobile-compact) li:last-child { width: 100%; min-width: 0; max-width: none; border-left-width: 0; border-right-width: 0; }
	#root .l4cl.hr:not(.mobile-compact) li:first-child:before { display: block; }
	.l4cl.mobile-compact { --li_w: min(220px, 75vw); }
	.l4cl.hr.mobile-compact { --li_w: 300px; margin-left: var(--rpn); margin-right: var(--rpn); }
	.l4cl.category { --li_w: min(170px, 75vw); }
	.l4cl.list { --img_w: 120px; }
	.l4cl.list li { display: block; --pd: 18px; }
	#root .l4ca section header .s1pr { width: auto; }
	#root .l4cl.list li > * { padding-left: 0; padding-right: 0; }
	#root .l4cl.list li > .link-btn { margin-right: -16px; }
	#root .l4cl.list figure { float: var(--text_align_start); margin-top: 0; }
	#root .l4cl.list figure ~ * { float: var(--text_align_end); clear: var(--text_align_end); width: calc(100% - var(--img_w) - var(--img_dist)); }
	.l4cl.small { --li_w: 75px; --li_b: var(--dist_a); }
	.l4cl.s4wi { margin-left: var(--rpn); }
	.l4cl.s4wi .swiper-wrapper {
		height: auto !important;
		transform: none !important;
	}
	.l4cl.s4wi .swiper-wrapper, .l4cl.s4wi .swiper-slide { align-items: stretch; }
	.l4cl.s4wi.align-center .swiper-wrapper, .l4cl.s4wi.align-center .swiper-slide { align-items: center; }
	.l4cl.s4wi .swiper-slide { display: flex; flex-wrap: wrap; width: auto !important; margin: 0 /*0 0 var(--li_b)*/ !important; }
	/*.l4cl.s4wi .swiper-slide:last-child, [dir="rtl"] .l4cl.s4wi .swiper-slide:first-child { padding-right: var(--li_b) !important; }*/
	#root .l4cl .swiper-slide-duplicate, #root .l4cl .swiper-button-nav { display: none; }
	#root .l4cl picture .swiper-button-nav { display: block; }
	.l4cl.wide, #root .l4cl.wide { overflow-x: auto; overflow-y: hidden; width: auto; margin-left: var(--rpn); margin-right: var(--rpn); --img_w: 46px; --img_dist: var(--rpp); }
	#root .l4cl.wide li { display: block; width: 298px; min-width: 298px; max-width: 298px; min-height: calc(var(--rpp) * 2 + 44px); padding: 12px 65px 6px 12px; border-right-width: var(--rpp); border-left-width: 0; }
	#root .l4cl.wide li:first-child { width: 314px; min-width: 314px; max-width: 314px; border-left-width: var(--rpp); }
	#root .l4cl.wide figure { float: var(--text_align_start); margin-bottom: 6px; }
	#root .l4cl.wide figure ~ *:not(.link-btn) { clear: var(--text_align_end); float: var(--text_align_end); width: calc(100% - var(--img_w) - var(--rpp)); }
	#root .l4cl.wide li > div { max-width: none; margin: 0; }
	#root .l4cl.wide .s1pr { margin-top: 1px; margin-left: 0; margin-right: 0; padding-left: 0; padding-right: 0; }
	.l4cl.wide .link-btn { position: absolute; left: auto; right: var(--rpp); top: auto; bottom: var(--rpp); margin-right: 0; padding: 0; }
	.l4cl.wide .link-btn a { margin: 0; }
	#root .l4cl.mobile-wide { overflow: visible; margin-left: var(--rpn); margin-right: 0; }
	#root .l4cl.mobile-wide li, #root .l4cl.mobile-wide li:first-child, #root .l4cl.mobile-wide li:last-child, #root .l4cl.mobile-wide .swiper-slide:last-child .li, #root .l4cl.mobile-wide .swiper-slide { width: 50%; min-width: 0; max-width: none; margin-bottom: 25px; border: 0 solid rgba(0,0,0,0); border-left-width: var(--rpp); }
	#root .l4cl.mobile-wide.s4wi .swiper-slide { width: 50% !important; }
	#root .l4cl.mobile-wide.s4wi .swiper-slide li { width: 100%; border-width: 0; }
	#root .l4cl.mobile-wide.list li, #root .l4cl.mobile-wide.list li:first-child, #root .l4cl.mobile-wide.list li:last-child { width: 100%; margin: 0; }
	.l4cl.mobile-wide.s4wi .swiper-wrapper {
		overflow: visible;
		display: flex; flex-wrap: wrap;
	}
	.l4cl.mobile-wide { flex-wrap: wrap; }
	.l4cl.size-16 { font-size: var(--main_fz); }
	#root .l4cl h1[class*="m"], #root .l4cl h2[class*="m"], #root .l4cl h3[class*="m"], #root .l4cl h4[class*="m"], #root .l4cl h5[class*="m"], #root .l4cl h6[class*="m"] { margin-bottom: var(--main_mr_h); }
	#root .l4cl h1[class*="size-"], #root .l4cl h2[class*="size-"], #root .l4cl h3[class*="size-"], #root .l4cl h4[class*="size-"], #root .l4cl h5[class*="size-"], #root .l4cl h6[class*="size-"] { font-size: var(--mob_h2); }
	.l4cu { margin: 0 0 0 var(--rpn); }
	.l4cu li { border-left-width: var(--rpp); }
	.l4cu li > span { margin-bottom: 8px; font-size: var(--size_24_f); }
	/*.l4cu.box {}*/
	.l4cu.box li { width: 50%; max-width: none; flex-basis: auto; }
	.l4cu.box li + li { margin-top: -16px; }
	.l4cu.box li:first-child + li { margin-top: 0; }
	.l4ft { display: block; height: auto !important; margin-top: 20px; margin-bottom: 32px; /*--pt: 40px; --pb: 28px;*/ --ps: var(--rpp); --mih: 40vw; }
	#root .l4ft li, #root .l4ft.cols li { position: relative !important; left: 0 !important; right: 0 !important; top: 0 !important; float: none; width: auto !important; min-height: var(--mih); margin: 0 0 var(--dist_a); padding: 0; }
	#root .l4ft.fullwidth li, #root .l4ft.cols.fullwidth li, #root .l4ft.fullwidth-m li, #root .l4ft.cols.fullwidth-m li { margin-left: var(--rpn); margin-right: var(--rpn); }
	#root .l4ft.fullwidth:not(.mobile-compact) li:before, #root .l4ft.fullwidth:not(.mobile-compact) figure, #root .l4ft.fullwidth:not(.mobile-compact) .img-overlay, #root .l4ft.fullwidth:not(.mobile-compact) svg, #root .l4ft.fullwidth:not(.mobile-compact) picture, #root .l4ft.fullwidth:not(.mobile-compact) video, #root .l4ft.fullwidth:not(.mobile-compact) iframe, .l4ft.fullwidth:not(.mobile-compact) li > .main:before, #root .l4ft.fullwidth-m:not(.mobile-compact) li:before, #root .l4ft.fullwidth-m:not(.mobile-compact) figure, #root .l4ft.fullwidth-m:not(.mobile-compact) .img-overlay, #root .l4ft.fullwidth-m:not(.mobile-compact) svg, #root .l4ft.fullwidth-m:not(.mobile-compact) picture, #root .l4ft.fullwidth-m:not(.mobile-compact) video, #root .l4ft.fullwidth-m:not(.mobile-compact) iframe, .l4ft.fullwidth-m:not(.mobile-compact) li > .main:before { border-radius: 0; }
	#root .l4ft li { transform: none !important; }
	.l4ft + p, .l4ft + .l4ft { margin-top: -16px; }
	#root .l4ft.size-xs li, #root .l4ft .size-xs { --mih: 50vw; }
	#root .l4ft.size-s li, #root .l4ft .size-s { --mih: 75vw; }
	#root .l4ft.size-m li, #root .l4ft .size-m { --mih: 100vw; }
	#root .l4ft.size-l li, #root .l4ft .size-l { --mih: 125vw; }
	#root .l4ft[class*="size-"].size-xs-mobile li, #root .l4ft li[class*="size-"].size-xs-mobile { --mih: 50vw !important; }
	#root .l4ft[class*="size-"].size-s-mobile li, #root .l4ft li[class*="size-"].size-s-mobile { --mih: 75vw !important; }
	#root .l4ft[class*="size-"].size-m-mobile li, #root .l4ft li[class*="size-"].size-m-mobile { --mih: 100vw !important; }
	#root .l4ft[class*="size-"].size-l-mobile li, #root .l4ft li[class*="size-"].size-l-mobile { --mih: 125vw !important; }
	.l4ft figure:has(.s1lb) + div { --pt: calc(var(--ps) * 2); }
	.cols + .l4ft { margin-top: 0; }
	.l4ft.cols { display: block; }
	.l4ft li.overlay { width: auto !important; }
	.l4ft.cols li > div, .l4ft li.overlay > div { position: relative; }
	.l4ft.cols li > img, .l4ft.cols li > picture, .l4ft.cols li > a > img, .l4ft.cols li > a > picture, .l4ft li.overlay > img, .l4ft li.overlay > picture, .l4ft li.overlay > figure, .l4ft li.overlay > a > img, .l4ft li.overlay > a > picture, .l4ft.cols li > figure { position: absolute; left: 0; right: 0; top: 0; bottom: 0; width: 100% !important; height: 100% !important; }
	#root .l4ft.cols li > img, #root .l4ft.cols li > a > img, #root .l4ft li.overlay > img, #root .l4ft li.overlay > a > img { height: 100% !important; }
	.l4ft.cols li > img:first-child:last-child, .l4ft.cols li > picture:first-child:last-child, .l4ft.cols li > a:first-child:last-child > img, .l4ft.cols li > a:first-child:last-child > picture, .l4ft li.overlay > img:first-child:last-child, .l4ft li.overlay > picture:first-child:last-child, .l4ft li.overlay > a:first-child:last-child > img, .l4ft li.overlay > a:first-child:last-child > picture, .l4ft.cols li > figure:first-child:last-child, .l4ft li.overlay > figure:first-child:last-child { position: relative; height: auto !important; }
	/*.l4ft.s4wi {}*/
	.l4ft:not(.mobile-compact) .swiper-wrapper, .l4ft:not(.mobile-compact) .swiper-wrapper, .l4ft .swiper-slide { display: block !important; overflow: visible !important; width: auto !important; height: auto !important; margin: 0 !important; transform: none !important; }
	.l4ft .swiper-custom-pagination { display: none !important; }
	/*.l4hs {}*/
	#root .l4hs > li[style*="horizontal_mobile"] { left: var(--horizontal_mobile); }
	#root .l4hs > li[style*="vertical_mobile"] { top: var(--vertical_mobile); }
	.l4in.slider { margin-left: var(--rpn); margin-right: var(--rpn); }
	.l4in.slider li, .l4in.slider li:first-child { border-left-width: var(--rpp); border-right-width: 0; }
	.l4in.slider li:last-child { border-right-width: var(--rpp); }
	.l4ne, .l4ne.featured, #root .l4ne.featured { overflow-x: auto; overflow-y: hidden; margin-right: var(--rpn); margin-left: var(--rpn); margin-bottom: 16px; padding-left: 0; padding-right: 0; --li_w: 224px; --li_d: 16px; }
	.l4ne li {
		margin-bottom: 3px; border-left-width: var(--li_d);
		flex-shrink: 0;
	}
	.l4ne:not(.l4ne-figure-before) li { width: var(--li_w); }
	[dir="ltr"] .l4ne > li:first-child, [dir="ltr"] .l4ne > li.mobile-hide:first-child + li { width: calc(var(--li_w) - var(--li_d) + var(--rpp)); border-left-width: var(--rpp); }
	[dir="ltr"] .l4ne > li:last-child { width: calc(var(--li_w) + var(--rpp)); border-right-width: var(--rpp); }
	.l4ne figure { margin-bottom: 10px; }
	.l4ne h1 + p.mobile-hide + *, .l4ne h2 + p.mobile-hide + *, .l4ne h3 + p.mobile-hide + *, .l4ne h4 + p.mobile-hide + *, .l4ne h5 + p.mobile-hide + *, .l4ne h6 + p.mobile-hide + * { margin-top: 0; }
	.l4ne h1, .l4ne h2, .l4ne h3, .l4ne h4, .l4ne h5, .l4ne h6, .l4ne.featured h1, .l4ne.featured h2, .l4ne.featured h3, .l4ne.featured h4, .l4ne.featured h5, .l4ne.featured h6 { margin-bottom: 3px; /*font-size: var(--main_h_small);*/ }
	.l4ne h1 .small, .l4ne h2 .small, .l4ne h3 .small, .l4ne h4 .small, .l4ne h5 .small, .l4ne h6 .small, #root .l4ne.featured h1 .small, #root .l4ne.featured h2 .small, #root .l4ne.featured h3 .small, #root .l4ne.featured h4 .small, #root .l4ne.featured h5 .small, #root .l4ne.featured h6 .small { margin-bottom: 6px; font-size: var(--main_fz_small); text-transform: var(--main_tt); }
	#root .l4ne.wide { overflow: visible; margin-left: var(--rpn); margin-right: 0; }
	#root .l4ne.wide li { width: 50%; min-width: 0; max-width: none; margin-top: 24px; border-right-width: 0; }
	#root .l4ne.wide li:first-child, #root .l4ne.wide li:first-child + li { margin-top: 0; }
	.l4pm { --dist: 22px; }
	.l4pr { max-width: none; margin-left: 0; margin-right: 0; }
	#root .l4pr, #root .m6pr .l4pr, #root .l4pr.s4wi, #root .m6pr .l4pr.s4wi { border-left-width: 0; border-right-width: 0; }
	.l4pr.s4wi .li { border: 0 solid rgba(0,0,0,0); }
	.m6pr .l4pr.s4wi:not(.slider-fraction) > .s1lb { left: var(--label_dist); }
	.l4pr .swiper-button-nav, .m6pr .l4pr .swiper-button-nav { overflow: visible; width: var(--rpp); font-size: 8px; }
	.l4pr .swiper-button-nav:after, .m6pr .l4pr .swiper-button-nav:after { left: -20px; right: -20px; }
	.l4pr li > span, .l4pr li a > span, .l4pr .swiper-outer > .label { left: 10px; top: 10px; }
	.l4pr .swiper-button-next { left: auto; right: var(--rpn); }
	.l4pr .swiper-button-prev { right: auto; left: var(--rpn); }
	.l4pr .swiper-pagination-bullets { margin-right: -7px; }
	#root .l4pr.aside-pager.s4wi { padding-left: 0; padding-right: 0; }
	.l4pr.s4wi:not(.no-thumbs-mobile) .swiper-button-next { right: 16px; }
	.l4pr.s4wi:not(.no-thumbs-mobile) .swiper-button-prev { left: 16px; }
	.l4pr.s4wi:not(.no-thumbs-mobile) .swiper-button-nav { width: 44px; color: var(--coal); font-size: var(--size_12_f); }
	.l4pr.s4wi:not(.no-thumbs-mobile) .swiper-button-nav:after { top: 50%; bottom: auto; height: 44px; margin-top: -22px; background: var(--white); opacity: .7; }
	.l4pr.s4wi:not(.no-thumbs-mobile) .swiper-button-nav:after { border-radius: 48px; }
	.l4pr:not(.no-thumbs-mobile) .swiper-button-nav:after, .m6pr .l4pr:not(.no-thumbs-mobile) .swiper-button-nav:after { left: 0; right: 0; }
	/*.l4pr.no-thumbs-mobile {}*/
	.l4pr.no-thumbs-mobile .swiper-custom-pagination, .l4pr .swiper-pagination-bullets { margin-top: var(--rpp); }
	.l4pr.no-thumbs-mobile.s4wi .li, .l4pr.slider-fraction.s4wi .li { padding-left: 0; padding-right: 0; border-left-width: 0; border-right-width: 0; }
	.l4pr.no-thumbs-mobile .swiper-custom-fraction { display: block; width: auto; }
	#root .l4pr.no-thumbs-mobile .swiper-button-nav { display: block; position: relative; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; width: 27px; height: 30px !important; color: var(--coal); font-size: var(--size_12_f); }
	.l4pr.no-thumbs-mobile .swiper-button-prev:before { content: "\e96a"; }
	.l4pr.no-thumbs-mobile .swiper-button-next:before { content: "\e96b"; }
	#root .l4pr.no-thumbs-mobile .swiper-button-nav:after { right: -9px; left: -9px; top: -7px; bottom: -7px; }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] > header .r6rt, .shopify-section:first-child .m6pr[style*="--m6pr_bg"] .img-multiply:before,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] > header .r6rt, .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .img-multiply:before { --body_bg: var(--m6pr_bg); --bg_dist: max(-400px, -100vh); }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr:before, .shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination:before,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr:before, .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination:before { content: ""; display: block; position: absolute; left: var(--rpn); right: var(--rpn); top: /*calc(0px - var(--header_outer_height))*/ var(--bg_dist); bottom: 0; z-index: -1; background: var(--m6pr_bg); }
	.shopify-section-announcement-bar ~ #root .shopify-section:first-child .m6pr[style*="--m6pr_bg"]:not([style*="--bg_dist:"]) .l4pr:before, .shopify-section-announcement-bar ~ #root .shopify-section:first-child .m6pr[style*="--m6pr_bg"]:not([style*="--bg_dist:"]) .l4pr .swiper-custom-pagination:before,
	.shopify-section-announcement-bar ~ #root .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"]:not([style*="--bg_dist:"]) .l4pr:before, .shopify-section-announcement-bar ~ #root .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"]:not([style*="--bg_dist:"]) .l4pr .swiper-custom-pagination:before { display: none; }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] figure.img-multiply-bg:before, .shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr picture,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] figure.img-multiply-bg:before, .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr picture { --b2r: 0px; --custom_bd: var(--m6pr_bg); }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-outer,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-outer { z-index: 3; }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-outer, .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-outer,
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr:not(.s4wi), .shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr:not(.s4wi) { padding-bottom: var(--rpp); }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination { position: relative; z-index: 3; }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination:before,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-custom-pagination:before { top: var(--rpn); background: var(--body_bg); }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr .swiper-button-nav,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr .swiper-button-nav { top: auto; bottom: calc(100% + 26px); }
	.shopify-section:first-child .m6pr[style*="--m6pr_bg"] .l4pr.no-thumbs-mobile:not(.mobile-wide) .swiper-custom-pagination,
	.shopify-section-breadcrumbs:first-child + .shopify-section .m6pr[style*="--m6pr_bg"] .l4pr.no-thumbs-mobile:not(.mobile-wide) .swiper-custom-pagination { margin-top: calc(var(--rpp) * 2); }
	.l4sc { margin-top: 17px; --dist: 30px; }
	.l4st { margin-top: 0; --iz: 36px; }
	#root .l4st { --width: 100%; }
	.l4st h1, .l4st h2, .l4st h3, .l4st h4, .l4st h5, .l4st h6 { margin: 0 0 5px; }
	.l4st h1 i, .l4st h2 i, .l4st h3 i, .l4st h4 i, .l4st h5 i, .l4st h6 i { margin-bottom: 14px; line-height: 44px; }
	.l4st .icon-truck { --iz: 30px; }
	.l4st .icon-shop { --iz: 34px; }
	.l4st .icon-app { --iz: 44px; }
	#root .l4st.w50-mobile { --width: 50%; }
	.l4ts { margin-top: 6px; margin-bottom: var(--rpp); }
	.l4ts li { width: 100%; margin-bottom: 26px; }
	.l4ts.box .swiper-slide li { margin-bottom: 0; }
	.l4ts q, .l4ts.s4wi q { max-width: none; margin-bottom: 8px; padding: 0; border-width: 0; font-size: var(--main_fz); line-height: 1.6111111111; }
	.l4ts.s4wi { margin-bottom: 6px; }
	.l4ts .swiper-pagination-bullets { margin-top: 0; }
	.l4ts.box .swiper-pagination-bullets { margin-top: 6px; }
	.l4ts.s4wi q { padding-left: 20px; padding-right: 20px; border-width: 0; }
	.l4ts .swiper-button-nav { display: block; bottom: 64px; }
	.l4ts.box .swiper-button-nav { bottom: 38px; }
	/*.l4tt {}*/
	#root .table-wrapper + .l4tt, #root table + .l4tt { margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: 32px; padding-bottom: 0; border-bottom: 1px solid var(--custom_bd); font-size: var(--main_fz_small); }
	.table-wrapper + .l4tt li, table + .l4tt li { margin: 0; padding: 10px var(--rpp); border-top: 1px solid var(--custom_bd); font-size: 1em; }
	.table-wrapper + .l4tt li:first-child, table + .l4tt li:first-child { padding-top: 0; border-top-width: 0; }
	/*.l4us {}*/
	.shopify-section > .l4us:last-child { margin-bottom: 32px; }
	.l4us + .mobile-hide + .l4us { margin-top: calc(0px - var(--main_mr)); }
	*:not(.l4us) + .l4us.mobile-hide + .l4us { margin-top: 0; }
	.l4us.wide.s4wi { margin-left: var(--rpn); margin-right: var(--rpn); }
	.l4us.wide.s4wi .li { margin-left: 0; margin-right: 0; border: 0 solid rgba(0,0,0,0); border-left-width: var(--rpp); border-right-width: var(--rpp); }
	.l4us .swiper-button-prev { left: calc(var(--rpp) - 10px); }
	.l4us .swiper-button-next { right: calc(var(--rpp) - 10px); }
	.l4us.wide.s4wi .swiper-slide { padding-left: 0; padding-right: 0; }
	.l4vw .active .icon-view-grid:before { content: "\e91b"; }
	.l4vw .active .icon-view-square:before { content: "\e98e"; }
	#root .m20, #root .margin-20 { margin-bottom: 15px; }
	#root .m30, #root .margin-30 { margin-bottom: 25px; }
	#root .m0-mobile, #root .margin-mobile-0 { margin-bottom: 0; }
	#root .m5-mobile, #root .margin-mobile-5 { margin-bottom: 5px; }
	#root .m10-mobile, #root .margin-mobile-10 { margin-bottom: 10px; }
	#root .m15-mobile, #root .margin-mobile-15 { margin-bottom: 15px; }
	#root .m20-mobile, #root .margin-mobile-20 { margin-bottom: 20px; }
	#root .m25-mobile, #root .margin-mobile-25 { margin-bottom: 25px; }
	#root .m30-mobile, #root .margin-mobile-30 { margin-bottom: 30px; }
	#root .m35-mobile, #root .margin-mobile-35 { margin-bottom: 35px; }
	#root .m40-mobile, #root .margin-mobile-40 { margin-bottom: 40px; }
	#root .m50-mobile, #root .margin-mobile-50 { margin-bottom: 50px; }
	#root .m60-mobile, #root .margin-mobile-60 { margin-bottom: 60px; }
	#root .m65-mobile, #root .margin-mobile-65 { margin-bottom: 65px; }
	#root .m70-mobile, #root .margin-mobile-70 { margin-bottom: 70px; }
	#root .m105-mobile, #root .margin-mobile-105 { margin-bottom: 105px; }
	#root .margin-mobile-content { margin-bottom: var(--main_mr); }
	#root .margin-mobile-header { margin-bottom: var(--main_mr_h); }
	.m6as { display: block; margin-top: 26px; --pd: var(--rpp); }
	.m6as:before { bottom: 0; }
	#root .m6as > div { min-height: 0; }
	#root .m6as > * { width: auto; padding-left: 0; padding-right: 0; }
	#root .m6as > figure { margin-top: 0; margin-bottom: var(--main_mr); }
	#root .m6as.overlay > figure { margin-bottom: 42px; }
	#root .m6as:not(.overlay) > .mobile-wide { width: calc(100% + var(--rpp) * 2); margin-left: var(--rpn); margin-right: var(--rpn); border-radius: 0; }
	.m6as:not(.overlay) > .mobile-wide > figure > picture, .m6as:not(.overlay) > .mobile-wide > figure > picture img, .m6as:not(.overlay) figure.mobile-wide > picture, .m6as:not(.overlay) figure.mobile-wide > picture img, .m6as:not(.overlay) figure.mobile-wide .img-overlay { --b2p: 0px; }
	#root .m6as figure.no-img { padding-top: 50%; }
	#root .m6as > figure, #root .m6as.overlay > figure { padding: 0; }
	.m6as > figure.mobile-half:has(.l4hs.static) { display: block; }
	.m6as > figure.mobile-half .l4hs.static { padding-top: 15vw; padding-bottom: 0; }
	#root .m6as > figure.mobile-half .l4hs.static > li { width: 100%; }
	#root .m6as > figure.mobile-half .l4hs.static > li > div { margin-left: auto; margin-right: auto; }
	#root .m6as > figure.mobile-half:has(.l4hs.static) > picture, #root .m6as > figure.mobile-half:has(.l4hs.static) > .img-overlay, #root .m6as > figure.mobile-half:has(.l4hs.static) > .link-overlay, #root .m6as > figure.mobile-half:has(.l4hs.static) > a > picture, #root .m6as > figure.mobile-half:has(.l4hs.static) > a > .img-overlay, #root .m6as > figure.mobile-half:has(.l4hs.static) > a > .link-overlay { bottom: 50%; height: auto !important; }
	.m6as p + p > a.strong:first-child, .m6as p + a.strong, .m6ac p + p > a.strong:first-child, .m6ac p + a.strong { margin-top: -8px; }
	.m6as.strict-height { --hdef: var(--mih); }
	/*.m6as.inv {}*/
	.m6as.inv > figure { margin-top: 6px; }
	/*.m6as.overlay {}*/
	#root .m6as.overlay > * { padding: 0 var(--pd) max(0.1px, calc(42px - var(--main_mr))); }
	#root .m6as.overlay > div { min-height: 0; }
	/*.m6as.wide {}*/
	.m6as.wide > figure { margin-left: var(--rpn); margin-right: var(--rpn); }
	.m6as.wide > figure img, .m6as.wide > figure video, .m6as.wide > figure svg, .m6as.wide > figure iframe { border-radius: 0; }
	.m6ac { margin-top: 26px; margin-bottom: 32px; }
	.m6ac, .m6ac .m6pr-compact { display: block; }
	#root .m6ac > *, #root .m6ac .m6pr-compact > * { width: 100%; }
	.m6ac .m6pr-compact { margin-bottom: -24px; }
	.m6ac .l4cl:last-child { margin-bottom: /*0*/ 22px; }
	.m6ac.mobile-inv .l4cl.hr { margin-top: -14px; }
	.m6ac .l4cl.hr:last-child, .m6ac .l4cl.mobile-compact:last-child { margin-bottom: 16px; }
	.m6ac.mobile-inv .l4cl.hr:last-child { margin-bottom: 0; }
	.m6ac > *:not(:last-child) .l4ft:last-child { margin-bottom: 0; }
	.m6bx { padding-left: var(--rpp); padding-right: var(--rpp); }
	.m6bx > .size-20 { margin-bottom: 8px; font-size: var(--mob_h3); }
	.m6bx > .link-btn:last-child, .m6bx > .submit:last-child, .m6bx .l4cn.box:last-child { margin-bottom: 3px; }
	.m6bx + h1, .m6bx + h2, .m6bx + h3, .m6bx + h4, .m6bx + h5, .m6bx + h6 { margin-top: 32px; }
	/* f8fl-mobile.css */
	.m6bx.size-s { padding-top: 34px; padding-bottom: calc(34px - var(--main_mr)); }
	.m6bx.size-m { padding-top: 42px; padding-bottom: calc(42px - var(--main_mr)); }
	.m6bx.size-l { padding-top: 76px; padding-bottom: calc(76px - var(--main_mr)); }
	.m6cu { margin: 32px 0; --p2: 20px; }
	#root .m6cu { padding-left: var(--rpp); padding-right: var(--rpp);}
	#root .m6cu.wide { margin-left: 0; padding-left: 0; padding-right: 0; }
	.m6cu > * { border-left-width: 0; }
	.m6cu.size-xs-mobile, .m6as.size-xs-mobile { --mih: 170px; }
	.m6cu.size-s-mobile, .m6as.size-s-mobile { --mih: 260px; }
	.m6cu.size-m-mobile, .m6as.size-m-mobile { --mih: 390px; }
	.m6cu.size-l-mobile, .m6as.size-l-mobile { --mih: 520px; }
	.m6cu.size-xl-mobile, .m6as.size-xl-mobile { --mih: 700px; }
	.m6fr { margin-bottom: 32px; --pdc: 42px; }
	#root .m6fr figure {
		flex-direction: column; flex-direction: column;
	}
	#root .m6fr figure picture[style*="--size"], #root .m6fr figure video[style*="--size"] { height: var(--size) !important; --size: 50% !important; }
	.m6fr article > div[style]:not(.media-flexible) {
		position: relative !important; left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: auto !important; max-width: none !important; margin: 0 !important; opacity: 1 !important;
		transform: none !important;
	}
	.m6fr.wide { margin-left: var(--rpn); margin-right: var(--rpn); --pda: var(--rpp); --pdc: 42px; --mih: 220px; }
	.m6fr.wide.s4wi .swiper-outer { margin-left: 0; margin-right: 0; }
	.m6fr.wide.s4wi .swiper-slide { padding-left: 0; padding-right: 0; }
	.m6fr.wide article.background-wide > figure, .m6fr.wide article.background-wide > .media-flexible { left: var(--rpp); right: var(--rpp); }
	#root .m6fr { margin-bottom: 32px; }
	.m6fr article { height: 100%; }
	.m6fr.mobile-text-start article, .m6fr article.mobile-text-start { text-align: var(--text_align_start); --justify_content: flex-start; }
	.m6fr.mobile-text-end article, .m6fr article.mobile-text-end { text-align: var(--text_align_end); --justify_content: flex-end; }
	.m6fr.mobile-text-start article .link-btn, .m6fr article.mobile-text-start .link-btn { justify-content: var(--justify_content); --justify_content: flex-start; }
	.m6fr.mobile-text-end article .link-btn, .m6fr article.mobile-text-end .link-btn { justify-content: var(--justify_content); --justify_content: flex-end; }
	#root .m6fr article.aside { display: block; padding-left: var(--rpp); padding-right: var(--rpp); --mih: 0px !important; }
	.m6fr .link-btn { margin-top: 0; }
	.m6fr.wide article > figure, .m6fr.wide article:before { left: 0; right: 0; }
	.m6fr.wide article > figure .link-btn.visible, .m6fr article > .link-btn { --label_dist: var(--rpp); }
	.m6fr article.aside > div, .m6fr.wide article.aside > div { min-height: 0; padding-bottom: calc(var(--pdc) - var(--main_mr)); }
	.m6fr article.aside > * { max-width: none; }
	#root .m6fr article.aside figure { position: relative; left: 0; right: 0; margin-left: var(--rpn); margin-right: var(--rpn); margin-bottom: auto; /*border-bottom: 42px solid rgba(0,0,0,0);*/ }
	#root .m6fr:not(.wide, .s4wi) article.aside figure picture, #root .m6fr:not(.wide, .s4wi) article.aside figure .img-overlay, #root .m6fr:not(.wide, .s4wi) article.aside.inv figure picture, #root .m6fr:not(.wide, .s4wi) article.aside.inv figure .img-overlay { overflow: hidden; border-radius: var(--b2p) var(--b2p) 0 0;  }
	#root .m6fr:not(.wide, .s4wi) article.aside figure *, #root .m6fr:not(.wide, .s4wi) article.aside.inv figure picture * { border-radius: 0; }
	#root .m6fr.s4wi article.aside figure { margin-left: var(--rpn); margin-right: var(--rpn); }
	.m6fr article.aside img, .m6fr article.aside picture { height: auto !important; }
	/*#root .m6fr article.aside > figure:first-child { margin-top: calc(0px - var(--pdc)); }*/
	#root .m6fr.wide article.aside > *, #root .m6fr article.aside > * { width: auto; min-width: 100%; padding-left: 0; padding-right: 0; }
	#root .m6fr.wide article.aside > figure, #root .m6fr article.aside > figure { left: 0 !important; right: 0 !important; width: calc(100% + var(--rpp) + var(--rpp)); }
	#root .m6fr.wide article.aside > .link-btn, #root .m6fr article.aside > .link-btn { min-width: calc(100% + 16px); }
	#root .m6fr article.aside > figure { height: 75vw; box-sizing: content-box; }
	#root .m6fr article.aside.size-xs-mobile > figure, #root .m6fr.size-xs-mobile article.aside > figure { height: 50vw; }
	#root .m6fr article.aside.size-s-mobile > figure, #root .m6fr.size-s-mobile article.aside > figure { height: 75vw; }
	#root .m6fr article.aside.size-m-mobile > figure, #root .m6fr.size-m-mobile article.aside > figure { height: 100vw; }
	#root .m6fr article.aside.size-l-mobile > figure, #root .m6fr.size-l-mobile article.aside > figure { height: 125vw; }
	#root .m6fr article.aside.size-auto > figure, #root .m6fr.size-auto article.aside > figure { height: auto; }
	.m6fr.wide + .m6wd, .m6wd + .m6fr.wide { margin-top: -32px; }
	html:not(.resized) .m6fr .swiper-slide { align-self: stretch; }
	.m6fr .swiper-slide {
		display: flex; flex-wrap: wrap;
		flex-direction: column;
		justify-content: flex-end;
	}
	.m6fr .swiper-slide.has-aside { display: block;align-self: flex-start; }
	.m6fr .swiper-pagination-bullets { bottom: 10px; }
	.m6fr.s4wi article.aside/*, .m6fr.s4wi .swiper-slide article > div, .m6fr.wide.s4wi article.aside > div*/ { padding-bottom: 42px; }
	.m6fr .play-pause:before { bottom: 7px; font-size: 14px; }
	.m6fr.slider-fraction .swiper-custom-pagination { bottom: 6px; }
	#root .m6fr[data-autoplay]:not(.slider-fraction) .swiper-button-nav { display: none; }
	/* media-flexible */
	#root .m6fr article.mobile-static { min-height: 0; }
	/*#root .m6fr article.mobile-static [class*="media-flexible"] { margin-bottom: var(--main_mr); }*/
	#root .m6fr article.mobile-static [class*="media-flexible"] .swiper-slide { display: block; align-self: flex-start; }
	#root .media-flexible-mobile figure { position: relative; }
	#root .m6fr.wide article.mobile-static figure, #root .m6fr.wide article.mobile-static picture, #root .m6fr.wide article.mobile-static img, #root .m6fr.wide article.mobile-static video, #root .m6fr.wide article.mobile-static iframe, #root .m6fr.wide article.mobile-static svg, #root .m6fr.wide article.mobile-static .img-overlay { border-radius: 0; }
	#root .m6fr article.mobile-static, #root .m6fr article.mobile-flexible-static { display: block; min-height: 0; margin-bottom: calc(var(--main_mr) - 32px); padding-top: 0; padding-bottom: 0; }
	#root .m6fr article.mobile-static > div #root .m6fr article.mobile-flexible-static > div { padding-top: 0; padding-bottom: 0; }
	#root .m6fr article.mobile-static [class*="media-flexible"], #root .m6fr article.mobile-static [class*="media-flexible"] figure {
		position: relative !important; left: 0 !important; right: 0 !important; top: 0 !important; bottom: 0 !important; width: 100% !important; max-width: none !important; height: auto !important; opacity: 1 !important;
		transform: none !important;
	}
	#root .m6fr article.mobile-static figure img, #root .m6fr article.mobile-static figure video, #root .m6fr article.mobile-static figure svg, #root .m6fr article.mobile-static figure iframe { height: var(--mhj) !important; min-height: 0 !important; object-fit: cover; }
	.m6fr article.mobile-static figure [class*="mask"] { --mhj: auto; }
	#root .m6fr article.mobile-static [class*="media-flexible"] > figure + figure { margin-top: var(--rpp) !important; }
	#root .m6fr.wide article.mobile-static [class*="media-flexible"] { width: calc(100% + var(--rpp) * 2) !important; margin-left: var(--rpn); margin-right: var(--rpn); }
	#root .m6fr:not(.wide) article.mobile-static [class*="media-flexible"].s4wi { overflow: hidden; border-radius: var(--b2r); }
	#content > .m6fr.wide:last-child article.mobile-static { margin-bottom: var(--main_mr); }
	#root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure, #root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure picture, #root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure img, #root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure video, #root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure svg, #root .m6fr article.mobile-static .s4wi[class*="media-flexible"] figure .img-overlay { border-radius: 0; }
	#root .m6fr article.mobile-flexible-static[class*="size-"][class*="-mobile"] [class*="media-flexible"] { position: relative; left: 0; right: 0; top: 0; bottom: 0; height: var(--mih); margin: 0 0 /*var(--main_mr)*/; }
	/*.m6fr.wide {}*/
	/* Rudolf */
	.m6fr.size-xs-mobile, .m6fr .size-xs-mobile { --mhj: 50vw /*15vh*/; }
	.m6fr.size-s-mobile, .m6fr .size-s-mobile { --mhj: 75vw /*20vh*/; }
	.m6fr.size-m-mobile, .m6fr .size-m-mobile { --mhj: 100vw /*25vh*/; }
	.m6fr.size-l-mobile, .m6fr .size-l-mobile { --mhj: 125vw /*30vh*/; }
	.m6fr.size-xl-mobile, .m6fr .size-xl-mobile { --mhj: calc(100vh - var(--header_height_static)); }
	.m6fr.size-xs article:not(.aside), .m6fr article.size-xs:not(.aside), .m6cu.size-xs, .m6fr.compact article:not(.aside), .m6fr article.compact:not(.aside) { --mih: 220px; }
	.m6fr.size-s article:not(.aside), .m6fr article.size-s:not(.aside), .m6cu.size-s { --mih: 370px; }
	.m6fr.size-m article:not(.aside), .m6fr article.size-m:not(.aside, .mobile-static, .mobile-flexible-static), .m6cu.size-m { --mih: 450px; }
	.m6fr.size-l article:not(.aside), .m6fr article.size-l:not(.aside), .m6cu.size-l { --mih: 520px; }
	.m6fr.size-xs-mobile article:not(.aside), .m6fr article.size-xs-mobile:not(.aside) { --mih: 220px; }
	.m6fr.size-s-mobile article:not(.aside), .m6fr article.size-s-mobile:not(.aside) { --mih: 370px; }
	.m6fr.size-m-mobile article:not(.aside), .m6fr article.size-m-mobile:not(.aside) { --mih: 450px; }
	.m6fr.size-l-mobile article:not(.aside), .m6fr article.size-l-mobile:not(.aside) { --mih: 520px; }
	.m6fr.wide .swiper-slide article { padding-left: var(--rpp); padding-right: var(--rpp); }
	#root .m6fr.wide .swiper-button-nav { top: auto; bottom: 0; }
	#root .m6fr.wide:not(.slider-fraction) .swiper-button-nav { overflow: visible; width: 37px; height: 44px; }
	#root .m6fr.wide:not(.slider-fraction) .swiper-button-nav:after { content: ""; display: block; position: absolute; left: -7px; right: -7px; top: 0; bottom: 0; }
	.m6pr { margin-bottom: 0; }
	.m6pr .l4dr { margin-right: -18px; }
	.m6pr .l4dr li { margin-right: 18px; }
	.m6pr .l4pr .swiper-outer a[href^="#model-3d"] + [data-shopify-xr] { bottom: 0 !important; }
	.m6tb { margin-top: /*-11px*/ 0; margin-bottom: 0; }
	.m6tb > div > * { margin-top: 0; margin-bottom: 0; }
	/*.m6tb .tabs-inner { margin-bottom: -18px; }*/
	.m6tb .tabs-inner > .cols:last-child { margin-bottom: 35px; margin-left: 0; }
	#root .m6tb .tabs-inner > .cols:last-child > * { border-left-width: 0; }
	.m6tb > div > *:last-child .tabs-inner { margin-bottom: 0; }
	.m6tb .tabs-header { color: var(--primary_text_h); font-size: var(--mob_h2); font-family: var(--main_ff_h); font-weight: var(--main_fw_h); font-style: var(--main_fs_h); line-height: var(--main_lh_h); text-transform: var(--main_tt_h); letter-spacing: var(--main_ls_h); }
	.shopify-section-content-tabs .m6tb .tabs-header { font-size: var(--main_fz); font-family: var(--main_ff); font-weight: var(--main_fw); font-style: var(--main_fs); letter-spacing: var(--main_ls); text-transform: var(--main_tt); }
	.m6tb + *, .recently-viewed-products { margin-top: 26px; }
	.with-mobile-tab { margin-bottom: 26px; }
	.with-mobile-tab + .with-mobile-tab { margin-top: -26px; }
	/*.m6tb.compact {}*/
	.m6tb.compact > nav ul { margin-right: -22px; }
	.m6tb.compact > nav ul li { margin-right: 22px; }
	.m6wd, .m6wd.large { margin-top: 32px; margin-bottom: 32px; padding-top: 24px; padding-bottom: 4px; }
	.m6wd > .l4ne.featured:last-child { margin-bottom: 12px; }
	.n6br { min-height: 10px; }
	#root .n6br ul, #root .n6br ol, #root .n6br p { margin-bottom: 8px; }
	.n6pg { margin-bottom: 4px; }
	#root .n6pg p { width: 100%; margin: 0 0 10px; text-align: center; }
	.n6pg + .link-btn a { width: 100%; }
	q { margin-bottom: 16px; padding-left: 16px; border: 0 solid var(--secondary_bg); border-left-width: 3px; }
	.recommendation-modal__container { padding: 16px var(--rpp) .1px !important; }
	.sm-fz { font-size: var(--mob_fz); }
	table { --p_lr: 8px; }
	th, td { font-size: var(--main_fz_small); }
	tr > *:first-child { padding-left: var(--rpp); }
	tr > *:last-child { padding-right: var(--rpp); }
	tr { position: relative; z-index: 2; }
	.cols .table-wrapper, .cols table { width: calc(100% + var(--rpp) + var(--rpp)); margin-left: var(--rpn); margin-right: var(--rpn); }
	.cols .table-wrapper table { width: 100%; margin-left: 0; margin-right: 0; }
	.table-wrapper + .l4tt, table + .l4tt { padding-left: 0; padding-right: 0; }
	.w100-mobile { max-width: none; }

	.mobile-text-start, .l4ft li.mobile-text-start { text-align: var(--text_align_start); justify-content: flex-start; --justify_content: flex-start; }
	.mobile-text-center, .l4ft li.mobile-text-center { text-align: center; justify-content: center; --justify_content: center; }
	.mobile-text-end, .l4ft li.mobile-text-end { text-align: var(--text_align_end); justify-content: flex-end; --justify_content: flex-end; }

	.t1ca #root { border-bottom: 58px solid rgba(0,0,0,0); }

	.m6as > * { margin-bottom: 0; }
	#logo.text-center-sticky, #logo.text-center-sticky * { margin-left: 0; margin-right: 0; }
	#logo.text-center-sticky, #logo.text-center-sticky * { justify-content: flex-start; }

	.l4cl .link-btn, #header > .link-btn, #header-inner > .link-btn, .l4ne.featured, .shopify-section-footer form, .shopify-section-footer fieldset, .l4cl.wide, #root .link-btn.mobile-only, .shopify-section-footer > div, .m6as.inv, .m6as.mobile-inv, .l4us.wide .swiper-slide, .l4cl li.mobile-only, .l4cl.s4wi .swiper-wrapper, .l4pr.no-thumbs-mobile .swiper-custom-pagination, #nav-user, .cols, .l4us.mobile-text-center:not(.s4wi), .l4us.text-center:not(.s4wi), #root .l4us-mobile.mobile-text-center:not(.s4wi), #root .l4us-mobile.text-center:not(.s4wi), .l4ca section header, .shopify-section-footer nav > .toggle h1 ~ .l4sc, .shopify-section-footer nav > .toggle h2 ~ .l4sc, .shopify-section-footer nav > .toggle h3 ~ .l4sc, .shopify-section-footer nav > .toggle h4 ~ .l4sc, .shopify-section-footer nav > .toggle h5 ~ .l4sc, .m6tb .tabs-header, .shopify-section-footer nav > .toggle h6 ~ .l4sc, .shopify-section-footer > nav .m6cn h1 ~ .l4sc, .shopify-section-footer > nav .m6cn h2 ~ .l4sc, .shopify-section-footer > nav .m6cn h3 ~ .l4sc, .shopify-section-footer > nav .m6cn h4 ~ .l4sc, .shopify-section-footer > nav .m6cn h5 ~ .l4sc, .shopify-section-footer > nav .m6cn h6 ~ .l4sc, #root .r6rt.mobile-only, .l4ca li.align-middle, #root .check.color.mobile-only { display: flex; flex-wrap: wrap; }
	#root .mobile-only.cols-mobile { display: flex; }
	#nav-top, #nav-top > ul > li > a, .link-btn.tags, .l4ne, .shopify-section-footer form, .l4cl.wide, .l4ne.featured, .l4cl.s4wi .swiper-wrapper, .m6fr .swiper-wrapper .swiper-slidee, #root .m6fr article, #root .m6fr article.aside, #root .link-btn.cols, .l4cl:not(.list) .li > div + div:last-child, .l4ca section header, .cols-mobile, .link-btn.mobile-compact, .m6tb .tabs-header, #root .m6as.inv, #root .m6ac.mobile-inv { flex-wrap: nowrap; }
	#root .l4ne.wide { flex-wrap: wrap; }
	#root .m6as.inv, .n6pge, .m6as { flex-direction: column; }
	#root .m6as.mobile-inv { flex-direction: column-reverse; }
	#nav-top { flex-direction: row-reverse; }
	.shopify-section-footer > div, .shopify-section-footer > div .l4pm, .shopify-section-footer > div .l4dr, .l4us.wide .swiper-slide, .mobile-text-center .countdown, .mobile-text-center.countdown, .l4pr.no-thumbs-mobile .swiper-custom-pagination, .m6fr.slider-fraction[data-active-content*="aside"] .swiper-custom-pagination, .m6fr.slider-fraction[data-active-content*="aside"][data-active-content*="inv"] .swiper-custom-pagination, #root .n6pg.text-center-mobile, .shopify-section-header .l4us.text-center-mobile:first-child ~ .l4us.text-center-mobile .swiper-slide, .l4us.mobile-text-center:not(.s4wi), .l4us.text-center:not(.s4wi), .shopify-section-header .l4us.mobile-text-center .swiper-slide, .l4us.mobile-text-center .swiper-slide, .m6cu .countdown { justify-content: center; }
	/*.m6fr article,*/ #nav-user { justify-content: flex-end; }
	.l4ca section header { align-items: baseline; }

	.l4cu, .l4cu.text-center, .text-center .l4cu, .l4ca section header { justify-content: space-between; }
	.shopify-section-footer input, .l4cl .f8pr button, .l4cl .link-btn.wide:not(.text-start, .text-end) > *, .l4cl figure .link-btn > *:not(.circle), .n6br p.cols, .l4al.inline li:first-child:last-child, .l4al.inline li.last-child:first-child { flex-grow: 3; }

	#nav-top > ul.l4us.l4us-mobile, .shopify-section-footer > nav > .strong h1 ~ *, .shopify-section-footer > nav > .strong h2 ~ *, .shopify-section-footer > nav > .strong h3 ~ *, .shopify-section-footer > nav > .strong h4 ~ *, .shopify-section-footer > nav > .strong h5 ~ *, .shopify-section-footer > nav > .strong h6 ~ *, .shopify-section-footer > nav .m6cn h1 ~ *, .shopify-section-footer > nav .m6cn h2 ~ *, .shopify-section-footer > nav .m6cn h3 ~ *, .shopify-section-footer > nav .m6cn h4 ~ *, .shopify-section-footer > nav .m6cn h5 ~ *, .shopify-section-footer > nav .m6cn h6 ~ *, .shopify-section-footer a.header-toggle, .shopify-section-footer nav > .toggle h1 ~ *, .shopify-section-footer nav > .toggle h2 ~ *, .shopify-section-footer nav > .toggle h3 ~ *, .shopify-section-footer nav > .toggle h4 ~ *, .shopify-section-footer nav > .toggle h5 ~ *, .shopify-section-footer nav > .toggle h6 ~ *, .shopify-section-footer > div figure, #root .mobile-only, #root .m6tb > div > .hidden, .m6tb .tabs-header.toggle + .tabs-inner, .m6tb.compact > nav, .m6tb.compact .tabs-inner, .cookie-on #cookie-bar, #cookie-inner, .l4cl.inline li:nth-child(n+6), #root .l4us-mobile, .l4ca.compact section, #root .l4cl.wide li.hidden, .m6fr .swiper-slide article.aside, .shopify-section-header nav:not(#nav-user) li.mobile-only, #cookie-bar .icon-cookie, #background .mobile-only, #root .l4cl figure .mobile-hide ~ .mobile-only { display: block; }

	#root span.mobile-only, #root i.mobile-only { display: inline; }
	#root .link-btn i, #root button i, #root .submit i { display: inline-block; }
	#root .shopify-section-header .mobile-hide, #nav-top > ul[data-type], #nav-top > ul > li.sub > a:before, #nav-user > ul > li.sub > a:before, #nav-user > ul > li.mobile-only, #root .mobile-hide, #root .m6tb > nav, .m6tb .tabs-inner, .m6tb.compact .tabs-header, .m6tb.compact > div > .hidden, .link-btn a.inline-mobile:before, #root .l4cl.hr li:first-child:before, #root blockquote:before, #root .l4ca .s1pr.mobile-hide, #root .l4ca.compact .cols .s1pr.mobile-hide, .js #nav-user > ul > li.mobile-only.search, .t1as #root #background, .l4pr.no-thumbs-mobile .swiper-pagination-bullets, .n6br.mobile-hide, #root .shopify-section-footer > nav > .strong:before, .search-full #root > .overlay-close, .search-full #nav-bar > .overlay-close, .l4us .swiper-button-nav, #background .mobile-hide, #logo .mobile-hide, .l4cl figure:not(.overlay-static) .link-btn > *:not(.circle), .l4cl figure:not(.overlay-static) form, #root .l4cl .static.mobile-hide, #root .l4cl form.mobile-hide, #root .l4cl form.link-btn, .f8ps, #root #nav-top > ul.l4us.mobile-hide, .shopify-section-footer > hr, #root .shopify-section-header #nav > ul.nav-top ~ .nav-top, #root .shopify-section-header #nav > ul.nav-top[data-type] ~ .nav-top, #nav-user > ul > li.currency, #nav-user > ul > li.lang, .link-btn.mobile-hide, #root .l4cl figure .mobile-hide, #root #content .l4cl figure .mobile-hide, .l4cl .overlay-static ~ .static, #root .m6fr figure picture.mobile-hide, #root .m6fr .mobile-only[class*="media-flexible"] ~ .mobile-hide-media-flexible, .media-flexible-mobile > figure ~ figure, .media-flexible.mobile-hide ~ .media-flexible, .media-flexible-mobile ~ .media-flexible, #root .l4ft li.empty, #root .m6fr figure picture.mobile-hide, #root .m6fr figure video.mobile-hide { display: none; }
	.shopify-section-footer .mobile-hide { display: none !important; }

	#root .tablet-hide, #root .mobile-only:not(i), .l4cl .link-btn { position: relative; left: 0; top: 0; }
	#root i.mobile-only { position: relative; left: 0; }
	#root .tablet-only, #root .mobile-hide { position: absolute; left: -3000em; top: -3000em; right: auto; bottom: auto; }
}
@media only screen and (max-width: 600px) {
	/*.l4cu {}*/
	.l4cu li { width: 50%; }
	.table-drop { border-bottom: 1px solid var(--custom_bd); }
	.table-drop tr > * { position: relative; z-index: 2; border-width: 0; }
	.table-drop tr > td { padding-top: 11px; }
	.table-drop tbody > tr:first-child > td { padding-top: 10px; }
	.table-drop tr > td:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; height: 1px; background: var(--custom_bd); }
	#root .table-drop tr > .text-end { display: table-cell; }
	.table-drop tr.sub.active, .table-drop.static tr.sub { display: table-row; }
	.table-drop tr.sub:last-child > * { padding-bottom: 16px; }
	.table-drop tr.sub > * { padding-top: 0; padding-bottom: 0; border-width: 0; background: none; }
	.table-drop.static tr.sub > * { padding-top: 10px; }
	.table-drop tr.sub.active + tr > * { padding-top: 26px; }
	.table-drop tr.sub.active + tr > *:before { top: 15px; }
	.table-drop tr.sub.active + tr.sub > *, .table-drop.static tr.sub + tr.sub > * { padding-top: 0; }
	.table-drop tr.sub > *:first-child:last-child a { display: block; padding-top: 2px; }
	.table-drop .icon-chevron-up, .table-drop .icon-chevron-down { font-size: 6px; }
	.table-drop a.toggle:before { content: ""; display: block; position: absolute; left: 0; right: 0; top: 0; bottom: 0; z-index: 9; }
	.table-drop a.toggle.active .icon-chevron-down:before { content: "\e908"; }
	.table-drop th .mobile-only { font-weight: var(--main_fw); }
	.table-drop tr > *, #root .table-wrapper + .l4tt, #root table + .l4tt { font-size: var(--main_fz); }

	.table-drop a.toggle { display: block; }
	.table-drop a.toggle ~ *, .table-drop tr > *:nth-child(n+3), .table-drop tr.sub.active + tr.sub > *:before, .table-drop tbody > tr:first-child > td:before, .table-drop tr.not-sub + tr.sub > td:before, #root .table-drop.simple tr > .text-end, .table-drop.static tbody tr.not-sub, .table-drop tr.sub > *:before { display: none; }
}
@media only screen and (max-width: 500px) {
	.js .countdown { --w: 23px; --h: 30px; --dist2: 15px; --fz: 16px; }
	.m6cu .countdown .simply-word { font-size: calc(var(--main_fz) * 0.8571428571); }

	.l4al .cols:not(.cols-mobile) {
		text-align: center;
		flex-direction: column;
	}
	.l4al li > i.sticky { font-size: 18px; }
	.l4al li > i.sticky ~ .cols { margin-top: -4px; }
	.n6pg, .spr-pagination { --dist: 20px; }
}
@media only screen and (max-height: 570px) {
	/*.m6fr {}*/
	.m6fr.size-m article, .m6fr article.size-m { min-height: 100vh; }
	.m6fr.size-l article, .m6fr article.size-l { min-height: 100vh; }
}
@media only screen and (max-width: 400px) {
	/*#header-inner:not(.text-center-mobile) #logo { max-width: 40% !important; }*/
	:root { --pager_w: 44px; }

	/*.cols {}*/
	#root .cols:not(.cols-mobile) > *, #root .cols:not(.cols-mobile, .link-btn) > * { float: none; width: 100%; }
	.f8nw { padding-left: var(--rpp); padding-right: var(--rpp); }
	.f8nw header, .f8nw input, .f8nw .check, .f8nw > *, .f8nw fieldset > * { width: 100%; margin-left: 0; margin-right: 0; }
	.f8nw .submit { justify-content: center; }
	/*.link-btn {}*/
	.link-btn .w300 { width: 100%; min-width: 0; max-width: 300px; }
	.l4pr { --dist_li: 10px; }
	/*#root .l4pr:not(.no-thumbs-mobile) .swiper-pagination-bullets .swiper-pagination-bullet:nth-child(4) ~ * { display: none; }*/
	.recommendation-modal__container {
		left: var(--rpp) !important; right: var(--rpp) !important; width: auto !important; max-width: none !important;
		transform: translateY(-50%) !important;
	}

	#root .l4as.caption { padding-top: 72px; padding-left: 0; padding-right: 0; }
}
@media only screen and (max-width: 340px) {
	.shopify-section-footer > nav .m6cn p { width: 100%; }
	#root .shopify-section-footer > nav .m6cn { padding-right: var(--rpp); padding-left: var(--rpp); }
	#root .shopify-section-footer > nav .m6cn .l4cn { padding-left: 0; padding-right: 0; margin-left: 0; margin-right: 0; }
	#root .shopify-section-footer > nav .m6cn figure { display: none; }
}
@media print {
	nav, .shopify-section-announcement-bar, form, .l4dr, .l4pm, .l4sc { display: none !important; }
}



/* xpert header styles (must remain at the bottom of the file) */
/* transparent header */
.has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed) #nav.tr_bd:before, .has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed) #nav-bar.tr_bd:before { border-bottom-width: 1px; }
.has-first-m6fr-wide .shopify-section-header.transparent.has-no-wide:not(.fixed):before { display: none; }
.has-first-m6fr-wide .shopify-section-header.transparent.has-no-wide:not(.fixed) #nav-outer:before { content: ""; display: block; position: absolute; left: 0; right: var(--dist_main); top: 0; bottom: 0; z-index: -1; background: none; border-bottom: 1px solid var(--custom_top_nav_bd); opacity: var(--custom_top_nav_bd_op); }

.has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed):before { box-shadow: none; background: none; border-bottom: 0 solid var(--custom_top_nav_bd); opacity: var(--custom_top_nav_bd_op); }
.has-first-m6fr-wide .shopify-section-header.transparent:has(#nav-outer):not(.fixed):before { border-bottom-width: 1px; }
.has-first-m6fr-wide .shopify-section-header.transparent.has-nav-outer:not(.fixed):before { border-bottom-width: 1px; }
.has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed) #nav:before, .has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed) #nav-bar:before { border-bottom-width: 0; }
.has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed):has(#nav-outer):has(.no-wide) #nav:before, .has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed):has(#nav-outer):has(.no-wide) #nav-bar:before { border-bottom-width: 1px; }
.has-first-m6fr-wide .shopify-section-header.transparent.has-nav-outer.has-no-wide:not(.fixed) #nav:before, .has-first-m6fr-wide .shopify-section-header.transparent.has-nav-outer.has-no-wide:not(.fixed) #nav-bar:before { border-bottom-width: 1px; }

.has-first-m6fr-wide:not(.m2a) .shopify-section-header.transparent:not(.fixed) #nav:not(.fixed):before, .has-first-m6fr-wide:not(.m2a) .shopify-section-header.transparent:not(.fixed) #nav-bar:not(.fixed):before { background: none; /*opacity: 0;*/ opacity: var(--custom_top_nav_bd_op); }
.has-first-m6fr-wide:not(.m2a) .shopify-section-header.transparent:not(.fixed) #nav.no-wide:after, .has-first-m6fr-wide:not(.m2a) .shopify-section-header.transparent:not(.fixed) #nav-bar.no-wide:after { background: none; }
.has-first-m6fr-wide:not(.tr_hh) .shopify-section-header.transparent:not(.fixed) #nav-bar:not(.fixed), .has-first-m6fr-wide:not(.m2a, .tr_hh) .shopify-section-header.transparent:not(.fixed) #nav:not(.fixed) { --custom_top_nav_fg: var(--custom_top_main_fg); }
.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { margin-top: calc(0px - var(--content_p) - var(--hhs)); --hhs: var(--header_height_static); }
.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article { border-top: var(--hhs) solid rgba(0,0,0,0); }
.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article:not(.background-wide, .ai):before, .has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article:not(.background-wide, .ai) > figure, .has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article:not(.background-wide, .ai) .media-flexible { top: calc(0px - var(--hhs)); }
@media only screen and (min-width: 761px) {
	.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article.size-xl, .has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.size-xl.im-tr:first-child article { --mih: calc(100vh - var(--nav_top_h)); }
}
@media only screen and (max-width: 760px) {
	.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article.size-xl:not(.size-s-mobile, .size-m-mobile, .size-l-mobile, .size-xs-mobile, .size-xl-mobile), .has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.size-xl.im-tr:first-child:not(.size-s-mobile, .size-m-mobile, .size-l-mobile, .size-xs-mobile, .size-xl-mobile) article { --mih: calc(100vh - var(--nav_top_h)); }
}

.has-first-m6fr-wide .shopify-section-header:has(#header-inner.mobile-visible-search) ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --hhs: calc(var(--header_height_static) + var(--mob_cl) + var(--custom_top_search_h)); --mob_cl: calc(var(--search_mob_pd) * 2); }
.has-first-m6fr-wide .shopify-section-header.has-mobile-visible-search ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --hhs: calc(var(--header_height_static) + var(--mob_cl) + var(--custom_top_search_h)); --mob_cl: calc(var(--search_mob_pd) * 2); }
.has-first-m6fr-wide .shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)) ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: var(--search_mob_pd); }
.has-first-m6fr-wide .shopify-section-header.has-mobile-visible-search.no-bd-m ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: var(--search_mob_pd); }
.has-first-m6fr-wide .shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-pd-t) ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: var(--search_mob_pd); }
.has-first-m6fr-wide .shopify-section-header.has-mobile-visible-search.no-pd-t ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: var(--search_mob_pd); }
/*.has-first-m6fr-wide .shopify-section-header:has(#header-inner.mobile-visible-search):has(#search.no-bg:not(.bd-b)):has(#search.no-pd-t) ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: 0px; }
	.has-first-m6fr-wide .shopify-section-header.has-mobile-visible-search.no-bd-m.no-pd-t ~ #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child { --mob_cl: 0px; } */


#nav-outer #nav:before, #nav-outer #nav:after, #nav-outer #nav-bar:before, #nav-outer #nav-bar:after { display: none; }
.search-full:not(.no-search-overlay) #nav-outer #nav > .overlay-close, .search-full:not(.no-search-overlay) #nav-outer #nav-bar > .overlay-close { display: none; }
#header-inner.text-center-logo #nav-outer { align-items: center; }


@media only screen and (min-width: 1001px) {
	.shopify-section-header { --bd_w: 100%; }
	.shopify-section-header li.sub:not(.show-all, .no-arrow), .shopify-section-header li.sub:not(.show-all, .no-arrow) { --bd_w: calc(100% - 16px); }
	#nav > ul > li > a:after, #nav-bar > ul > li > a:after {
		content: ""; display: block; position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; bottom: calc(50% - var(--btn_lh) * var(--custom_top_nav_fz) * 0.5 - 4px); width: 0%; border-bottom: 1px solid var(--custom_top_nav_fg);
		transform: none;
	}
	#header-inner #nav-outer {
		display: block; position: static; min-width: 0;
		flex-shrink: 10; flex-grow: 3;
	}
	#header-inner #nav-outer, #header-inner #nav-outer #nav-bar, #header-inner #nav-outer #nav-bar > ul, #header-inner #nav-outer #nav-bar > li { align-self: stretch; }
	.shopify-section-header:not(.ready) #nav-outer { opacity: 0; }
	html[style*="logo_offset"] #header-inner.text-center-logo #nav-outer:not(.fixed) #nav, html[style*="logo_offset"] #header-inner.text-center-logo #nav-outer:not(.fixed) #nav-bar { max-width: calc(var(--logo_offset) - 16px * 2); }
	#header-inner.text-center-logo #nav-outer {
		position: absolute; left: 0; right: 0; top: 12px; bottom: 0; pointer-events: none;
		display: flex;
	}
	#header-inner.text-center-logo #nav-outer > * { pointer-events: auto; }

	.has-first-m6fr-wide #root #nav > ul > li > a, .has-first-m6fr-wide #root #nav-bar > ul > li > a {  color: var(--custom_top_nav_fg); }
	.has-first-m6fr-wide #root #nav > ul > li.active > a, .has-first-m6fr-wide #root #nav-bar > ul > li.active > a { font-weight: var(--custom_top_nav_fw); }
	.has-first-m6fr-wide #root #nav > ul > li.active > a:after, .has-first-m6fr-wide #root #nav-bar > ul > li.active > a:after { width: var(--bd_w); }

	#header-inner #nav-outer { display: flex; }
	#header-inner #nav-outer #nav-bar > ul > li { align-items: center; }
}
@media only screen and (max-width: 1000px) { /* 1000 */
	#header-inner #nav-outer { position: absolute; left: var(--l0ra); right: var(--lar0); top: 0; margin: 0; border-width: 0; }
}
@media only screen and (max-width: 760px) {
	#search.no-bg:before { background: none; }
	.shopify-section-header:not(.fixed) #search.no-bg:before { /*border-top-width: 1px; border-bottom-width: 1px;*/ border: 0 solid var(--custom_top_nav_bd); opacity: var(--custom_top_nav_bd_op); }
	/*.shopify-section-header:not(.fixed)*/ #search.no-bg:after { content: ""; display: block; position: absolute; left: var(--rpn); right: var(--rpn); top: -1px; bottom: 0; z-index: -2; background: var(--custom_top_main_bg); }
	.has-first-m6fr-wide .shopify-section-header.transparent:not(.fixed) #search.no-bg:after { display: none; }
	/*#search.no-bd:before { border-top-width: 0; border-bottom-width: 0; }*/
	/*#search.no-bg.bd:before { }*/
	#root #search.no-bg.bd-t:before { border-top-width: 1px; }
	#root #search.no-bg.bd-b:before { border-bottom-width: 1px; }
	.shopify-section-header.no-bd:before { border-bottom-width: 0; }
	#root #search.no-pd-t { padding-top: 0; }

	/*.m6fr {}*/
	.has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article.aside:not(.background-wide, .ai) > figure + div, .has-first-m6fr-wide #content > [id*="shopify-section"]:first-child > .m6fr.wide.im-tr:first-child article.aside:not(.background-wide, .ai) .media-flexible + div { margin-top: calc(0px - var(--hhs) - var(--pd)); }

	.has-first-m6fr-wide .shopify-section-header.transparent.has-mobile-visible-search:has(#nav-outer):not(.fixed):before { border-bottom-width: 0; }
	.has-first-m6fr-wide .shopify-section-header.transparent.has-mobile-visible-search.has-nav-outer:not(.fixed):before { border-bottom-width: 0; }
}