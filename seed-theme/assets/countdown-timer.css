/* Countdown Timer Enhanced Styles */

.countdown-section {
  position: relative;
  overflow: hidden;
}

.countdown-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.1) 50%, transparent 100%);
  transform: translateX(-100%);
  animation: shimmer 3s infinite;
  pointer-events: none;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.countdown-timer .time-unit {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center;
}

.countdown-timer .time-unit:hover {
  transform: scale(1.05);
  box-shadow: rgba(0, 0, 0, 0.08) 0px 6px 12px 0px, rgba(0, 0, 0, 0.12) 0px 0px 4px 0px, rgba(0, 0, 0, 0.08) 0px 0px 2px 0px;
}

.countdown-timer .time-number {
  transition: all 0.2s ease;
  position: relative;
}

.countdown-timer .time-number::before {
  content: attr(data-prev);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  opacity: 0;
  transform: translateY(-100%);
  transition: all 0.3s ease;
}

.countdown-timer .time-number.flip-animation {
  animation: flipNumber 0.6s ease-in-out;
}

@keyframes flipNumber {
  0% {
    transform: rotateX(0deg);
  }
  50% {
    transform: rotateX(-90deg);
  }
  100% {
    transform: rotateX(0deg);
  }
}

.countdown-timer .time-label {
  transition: color 0.3s ease;
}

.countdown-timer .time-unit:hover .time-label {
  opacity: 0.8;
}

/* Pulse animation for urgent countdown */
.countdown-section.urgent .time-unit {
  animation: urgentPulse 1s infinite alternate;
}

@keyframes urgentPulse {
  0% {
    box-shadow: rgba(255, 0, 0, 0.2) 0px 4px 8px 0px, rgba(255, 0, 0, 0.3) 0px 0px 2px 0px, rgba(255, 0, 0, 0.2) 0px 0px 1px 0px;
  }
  100% {
    box-shadow: rgba(255, 0, 0, 0.4) 0px 6px 12px 0px, rgba(255, 0, 0, 0.5) 0px 0px 4px 0px, rgba(255, 0, 0, 0.4) 0px 0px 2px 0px;
  }
}

/* Fade out animation when countdown ends */
.countdown-section.countdown-ended {
  animation: fadeOut 1s ease-out forwards;
}

@keyframes fadeOut {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }
}

/* Loading state */
.countdown-timer.loading .time-number {
  background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  color: transparent;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced mobile responsiveness */
@media (max-width: 480px) {
  .countdown-section::before {
    display: none; /* Disable shimmer on mobile for performance */
  }
  
  .countdown-timer .time-unit:hover {
    transform: none; /* Disable hover effects on mobile */
    box-shadow: rgba(0, 0, 0, 0.04) 0px 4px 8px 0px, rgba(0, 0, 0, 0.06) 0px 0px 2px 0px, rgba(0, 0, 0, 0.04) 0px 0px 1px 0px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .countdown-timer .time-unit {
    border: 2px solid currentColor;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .countdown-section::before,
  .countdown-timer .time-unit,
  .countdown-timer .time-number,
  .countdown-section.urgent .time-unit {
    animation: none;
  }
  
  .countdown-timer .time-unit {
    transition: none;
  }
  
  .countdown-timer .time-unit:hover {
    transform: none;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .countdown-timer .time-unit {
    background: #2a2a2a;
    color: #ffffff;
    box-shadow: rgba(255, 255, 255, 0.1) 0px 4px 8px 0px, rgba(255, 255, 255, 0.15) 0px 0px 2px 0px, rgba(255, 255, 255, 0.1) 0px 0px 1px 0px;
  }
  
  .countdown-timer .time-unit:hover {
    box-shadow: rgba(255, 255, 255, 0.2) 0px 6px 12px 0px, rgba(255, 255, 255, 0.25) 0px 0px 4px 0px, rgba(255, 255, 255, 0.2) 0px 0px 2px 0px;
  }
}
