/* How It Works Video Section Styles */
.how-it-works-section {
  margin: 60px 0;
  padding: 0;
}

.how-it-works-title {
  box-sizing: border-box;
  color: rgb(74, 74, 74);
  display: block;
  font-family: Merriweather, serif;
  font-size: 48px;
  font-weight: 400;
  letter-spacing: 0.12px;
  line-height: 67.2px;
  margin: 0 0 40px 0;
  text-align: center;
  text-size-adjust: 100%;
  unicode-bidi: isolate;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.how-it-works-video-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.video-wrapper {
  position: relative;
  width: 100%;
  max-width: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.video-wrapper video,
.video-wrapper iframe {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 12px;
}

/* Ensure proper aspect ratio for videos */
.video-wrapper::before {
  content: '';
  display: block;
  padding-top: 56.25%; /* 16:9 aspect ratio */
}

.video-wrapper video,
.video-wrapper iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Performance optimizations */
.video-wrapper video {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* Loading state */
.video-wrapper::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 3px solid rgba(74, 74, 74, 0.1);
  border-top: 3px solid rgb(74, 74, 74);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  opacity: 0;
  pointer-events: none;
}

.video-wrapper.loading::after {
  opacity: 1;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .how-it-works-section {
    margin: 40px 0;
    padding: 0 15px;
  }

  .how-it-works-title {
    font-size: 32px;
    line-height: 44px;
    margin-bottom: 30px;
  }

  .how-it-works-video-container {
    max-width: 100%;
    padding: 0;
  }

  .video-wrapper {
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  }

  .video-wrapper video,
  .video-wrapper iframe {
    border-radius: 8px;
  }
}

@media (max-width: 480px) {
  .how-it-works-section {
    margin: 30px 0;
  }

  .how-it-works-title {
    font-size: 28px;
    line-height: 38px;
    margin-bottom: 25px;
  }
}
