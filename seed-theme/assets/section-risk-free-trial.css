/* Risk-Free Trial Section Styles */
.risk-free-trial-section {
  width: 100%;
  padding: 60px 0;
  background-color: transparent;
  overflow: hidden;
  box-sizing: border-box;
}

.risk-free-trial-container {
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
}

.risk-free-trial-content {
  display: flex;
  align-items: center;
  gap: 60px;
  min-height: 400px;
  width: 100%;
  box-sizing: border-box;
  max-width: 1400px;
  margin: 0 auto;
}

.risk-free-trial-image {
  flex: 0 0 50%;
  width: 50%;
  display: flex !important;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  min-height: 400px;
}

.risk-free-trial-img {
  width: 100%;
  max-width: 700px;
  height: auto;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
  object-fit: cover;
}

.risk-free-trial-image-placeholder {
  width: 100%;
  max-width: 700px;
  height: 600px;
  background-color: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #6c757d;
  font-family: Lato, sans-serif;
  font-size: 16px;
  box-sizing: border-box;
}

.risk-free-trial-text {
  flex: 0 0 50%;
  width: 50%;
  padding-left: 30px;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.risk-free-trial-title {
  font-family: Merriweather, serif;
  font-size: var(--title-size);
  font-weight: 700;
  color: var(--title-color);
  line-height: 1.4;
  letter-spacing: 0.12px;
  margin: 0 0 30px 0;
  max-width: 440px;
  width: 440px;
  box-sizing: border-box;
  display: block;
}

.risk-free-trial-description {
  font-family: Lato, sans-serif;
  font-size: var(--description-size);
  color: var(--description-color);
  line-height: 1.4;
  letter-spacing: 0.0278261px;
  margin: 0 0 20px 0;
  max-width: 380px;
  width: 380px;
  box-sizing: border-box;
  display: block;
}

.risk-free-trial-description p {
  margin: 0 0 20px 0;
}

.risk-free-trial-description p:last-child {
  margin-bottom: 0;
}

.risk-free-trial-button-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
  margin-top: 20px;
}

.risk-free-trial-btn {
  appearance: button;
  background-color: rgb(0, 158, 224);
  border: 1px solid rgb(0, 158, 224);
  border-radius: 4px;
  box-shadow: rgba(0, 158, 224, 0.4) 0px 4px 16px 0px;
  box-sizing: border-box;
  color: rgb(255, 255, 255);
  cursor: pointer;
  display: inline-block;
  font-family: Lato, sans-serif;
  font-size: 16px;
  font-weight: 600;
  height: 50px;
  letter-spacing: 0.571429px;
  line-height: 24px;
  min-width: 200px;
  padding: 12px 30px;
  position: relative;
  text-align: center;
  text-decoration: none;
  text-transform: uppercase;
  transition: all 0.2s linear;
  vertical-align: middle;
  width: 200px;
  white-space: nowrap;
  outline: none;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

.risk-free-trial-btn:hover {
  background-color: rgb(20, 178, 244);
  border-color: rgb(20, 178, 244);
  color: rgb(255, 255, 255);
  text-decoration: none;
}

.risk-free-trial-btn:focus {
  outline: 2px solid rgb(0, 158, 224);
  outline-offset: 2px;
}

.risk-free-trial-btn:active {
  background-color: rgb(0, 138, 196);
  border-color: rgb(0, 138, 196);
}

.risk-free-trial-btn[aria-disabled="true"] {
  background-color: #ccc;
  border-color: #ccc;
  color: #666;
  cursor: not-allowed;
  pointer-events: none;
}

.risk-free-trial-guarantee {
  font-family: Lato, sans-serif;
  font-size: 16px;
  color: var(--guarantee-color);
  margin: 0;
  font-weight: 500;
  text-align: left;
}

.risk-free-trial-image-placeholder svg {
  margin-bottom: 15px;
  opacity: 0.6;
}

.risk-free-trial-image-placeholder p {
  margin: 0;
  font-weight: 500;
}

/* Large Desktop Styles */
@media (min-width: 1440px) {
  .risk-free-trial-content {
    max-width: 1600px;
    gap: 80px;
    min-height: 450px;
  }
  
  .risk-free-trial-title {
    font-size: calc(var(--title-size) + 6px);
  }
  
  .risk-free-trial-description {
    font-size: calc(var(--description-size) + 2px);
  }
  
  .risk-free-trial-btn {
    font-size: 18px;
    padding: 14px 32px;
    min-width: 220px;
    width: 220px;
    height: 54px;
  }
}

/* Desktop Styles */
@media (max-width: 1439px) and (min-width: 1025px) {
  .risk-free-trial-content {
    max-width: 1400px;
    gap: var(--content-gap);
    min-height: 400px;
  }
}

/* Tablet Styles */
@media (max-width: 1024px) and (min-width: 769px) {
  .risk-free-trial-content {
    gap: 40px;
    min-height: 350px;
  }
  
  .risk-free-trial-text {
    flex: 0 0 50%;
    width: 50%;
    padding-left: 20px;
  }
  
  .risk-free-trial-image {
    flex: 0 0 50%;
    width: 50%;
  }
  
  .risk-free-trial-title {
    font-size: calc(var(--title-size) - 4px);
    max-width: 100%;
    width: auto;
  }
  
  .risk-free-trial-description {
    font-size: calc(var(--description-size) - 1px);
    max-width: 100%;
    width: auto;
  }
  
  .risk-free-trial-btn {
    padding: 12px 30px;
    font-size: 16px;
    width: 200px;
    min-width: 200px;
  }
  
  .risk-free-trial-guarantee {
    font-size: 13px;
  }
  
  .risk-free-trial-img,
  .risk-free-trial-image-placeholder {
    width: 100%;
    max-width: 450px;
    height: auto;
    aspect-ratio: 710/606;
  }
}

/* Mobile Landscape Styles */
@media (max-width: 768px) and (min-width: 481px) {
  .risk-free-trial-section {
    padding: var(--section-padding-top-mobile) 0 var(--section-padding-bottom-mobile);
  }
  
  .risk-free-trial-content {
    flex-direction: column;
    gap: var(--content-gap-mobile);
    min-height: 300px;
  }
  
  .risk-free-trial-text {
    flex: none;
    width: 100%;
    padding-left: 0;
    text-align: center;
    order: 2;
  }
  
  .risk-free-trial-image {
    flex: none;
    width: 100%;
    order: 1;
  }
  
  .risk-free-trial-title {
    font-size: var(--title-size-mobile);
    max-width: 100%;
    width: auto;
    margin: 0 0 15px 0;
  }
  
  .risk-free-trial-description {
    font-size: var(--description-size-mobile);
    max-width: 100%;
    width: auto;
    margin: 0 0 20px 0;
  }
  
  .risk-free-trial-button-container {
    align-items: center;
    margin-top: 15px;
    text-align: center;
  }

  .risk-free-trial-btn {
    width: 200px;
    max-width: 200px;
    padding: 12px 30px;
    font-size: 16px;
    margin: 0 auto;
    display: block;
  }
  
  .risk-free-trial-guarantee {
    font-size: 12px;
    text-align: center;
  }
  
  .risk-free-trial-img,
  .risk-free-trial-image-placeholder {
    width: 100%;
    max-width: 400px;
    height: auto;
    aspect-ratio: 710/606;
  }
}

/* Mobile Portrait Styles */
@media (max-width: 480px) {
  .risk-free-trial-section {
    padding: var(--section-padding-top-mobile) 0 var(--section-padding-bottom-mobile);
  }
  
  .risk-free-trial-content {
    flex-direction: column;
    gap: var(--content-gap-mobile);
    min-height: 250px;
  }
  
  .risk-free-trial-text {
    flex: none;
    width: 100%;
    padding-left: 0;
    text-align: center;
    order: 2;
  }
  
  .risk-free-trial-title {
    font-size: var(--title-size-mobile);
    max-width: 100%;
    width: auto;
    margin: 0 0 15px 0;
  }
  
  .risk-free-trial-description {
    font-size: var(--description-size-mobile);
    max-width: 100%;
    width: auto;
    margin: 0 0 20px 0;
  }
  
  .risk-free-trial-btn {
    width: 200px;
    max-width: 200px;
    padding: 12px 30px;
    font-size: 16px;
    margin: 0 auto;
    display: block;
  }
  
  .risk-free-trial-guarantee {
    font-size: 12px;
    text-align: center;
  }
  
  .risk-free-trial-image {
    flex: none;
    width: 100%;
    order: 1;
  }
  
  .risk-free-trial-img,
  .risk-free-trial-image-placeholder {
    width: 100%;
    max-width: 100%;
    height: auto;
    aspect-ratio: 710/606;
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .risk-free-trial-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .risk-free-trial-btn,
  .risk-free-trial-img {
    transition: none;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .risk-free-trial-title {
    color: #000;
  }

  .risk-free-trial-description {
    color: #333;
  }

  .risk-free-trial-btn {
    border: 2px solid currentColor;
  }

  .risk-free-trial-guarantee {
    color: #555;
  }
}

/* Print Styles */
@media print {
  .risk-free-trial-section {
    background: white !important;
    color: black !important;
  }

  .risk-free-trial-btn {
    border: 1px solid black !important;
    background: white !important;
    color: black !important;
  }

  .risk-free-trial-image {
    break-inside: avoid;
  }
}

/* Focus Styles for Accessibility */
.risk-free-trial-btn:focus-visible {
  outline: 2px solid var(--button-bg);
  outline-offset: 2px;
}

.risk-free-trial-title:focus-visible,
.risk-free-trial-description:focus-visible {
  outline: 2px solid var(--title-color);
  outline-offset: 2px;
}

.risk-free-trial-image:focus-visible {
  outline: 2px solid var(--title-color);
  outline-offset: 2px;
}

/* RTL Support */
[dir="rtl"] .risk-free-trial-text {
  padding-left: 0;
  padding-right: 30px;
}

[dir="rtl"] .risk-free-trial-content {
  flex-direction: row-reverse;
}

@media (max-width: 768px) {
  [dir="rtl"] .risk-free-trial-content {
    flex-direction: column;
  }

  [dir="rtl"] .risk-free-trial-text {
    padding-right: 0;
  }
}

/* Loading States */
.risk-free-trial-img[loading="lazy"] {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.risk-free-trial-img[loading="lazy"].loaded {
  opacity: 1;
}

/* No hover effects for images - keeping it simple like other sections */
