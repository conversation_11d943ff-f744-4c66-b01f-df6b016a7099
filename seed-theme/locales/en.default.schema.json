/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "global": {
    "fix_zindex": {
      "label": "Control the stacking order of sections",
      "info": "Increase or decrease the z-index value to control the visual layering of sections, ensuring the desired display arrangement when using negative spacing. [Read more](https://intercom.help/someoneyouknow/en/articles/8310188-z-index-settings)"
    },
    "mobile": {
      "header": "Mobile layout"
    },
    "alignment": {
      "left": {
        "label": "Left"
      },
      "center": {
        "label": "Center"
      },
      "right": {
        "label": "Right"
      },
      "divide_evenly": {
        "label": "Divide evenly"
      },
      "top_left": {
        "label": "Top left"
      },
      "top_center": {
        "label": "Top center"
      },
      "top_right": {
        "label": "Top right"
      },
      "center_left": {
        "label": "Center left"
      },
      "center_center": {
        "label": "Center center"
      },
      "center_right": {
        "label": "Center right"
      },
      "bottom_left": {
        "label": "Bottom left"
      },
      "bottom_center": {
        "label": "Bottom center"
      },
      "bottom_right": {
        "label": "Bottom right"
      }
    },
    "media_position": {
      "label": "Position on desktop",
      "desktop": {
        "label": "Position on desktop",
        "options__1": {
          "label": "Before content"
        },
        "options__2": {
          "label": "After content"
        }
      },
      "mobile": {
        "label": "Position on mobile",
        "options__1": {
          "label": "Before content"
        },
        "options__2": {
          "label": "After content"
        }
      }
    },
    "shop_the_look": {
      "label": "Shop the look",
      "show_look": {
        "label": "Enable ‘shop the look’ feature"
      },
      "product_list": {
        "label": "Products"
      },
      "look_btn_style": {
        "label": "Add to cart button style"
      },
      "look_position": {
        "label": "Position",
        "info": "This is the position of the add to cart button that will appear in the banner.",
        "options__1": {
          "label": "Left"
        },
        "options__2": {
          "label": "Right"
        }
      },
      "label_header": {
        "header": "Label"
      },
      "show_look_label": {
        "label": "Show label"
      },
      "look_label_text": {
        "label": "Label text"
      }
    },
    "typography": {
      "title_weight": {
        "label": "Heading font weight"
      },
      "title": {
        "label": "Heading",
        "info": "To add an underline, select the text and press Ctrl + U for Windows and Linux or Cmd + U for Mac."
      },
      "title_underline_style": {
        "label": "Underlined heading style",
        "group": {
          "fonts": "Fonts",
          "colors": "Colors"
        },
        "none": {
          "label": "None"
        },
        "secondary_font": {
          "label": "Secondary font"
        },
        "secondary_font_accent": {
          "label": "Secondary font in accent color"
        },
        "secondary_font_gradient": {
          "label": "Secondary font in accent gradient color"
        },
        "accent": {
          "label": "Accent color"
        },
        "gradient": {
          "label": "Accent gradient color"
        }
      },
      "title_size": {
        "label": "Heading font size",
        "h6": {
          "label": "Heading 6"
        },
        "h5": {
          "label": "Heading 5"
        },
        "h4": {
          "label": "Heading 4"
        },
        "h3": {
          "label": "Heading 3"
        },
        "h2": {
          "label": "Heading 2"
        },
        "h1": {
          "label": "Heading 1"
        }
      },
      "blog_title_size": {
        "label": "Blog posts title size"
      },
      "text": {
        "header": "Text",
        "label": "Content"
      },
      "section_text": {
        "header": "Text"
      },
      "title_font": {
        "label": "Heading font",
        "primary": {
          "label": "Primary font"
        },
        "secondary": {
          "label": "Secondary font"
        }
      },
      "font_size": {
        "label": "Text font size",
        "13px": {
          "label": "Small"
        },
        "14px": {
          "label": "Medium"
        },
        "16px": {
          "label": "Large"
        },
        "18px": {
          "label": "Extra large"
        }
      },
      "font_weight": {
        "label": "Text font weight",
        "100": {
          "label": "Thin"
        },
        "200": {
          "label": "Extra light"
        },
        "300": {
          "label": "Light"
        },
        "400": {
          "label": "Regular"
        },
        "500": {
          "label": "Medium"
        },
        "600": {
          "label": "Semi bold"
        },
        "700": {
          "label": "Bold"
        },
        "800": {
          "label": "Extra bold"
        },
        "900": {
          "label": "Black"
        }
      },
      "text_position": {
        "label": "Content position",
        "top_left": {
          "label": "Top left"
        },
        "top_center": {
          "label": "Top center"
        },
        "top_right": {
          "label": "Top right"
        },
        "center_left": {
          "label": "Center left"
        },
        "center_center": {
          "label": "Center center"
        },
        "center_right": {
          "label": "Center right"
        },
        "bottom_left": {
          "label": "Bottom left"
        },
        "bottom_center": {
          "label": "Bottom center"
        },
        "bottom_right": {
          "label": "Bottom right"
        }
      }
    },
    "color_palette": {
      "label": "Color scheme"
    },
    "buttons": {
      "header": "Buttons"
    },
    "button": {
      "header": "Button",
      "show_link": {
        "label": "Show button"
      },
      "link_text": {
        "label": "Label",
        "info": "Leave the label blank to hide the button. Only applicable when ‘clickable images’ setting is enabled in theme settings - accessibility."
      },
      "link_url": {
        "label": "Link"
      },
      "button_style": {
        "label": "Style",
        "group": {
          "plain": "Fill",
          "inv": "No fill",
          "link": "Link"
        },
        "primary": {
          "label": "Primary"
        },
        "secondary": {
          "label": "Secondary"
        },
        "tertiary": {
          "label": "Tertiary"
        },
        "positive": {
          "label": "Positive"
        },
        "buy_button": {
          "label": "Buy button"
        },
        "dynamic_buy_button": {
          "label": "Dynamic buy button"
        }
      }
    },
    "overlay": {
      "header": "Overlay link",
      "show_overlay_link": {
        "label": "Enable link overlay"
      },
      "overlay_url": {
        "label": "Link"
      }
    },
    "layout": {
      "width": {
        "label": "Width",
        "label_banner": "Banner width",
        "options__1": {
          "label": "Boxed width"
        },
        "options__2": {
          "label": "Full width"
        }
      },
      "height": {
        "label": "Height",
        "label_banner": "Banner height",
        "options__1": {
          "label": "Extra small"
        },
        "options__2": {
          "label": "Small"
        },
        "options__3": {
          "label": "Medium"
        },
        "options__4": {
          "label": "Large"
        },
        "options__5": {
          "label": "Extra large"
        },
        "16_9": {
          "label": "16/9"
        },
        "1_1": {
          "label": "1/1"
        },
        "4_5": {
          "label": "4/5"
        },
        "32_9": {
          "label": "32/9"
        },
        "21_9": {
          "label": "21/9"
        },
        "3_2": {
          "label": "3/2"
        },
        "4_3": {
          "label": "4/3"
        },
        "adapt": {
          "label": "Adapt to image"
        },
        "adapt_first": {
          "label": "Adapt to first image"
        },
        "full": {
          "label": "Full height "
        }
      },
      "enable_custom_height": {
        "label": "Enable custom minimal height"
      },
      "custom_height": {
        "label": "Custom height"
      }
    },
    "icon": {
      "label": "Icon",
      "info": "Icon uses primary button color for accents.",
      "options__default_check": {
        "label": "Check (green)"
      },
      "options__1": {
        "label": "No icon"
      },
      "options__2": {
        "label": "Group"
      },
      "options__3": {
        "label": "Notification"
      },
      "options__4": {
        "label": "Cloud data"
      },
      "options__5": {
        "label": "Verified"
      },
      "options__6": {
        "label": "Truck"
      },
      "options__7": {
        "label": "Image"
      },
      "options__8": {
        "label": "Phone call"
      },
      "options__9": {
        "label": "Filters"
      },
      "options__10": {
        "label": "Shopping bag"
      },
      "options__11": {
        "label": "Global shipping"
      },
      "options__12": {
        "label": "Barcode"
      },
      "options__13": {
        "label": "Box"
      },
      "options__14": {
        "label": "Delivery box"
      },
      "options__15": {
        "label": "Statistic"
      },
      "options__16": {
        "label": "Review"
      },
      "options__17": {
        "label": "Email"
      },
      "options__18": {
        "label": "Coin"
      },
      "options__19": {
        "label": "24 hour clock"
      },
      "options__20": {
        "label": "Question"
      },
      "options__21": {
        "label": "24/7 on call"
      },
      "options__22": {
        "label": "Speech bubbles"
      },
      "options__23": {
        "label": "Coupon"
      },
      "options__24": {
        "label": "Mobile payment"
      },
      "options__25": {
        "label": "Calculator"
      },
      "options__26": {
        "label": "Secure"
      }
    },
    "spacing": {
      "header": "Spacing",
      "spacing_desktop": {
        "label": "Bottom spacing desktop"
      },
      "spacing_mobile": {
        "label": "Bottom spacing mobile"
      }
    },
    "padding": {
      "header": "Padding",
      "mobile": "Padding mobile",
      "padding_top": {
        "label": "Top"
      },
      "padding_bottom": {
        "label": "Bottom"
      },
      "padding_left": {
        "label": "Left"
      },
      "padding_right": {
        "label": "Right"
      }
    }
  },
  "settings_schema": {
    "accessibility": {
      "name": "Accessibility",
      "settings": {
        "favicon": {
          "label": "Favicon",
          "info": "32 x 32px .png recommended [Learn more](https://intercom.help/someoneyouknow/en/articles/6208028-accessibility)"
        },
        "accessibility": {
          "header": "Accessibility",
          "show_accessibility": {
            "label": "Show accessibility mode toggle in header",
            "info": "Enable this toggle to show more contrast and larger fonts to your visitors."
          },
          "enable_accessibility_default": {
            "label": "Enable accessibility mode by default",
            "info": "This hides the toggle in the header and shows accessibility mode always."
          },
          "rtl": {
            "label": "Enable RTL text direction",
            "info": "Arabic, Hebrew and Persian are the most widespread RTL writing systems in modern times. Online stores in RTL language are recommended to enable this setting. [Learn more ](https://intercom.help/someoneyouknow/en/articles/6208028-accessibility)"
          },
          "back_to_top_button": {
            "label": "Enable 'Back to top' button"
          }
        }
      }
    },
    "logos": {
      "name": "Logos",
      "settings": {
        "logo": {
          "label": "Logo image"
        },
        "logo_width": {
          "label": "Logo width",
          "info": "To keep the header layout as optimized as possible, the logo has a maximum height of 130px."
        },
        "mobile": {
          "header": "Mobile logo image",
          "mobile_logo": {
            "label": "Mobile logo image",
            "info": "Leave empty if you want to use the same logo as uploaded for desktop. The logo is always centered on mobile."
          },
          "logo_width_mobile": {
            "label": "Mobile logo width",
            "info": "To keep the header layout as optimized as possible, the logo has a maximum height of 88px."
          }
        }
      }
    },
    "layout": {
      "name": "Layout",
      "settings": {
        "width": {
          "label": "Content maximum width",
          "info": "When you choose 2000px, the content will be full width."
        }
      }
    },
    "colors": {
      "name": "Colors",
      "settings": {
        "color_scheme": {
          "background": {
            "header": "Background",
            "primary_bg": {
              "label": "Primary background"
            },
            "primary_bg_gradient": {
              "label": "Primary background gradient",
              "info": "Primary background gradient replaces primary background color where possible."
            },
            "secondary_bg": {
              "label": "Secondary background"
            }
          },
          "typography": {
            "header": "Typography",
            "title_color": {
              "label": "Headings"
            },
            "title_color_gradient": {
              "label": "Headings gradient",
              "info": "Headings gradient replaces headings color where possible."
            },
            "primary_fg": {
              "label": "Text"
            },
            "link_hover_color": {
              "label": "Link hover color"
            }
          },
          "primary_button": {
            "header": "Primary button",
            "primary_button_bg": {
              "label": "Background"
            },
            "primary_button_fg": {
              "label": "Label"
            }
          },
          "secondary_button": {
            "header": "Secondary button",
            "secondary_button_bg": {
              "label": "Background"
            },
            "secondary_button_fg": {
              "label": "Label"
            }
          },
          "tertiary_button": {
            "header": "Tertiary button",
            "tertiary_button_bg": {
              "label": "Background"
            },
            "tertiary_button_fg": {
              "label": "Label"
            }
          },
          "inputs": {
            "header": "Inputs",
            "input_bg": {
              "label": "Background"
            },
            "input_fg": {
              "label": "Text"
            }
          },
          "general": {
            "header": "General",
            "primary_bd": {
              "label": "Border"
            },
            "accent": {
              "label": "Accent"
            },
            "accent_gradient": {
              "label": "Accent gradient",
              "info": "Accent gradient replaces accent color where possible."
            }
          }
        },
        "default_color_scheme": {
          "label": "Default color scheme",
          "info": "This color scheme will be used for all sections and pages that don't have a specific color scheme set."
        },
        "general": {
          "header": "General",
          "positive_vibes": {
            "label": "Success"
          },
          "negative_vibes": {
            "label": "Critical"
          }
        },
        "text_selection": {
          "header": "Text Selection",
          "text_selection_bg": {
            "label": "Selection background",
            "info": "Background color when text is selected/highlighted"
          },
          "text_selection_color": {
            "label": "Selection text color",
            "info": "Text color when text is selected/highlighted"
          }
        },
        "buttons": {
          "header": "Buttons",
          "buy_button_color": {
            "label": "Buy button"
          },
          "buy_button_text_color": {
            "label": "Buy button label"
          },
          "dynamic_buy_button_color": {
            "label": "Dynamic buy button"
          },
          "dynamic_buy_button_text_color": {
            "label": "Dynamic buy button label"
          },
          "preorder_button_color": {
            "label": "Pre-order button"
          },
          "preorder_button_text_color": {
            "label": "Pre-order button label"
          },
          "unavailable_button_color": {
            "label": "Out of stock button"
          },
          "unavailable_button_text_color": {
            "label": "Out of stock button label"
          },
          "checkout_button_style": {
            "label": "Checkout button style"
          }
        },
        "dropdown_color": {
          "label": "Menus and drawers",
          "info": "This is the color scheme of your mega menu or dropdown menu. Mobile menu, search drawer, side cart, filters, shop the look drawer, quickshop cart, other drawers and dropdowns will automatically use this color scheme."
        },
        "product_prices": {
          "header": "Product prices",
          "price_color": {
            "label": "Price"
          },
          "compare_at_price_color": {
            "label": "Sale"
          }
        },
        "product_label": {
          "header": "Product label",
          "paragraph": "Click [here](https://intercom.help/someoneyouknow/en/articles/8789852-products) for information about adding labels.",
          "product_label_color": {
            "label": "Background"
          },
          "product_label_text_color": {
            "label": "Label text"
          },
          "sale_label_color": {
            "label": "Sale label background"
          },
          "sale_label_text_color": {
            "label": "Sale label text"
          }
        }
      }
    },
    "typography": {
      "name": "Typography",
      "settings": {
        "fonts": {
          "header": "Fonts",
          "primary_font": {
            "label": "Primary font"
          },
          "secondary_font": {
            "label": "Secondary font"
          },
          "paragraph": "Not all font families support multiple font weights."
        },
        "custom_fonts": {
          "header": "Custom fonts",
          "paragraph": "Custom fonts can be added in two ways: by uploading a font file or by using an Adobe font snippet. [More info](https://intercom.help/someoneyouknow/en/articles/8817864-typography-and-custom-fonts)",
          "enable_custom_primary_font": {
            "label": "Enable custom primary font"
          },
          "custom_primary_font_name": {
            "label": "Custom primary font name"
          },
          "custom_primary_font_file": {
            "label": "Custom primary font file",
            "info": "Upload your font file in the 'Files' section of your Shopify admin."
          },
          "custom_primary_font_snippet": {
            "label": "Custom primary font snippet",
            "info": "The snippet looks like this: <link rel=\"stylesheet\" href=\"https://use.typekit.net/abc123.css\">"
          },
          "enable_custom_secondary_font": {
            "label": "Enable custom secondary font"
          },
          "custom_secondary_font_name": {
            "label": "Custom secondary font name"
          },
          "custom_secondary_font_file": {
            "label": "Custom secondary font file",
            "info": "Upload your font file in the 'Files' section of your Shopify admin."
          },
          "custom_secondary_font_snippet": {
            "label": "Custom secondary font snippet",
            "info": "The snippet looks like this: <link rel=\"stylesheet\" href=\"https://use.typekit.net/abc123.css\">"
          }
        },
        "headings": {
          "header": "Headings",
          "heading_line_height": {
            "label": "Headings line height"
          },
          "primary_case": {
            "label": "Primary font case",
            "options__1": {
              "label": "Standard"
            },
            "options__2": {
              "label": "UPPERCASE"
            }
          },
          "primary_letter_spacing": {
            "label": "Primary font letter spacing"
          },
          "secondary_case": {
            "label": "Secondary font case",
            "options__1": {
              "label": "Standard"
            },
            "options__2": {
              "label": "UPPERCASE"
            }
          },
          "secondary_letter_spacing": {
            "label": "Secondary font letter spacing"
          },
          "global_title_size": {
            "label": "Default heading font size",
            "info": "Applicable to static pages like the account login and register pages."
          }
        },
        "body": {
          "header": "Body",
          "body_font": {
            "label": "Body text font",
            "options__1": {
              "label": "Primary font"
            },
            "options__2": {
              "label": "Secondary font"
            }
          },
          "body_font_size": {
            "label": "Body font size",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            },
            "options__4": {
              "label": "Extra large"
            }
          },
          "body_case": {
            "label": "Body font case",
            "options__1": {
              "label": "Standard"
            },
            "options__2": {
              "label": "UPPERCASE"
            }
          },
          "body_letter_spacing": {
            "label": "Letter spacing"
          },
          "body_line_height": {
            "label": "Line height"
          }
        },
        "prices": {
          "header": "Product prices",
          "prices_font": {
            "label": "Product prices font",
            "options__1": {
              "label": "Primary font"
            },
            "options__2": {
              "label": "Secondary font"
            }
          },
          "prices_font_weight": {
            "label": "Product prices font weight",
            "options__1": {
              "label": "Light"
            },
            "options__2": {
              "label": "Regular"
            },
            "options__3": {
              "label": "Bold"
            }
          }
        },
        "breadcrumbs": {
          "header": "Breadcrumbs",
          "breadcrumbs_font": {
            "label": "Breadcrumbs font",
            "options__1": {
              "label": "Primary font"
            },
            "options__2": {
              "label": "Secondary font"
            }
          },
          "breadcrumbs_font_size": {
            "label": "Breadcrumbs font size",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            },
            "options__4": {
              "label": "Extra large"
            }
          }
        },
        "drawers": {
          "header": "Drawers",
          "drawers_font_size_heading": {
            "label": "Heading font size"
          }
        },
        "hyphens": {
          "header": "Hyphens",
          "enable_hyphens": {
            "label": "Enable hyphens",
            "info": "This will add hyphens to long words to prevent them from breaking the layout."
          }
        },
        "mobile": {
          "header": "Mobile",
          "body_font_size_mobile": {
            "label": "Body font size",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            },
            "options__4": {
              "label": "Extra large"
            }
          }
        }
      }
    },
    "images": {
      "name": "Images",
      "settings": {
        "product_images": {
          "header": "Product images",
          "product_image_ratio": {
            "label": "Image ratio",
            "options__1": {
              "label": "Portrait"
            },
            "options__2": {
              "label": "Square"
            },
            "options__3": {
              "label": "Landscape"
            }
          },
          "fill_product_images": {
            "label": "Fill images"
          },
          "show_secondary_image": {
            "label": "Show second image on hover"
          }
        },
        "multiply": {
          "header": "Pass through colors",
          "paragraph": "Apply this effect on your product or collection images, it will hide the white background. Recommended when using a colored general background and images with a white background.",
          "multiply_product_images": {
            "label": "Pass through colors on product images",
            "options__1": {
              "label": "Disable pass through"
            },
            "options__2": {
              "label": "Enable pass through"
            },
            "options__3": {
              "label": "Enable pass through with a background color"
            }
          },
          "multiply_product_images_color_palette": {
            "label": "Pass through colors on product images background color"
          },
          "multiply_collection_images": {
            "label": "Pass through colors on collection images",
            "options__1": {
              "label": "Disable pass through"
            },
            "options__2": {
              "label": "Enable pass through"
            },
            "options__3": {
              "label": "Enable pass through with a background color"
            }
          },
          "multiply_collection_images_color_palette": {
            "label": "Pass through colors on collection images background color"
          }
        },
        "corner_radius": {
          "header": "Corner radius",
          "everything_rounded": {
            "label": "Show all images with round corners"
          }
        }
      }
    },
    "buttons": {
      "name": "Buttons",
      "settings": {
        "button_height": {
          "label": "Height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "button_style": {
          "label": "Styling",
          "options__1": {
            "label": "Fill, shadow"
          },
          "options__2": {
            "label": "Fill, no shadow"
          },
          "options__3": {
            "label": "No fill"
          }
        },
        "button_rounded": {
          "label": "Shape",
          "options__1": {
            "label": "Square"
          },
          "options__2": {
            "label": "Rounded"
          },
          "options__3": {
            "label": "Pill"
          }
        },
        "button_font": {
          "label": "Font",
          "options__1": {
            "label": "Primary font"
          },
          "options__2": {
            "label": "Secondary font"
          }
        },
        "button_font_weight": {
          "label": "Font weight"
        },
        "button_case": {
          "label": "Case",
          "options__1": {
            "label": "Standard"
          },
          "options__2": {
            "label": "UPPERCASE"
          }
        }
      }
    },
    "cart": {
      "name": "Cart drawer",
      "settings": {
        "enable_cart_drawer": {
          "label": "Enable cart drawer",
          "info": "When adding products to the cart or when clicking the cart icon in the header, this will open a cart side drawer instead of redirecting to the cart page."
        },
        "empty_cart_text": {
          "label": "Empty cart text"
        },
        "cart_drawer_undo": {
          "header": "Undo deleted products",
          "enable_cart_drawer_undo_remove": {
            "label": "Enable undo deleted products",
            "info": "Enable this if you want to give a visitor the option if a product is removed, it can be undone."
          },
          "enable_cart_drawer_undo_remove_delay": {
            "label": "Enable timer for undo deleted products",
            "info": "This will show the undo feature for a certain amount of seconds."
          },
          "cart_drawer_undo_remove_delay": {
            "label": "Timer undo deleted products"
          }
        },
        "cart_drawer_checkout_button": {
          "label": "Show checkout button"
        },
        "cart_drawer_order_notes": {
          "label": "Show order notes"
        },
        "upsell": {
          "header": "Cart upsell",
          "enable_cart_drawer_upsell_complementary": {
            "label": "Show cart complementary products",
            "info": "Products can be selected in Shopify's Search & Discovery app."
          },
          "enable_cart_drawer_upsell_related": {
            "label": "Show cart related products"
          },
          "enable_cart_drawer_upsell_variants": {
            "label": "Show variant picker",
            "info": "Not applicable for products with more than 250 variants."
          }
        }
      }
    },
    "trustbadge": {
      "name": "Trustbadge",
      "settings": {
        "show_trustbadge": {
          "label": "Show secure and safe checkout trustbadge",
          "info": "It will be shown automatically in the cart drawer, cart page and it can also be added as a block on the productpage and on the featured product section."
        },
        "trustbadge_image": {
          "label": "Payment gateway providers",
          "options__1": {
            "label": "None"
          },
          "options__2": {
            "label": "Amazon Pay"
          },
          "options__3": {
            "label": "Authorize.net"
          },
          "options__4": {
            "label": "Klarna"
          },
          "options__5": {
            "label": "Opayo"
          },
          "options__6": {
            "label": "PayPal"
          },
          "options__7": {
            "label": "Shop Pay"
          },
          "options__8": {
            "label": "Skrill"
          },
          "options__9": {
            "label": "Stripe"
          }
        },
        "trustbadge_custom_image": {
          "label": "Custom image",
          "info": "Upload your own custom secure image to show next to the secure & safe checkout text. .png or .svg file recommended."
        },
        "trustbadge_text": {
          "label": "Secure & safe checkout text"
        }
      }
    },
    "search_drawer": {
      "name": "Search drawer",
      "settings": {
        "enable_search_drawer": {
          "label": "Enable live search results"
        },
        "search_behaviour": {
          "header": "Search behaviour:",
          "search_drawer_enable_suggestions": {
            "label": "Enable search suggestions"
          },
          "search_drawer_show_price": {
            "label": "Show price"
          },
          "search_drawer_show_vendor": {
            "label": "Show vendor"
          }
        },
        "resources": {
          "header": "Search for:",
          "search_drawer_enable_collections": {
            "label": "Collections"
          },
          "search_drawer_enable_products": {
            "label": "Products"
          },
          "search_drawer_enable_pages": {
            "label": "Pages"
          },
          "search_drawer_enable_articles": {
            "label": "Blog posts"
          }
        },
        "popular_collections": {
          "header": "Popular collections",
          "search_drawer_popular_collections_title": {
            "label": "Heading",
            "info": "Promote popular collections and/or important products when search bar is empty."
          },
          "search_drawer_popular_collections": {
            "label": "Collections"
          }
        },
        "popular_products": {
          "header": "Popular products",
          "search_drawer_popular_products_title": {
            "label": "Heading"
          },
          "search_drawer_popular_products": {
            "label": "Products"
          }
        }
      }
    },
    "products": {
      "name": "Products",
      "settings": {
        "productcards_text_alignment": {
          "label": "Productcards text alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "labels": {
          "header": "Product labels",
          "show_sale_label": {
            "label": "Show 'Sale' label on products"
          },
          "sale_label_price": {
            "label": "Show saved amount on 'Sale' label",
            "options__1": {
              "label": "None"
            },
            "options__2": {
              "label": "Percentage"
            },
            "options__3": {
              "label": "Price difference"
            }
          },
          "show_stock_label": {
            "label": "Show 'Last stock!' on products",
            "info": "When stock is running low on a product, inform your visitors about this. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          },
          "stock_label_qty": {
            "label": "Stock level to show ‘Last stock!’ label"
          },
          "show_price_varies": {
            "label": "Show price range on product cards",
            "info": "When different variants of a product have a different price range, this will show on your product card."
          },
          "product_custom_label": {
            "label": "Metafield: custom label",
            "info": "Create a metafield for a custom label and enter the namespace and key here (e.g. custom.label) [Learn more](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          }
        },
        "stock": {
          "header": "Product stock",
          "show_product_stock": {
            "label": "Show available product stock"
          },
          "show_product_stock_qty": {
            "label": "Stock level to show available product stock",
            "info": "You can also check 'always show available product stock' below to override this."
          },
          "show_product_stock_always": {
            "label": "Always show available product stock"
          }
        },
        "ratings": {
          "header": "Product ratings",
          "show_product_rating": {
            "label": "Show product ratings",
            "info": "The ratings will be automatically pulled from your review app."
          }
        },
        "vendor": {
          "header": "Product vendor",
          "show_vendor": {
            "label": "Show product vendor"
          }
        },
        "preorder": {
          "header": "Pre-order",
          "show_preorder": {
            "label": "Show pre-order button",
            "info": "This shows a pre-order button as buy button. Only applicable when product has 'continue selling when out of stock' setting active."
          },
          "show_preorder_inventory": {
            "label": "Show inventory for pre-order products"
          },
          "preorder_button_text": {
            "label": "Button label"
          }
        },
        "short_product_description": {
          "header": "Product short description",
          "product_short_description": {
            "label": "Show product short description",
            "info": "Show a part of the product description or use a custom text.",
            "options__1": {
              "label": "None"
            },
            "options__2": {
              "label": "Part of the product description"
            },
            "options__3": {
              "label": "Custom text"
            }
          },
          "product_short_description_text": {
            "label": "Metafield: custom short description",
            "info": "Create a metafield for a custom short description and enter the namespace and key here (e.g. custom.short_description) [Learn more](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          }
        },
        "titles": {
          "header": "Titles",
          "product_titles_caps": {
            "label": "Show product titles in ALL CAPS"
          }
        },
        "color_swatches": {
          "header": "Color swatches",
          "enable_color_swatches": {
            "label": "Enable color swatches",
            "info": "[More information](https://intercom.help/someoneyouknow/en/articles/6208038-products)"
          },
          "color_swatch_name": {
            "label": "Color swatches name",
            "info": "The option name used for the color swatches, for example 'Color' or 'Colour'. You can add multiple names by entering each on a separate line."
          },
          "color_swatch_hexes": {
            "label": "Custom colors",
            "info": "For each color: enter on a separate line the name of the color, then a colon (:) followed by the hex code (for example: Red: #DC143C). Leave out the hex code to use a custom image instead."
          },
          "color_swatches_variant_image": {
            "label": "Show variant images instead of colors on productcards"
          }
        }
      }
    },
    "shipping_delivery": {
      "name": "Shipping and delivery",
      "settings": {
        "free_shipping": {
          "header": "Free shipping",
          "enable_free_shipping": {
            "label": "Enable free shipping",
            "info": "Note that this feature does not work well when multi-currency is enabled. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208035-shipping-and-delivery)"
          },
          "free_shipping_amount": {
            "label": "Minimum order amount to free shipping"
          }
        },
        "deliverytime": {
          "header": "Delivery time",
          "paragraph": "Use metafields to show product delivery times. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208035-shipping-and-delivery)",
          "product_deliverytime_in_stock": {
            "label": "Metafield deliverytime - in stock"
          },
          "product_deliverytime_not_in_stock": {
            "label": "Metafield deliverytime - out of stock"
          },
          "paragraph__2": "Do you want to show default deliverytimes for products where no delivery time-metafield is set up? Enter them below.",
          "default_product_deliverytime_in_stock": {
            "label": "Default deliverytime - in stock"
          },
          "default_product_deliverytime_not_in_stock": {
            "label": "Default deliverytime - not in stock"
          },
          "product_deliverytime_info": {
            "label": "Metafield extra info for deliverytime"
          },
          "default_product_deliverytime_info": {
            "label": "Default extra info for deliverytime",
            "info": "This will show an info icon next to your delivery- and shipping messages on productcards and productpage. The text will show in a popup."
          },
          "show_deliverytime_always": {
            "label": "Show delivery time for products without inventory tracking",
            "info": "When inventory tracking is disabled for a product, the 'in stock' delivery time will be shown."
          }
        },
        "shipping_timer": {
          "header": "Shipping timer",
          "paragraph": "If you handle ‘same day shipping’, show a timer how much time visitors have left to order your products to be shipped today.",
          "shipping_timer_show_until": {
            "label": "Before what time should your customers order?",
            "options__1": {
              "label": "1AM"
            },
            "options__2": {
              "label": "2AM"
            },
            "options__3": {
              "label": "3AM"
            },
            "options__4": {
              "label": "4AM"
            },
            "options__5": {
              "label": "5AM"
            },
            "options__6": {
              "label": "6AM"
            },
            "options__7": {
              "label": "7AM"
            },
            "options__8": {
              "label": "8AM"
            },
            "options__9": {
              "label": "9AM"
            },
            "options__10": {
              "label": "10AM"
            },
            "options__11": {
              "label": "11AM"
            },
            "options__12": {
              "label": "12PM"
            },
            "options__13": {
              "label": "1PM"
            },
            "options__14": {
              "label": "2PM"
            },
            "options__15": {
              "label": "3PM"
            },
            "options__16": {
              "label": "4PM"
            },
            "options__17": {
              "label": "5PM"
            },
            "options__18": {
              "label": "6PM"
            },
            "options__19": {
              "label": "7PM"
            },
            "options__20": {
              "label": "8PM"
            },
            "options__21": {
              "label": "9PM"
            },
            "options__22": {
              "label": "10PM"
            },
            "options__23": {
              "label": "11PM"
            },
            "options__24": {
              "label": "11:59PM"
            }
          },
          "shipping_timer_show_from": {
            "label": "Starting from what time should the timer show up on your page?",
            "options__1": {
              "label": "12AM"
            },
            "options__2": {
              "label": "1AM"
            },
            "options__3": {
              "label": "2AM"
            },
            "options__4": {
              "label": "3AM"
            },
            "options__5": {
              "label": "4AM"
            },
            "options__6": {
              "label": "5AM"
            },
            "options__7": {
              "label": "6AM"
            },
            "options__8": {
              "label": "7AM"
            },
            "options__9": {
              "label": "8AM"
            },
            "options__10": {
              "label": "9AM"
            },
            "options__11": {
              "label": "10AM"
            },
            "options__12": {
              "label": "11AM"
            },
            "options__13": {
              "label": "12PM"
            },
            "options__14": {
              "label": "1PM"
            },
            "options__15": {
              "label": "2PM"
            },
            "options__16": {
              "label": "3PM"
            },
            "options__17": {
              "label": "4PM"
            },
            "options__18": {
              "label": "5PM"
            },
            "options__19": {
              "label": "6PM"
            },
            "options__20": {
              "label": "7PM"
            },
            "options__21": {
              "label": "8PM"
            },
            "options__22": {
              "label": "9PM"
            },
            "options__23": {
              "label": "10PM"
            },
            "options__24": {
              "label": "11PM"
            }
          },
          "shipping_timer_show_unavailable": {
            "label": "Show shipping timer when product is unavailable"
          },
          "paragraph__2": "On which days do you want to show the timer?",
          "shipping_timer_enable_monday": {
            "label": "Monday"
          },
          "shipping_timer_enable_tuesday": {
            "label": "Tuesday"
          },
          "shipping_timer_enable_wednesday": {
            "label": "Wednesday"
          },
          "shipping_timer_enable_thursday": {
            "label": "Thursday"
          },
          "shipping_timer_enable_friday": {
            "label": "Friday"
          },
          "shipping_timer_enable_saturday": {
            "label": "Saturday"
          },
          "shipping_timer_enable_sunday": {
            "label": "Sunday"
          }
        }
      }
    },
    "social_media": {
      "name": "Social media",
      "settings": {
        "whatsapp": {
          "label": "WhatsApp phone number"
        },
        "header": "Social media accounts",
        "paragraph": "You can set up your social links in the ['Brand' page](https://help.shopify.com/en/manual/promoting-marketing/managing-brand-assets) in the Shopify settings, but you can also choose to connect them here.",
        "social_instagram": {
          "label": "Instagram"
        },
        "social_instagram_name": {
          "label": "Instagram name"
        },
        "social_pinterest": {
          "label": "Pinterest"
        },
        "social_pinterest_name": {
          "label": "Pinterest name"
        },
        "social_youtube": {
          "label": "YouTube"
        },
        "social_youtube_name": {
          "label": "YouTube name"
        },
        "social_facebook": {
          "label": "Facebook"
        },
        "social_facebook_name": {
          "label": "Facebook name"
        },
        "social_twitter": {
          "label": "X"
        },
        "social_twitter_name": {
          "label": "X name"
        },
        "social_tiktok": {
          "label": "TikTok"
        },
        "social_tiktok_name": {
          "label": "TikTok name"
        },
        "social_tumblr": {
          "label": "Tumblr"
        },
        "social_tumblr_name": {
          "label": "Tumblr name"
        },
        "social_snapchat": {
          "label": "Snapchat"
        },
        "social_snapchat_name": {
          "label": "Snapchat name"
        }
      }
    },
    "newsletter": {
      "name": "Newsletter popup",
      "settings": {
        "newsletter_popup": {
          "header": "Newsletter popup",
          "show_newsletterpopup": {
            "label": "Enable newsletter popup"
          },
          "newsletter_popup_testmode": {
            "label": "Enable test mode",
            "info": "This shows the popup on every refresh, you should only use this for editing the popup. Make sure 'Test mode' is disabled when publishing your theme."
          },
          "newsletter_popup_seconds": {
            "label": "Show the popup after"
          },
          "newsletter_popup_image": {
            "label": "Image",
            "info": "960 x 900px .png recommended"
          }
        },
        "checkbox": {
          "header": "Checkbox",
          "enable_newsletter_terms_checkbox": {
            "label": "Show checkbox"
          },
          "newsletter_terms_text": {
            "label": "Label for the checkbox",
            "info": "For example, place a link to your privacy policy."
          }
        }
      }
    },
    "age_verify_popup": {
      "name": "Age verification popup",
      "settings": {
        "show_age_verify_popup": {
          "label": "Enable age verification popup",
          "info": "[Learn more](https://intercom.help/someoneyouknow/en/articles/6686994-age-verification-popup)"
        },
        "age_verify_popup_testmode": {
          "label": "Enable test mode",
          "info": "This shows the popup on every refresh, you should only use this for editing the popup. Make sure 'Test mode' is disabled when publishing your theme."
        },
        "age_verify_popup_image": {
          "label": "Image",
          "info": "470 x 110px .png recommended"
        },
        "verify": {
          "header": "Verification question",
          "age_verify_popup_title": {
            "label": "Heading"
          },
          "age_verify_popup_text": {
            "label": "Text"
          },
          "age_verify_popup_cookie_text": {
            "label": "Cookies text (optional)",
            "info": "The text will automatically be shown to visitors when this is obliged. Make sure you have enabled 'Limit tracking for customers in Europe'. [Learn more](https://help.shopify.com/en/manual/your-account/privacy/cookies#tracking-european-customers-and-gdpr-compliance)"
          },
          "age_verify_popup_accept_text": {
            "label": "Approve button label"
          },
          "age_verify_popup_deny_text": {
            "label": "Decline button label"
          }
        },
        "denied": {
          "header": "Declined",
          "paragraph": "This will display when the customer has declined the age verification.",
          "age_verify_popup_denied_title": {
            "label": "Heading"
          },
          "age_verify_popup_denied_text": {
            "label": "Text"
          }
        }
      }
    },
    "cookies": {
      "name": "Cookies",
      "settings": {
        "show_cookiebanner": {
          "label": "Show cookie banner/popup",
          "info": "The cookie banner or popup will automatically be shown to visitors when this is obliged, and if no age verification popup is enabled. Make sure you have enabled 'Limit tracking for customers in Europe'. [Learn more](https://help.shopify.com/en/manual/your-account/privacy/cookies#tracking-european-customers-and-gdpr-compliance)",
          "options__1": {
            "label": "Banner"
          },
          "options__2": {
            "label": "Popup"
          },
          "options__3": {
            "label": "None"
          }
        },
        "cookiebanner_testmode": {
          "label": "Enable test mode",
          "info": "This shows the banner/popup on every refresh, you should only use this for editing. Make sure 'Test mode' is disabled when publishing your theme."
        },
        "cookiebanner_image": {
          "label": "Image in the cookie popup",
          "info": "Only applicable in the popup version of the cookie banner. (580 x 540px .png recommended)"
        },
        "cookiebanner_title": {
          "label": "Heading",
          "info": "Only applicable in the popup version of the cookie banner."
        },
        "cookiebanner_text": {
          "label": "Text",
          "info": "For example, place a link to your privacy policy."
        }
      }
    },
    "custom_scripts": {
      "name": "Custom scripts",
      "settings": {
        "custom_script_for_head": {
          "label": "Custom script for head tag",
          "info": "This setting allows you to insert scripts into the head tag of the theme code."
        },
        "custom_script_for_body": {
          "label": "Custom script for body tag",
          "info": "This setting allows you to insert scripts into the body tag of the theme code."
        }
      }
    },
    "shop_the_look": {
      "name": "Shop the look",
      "settings": {
        "paragraph": "Turn every image banner across the entire store into a shop the look banner simply by adding \nproducts in the banner settings. A drawer will open with the selected productcards",
        "shop_the_look_title": {
          "label": "Heading"
        },
        "shop_the_look_size": {
          "label": "Heading size",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          },
          "options__4": {
            "label": "Extra large"
          }
        },
        "shop_the_look_subtitle": {
          "label": "Subtitle"
        },
        "shop_the_look_items": {
          "label": "Products per row",
          "info": "If list includes more products than shown, it will be a slider automatically. Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/8789841-shop-the-look)"
        }
      }
    }
  },
  "sections": {
    "section": {
      "name": "Custom section",
      "settings": {
        "overlay_opacity": {
          "label": "Background opacity"
        }
      },
      "presets": {
        "custom_section": "Custom section"
      }
    },
    "background": {
      "name": "Background",
      "settings": {
        "overlay_opacity": {
          "label": "Background opacity"
        },
        "height": {
          "label": "Banner height",
          "options__1": {
            "label": "Extra small"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "enable_custom_height": {
          "label": "Enable custom height"
        },
        "custom_height": {
          "label": "Custom height",
          "info": "Only applicable when ‘custom height’ is enabled."
        },
        "mobile": {
          "height_mobile": {
            "label": "Banner height",
            "options__1": {
              "label": "Extra small"
            },
            "options__2": {
              "label": "Small"
            },
            "options__3": {
              "label": "Medium"
            },
            "options__4": {
              "label": "Large"
            }
          },
          "enable_custom_height_mobile": {
            "label": "Enable custom height"
          },
          "custom_height_mobile": {
            "label": "Custom height",
            "info": "Only applicable when ‘custom height’ is enabled."
          }
        }
      },
      "presets": {
        "name": "Background"
      }
    },
    "grid": {
      "name": "Flexible grid banners",
      "settings": {
        "number_of_columns": {
          "label": "Columns per row",
          "options__1": {
            "label": "4"
          },
          "options__2": {
            "label": "6"
          }
        }
      },
      "blocks": {
        "image": {
          "settings": {
            "size": {
              "header": "Banner size",
              "cols": {
                "label": "Columns",
                "info": "If columns per row '4' is selected, values above 4 will be ignored."
              },
              "rows": {
                "label": "Rows"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Flexible grid banners"
      }
    },
    "gallery": {
      "name": "Image gallery",
      "settings": {
        "items_layout": {
          "label": "Layout",
          "options__1": {
            "label": "Collage"
          },
          "options__2": {
            "label": "Collage mirrored"
          },
          "options__3": {
            "label": "Columns"
          }
        },
        "items_width": {
          "label": "Number of columns"
        },
        "space_between": {
          "label": "Space between banners"
        },
        "height": {
          "info": "Based on the first banner in each row."
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "image_zoom": {
          "label": "Show zoom effect on hover on banner images"
        },
        "image_move": {
          "label": "Show move animation on hover on banner",
          "info": "Not applicable when 'Space between banners' is less than 10px."
        },
        "show_content_below": {
          "label": "Show content below banner images"
        },
        "show_content_on_hover": {
          "label": "Show content on hover on banner images"
        },
        "content_on_hover_color_palette": {
          "label": "Color scheme on hover on banner images"
        },
        "mobile": {
          "header": "Mobile layout",
          "layout_mobile": {
            "label": "Layout",
            "options__1": {
              "label": "In rows"
            },
            "options__2": {
              "label": "Swipe"
            }
          },
          "mobile_height": {
            "label": "Banner height",
            "options__1": {
              "label": "Extra small"
            },
            "options__2": {
              "label": "Small"
            },
            "options__3": {
              "label": "Medium"
            },
            "options__4": {
              "label": "Large"
            }
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Image banner",
          "settings": {
            "image": {
              "label": "Image"
            },
            "video": {
              "label": "Video"
            },
            "fill_images": {
              "label": "Fill image/video"
            },
            "overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "text_position": {
              "label": "Content position",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Center left"
              },
              "options__5": {
                "label": "Center center"
              },
              "options__6": {
                "label": "Center right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            },
            "mobile": {
              "header": "Mobile layout",
              "image_mobile": {
                "label": "Mobile image (optional)"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Image gallery"
      }
    },
    "promo_gallery": {
      "name": "Promo gallery",
      "settings": {
        "items_width": {
          "label": "Columns per row"
        },
        "height": {
          "info": "Based on the first image/video in each row."
        },
        "space_between": {
          "label": "Space between banners"
        },
        "image_zoom": {
          "label": "Show zoom effect on hover on banner images"
        },
        "shadow": {
          "label": "Show shadow"
        },
        "banners": {
          "header": "Promo banners",
          "title_size_blocks": {
            "label": "Heading size"
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "layout_mobile": {
            "label": "Layout",
            "options__1": {
              "label": "In rows"
            },
            "options__2": {
              "label": "Swipe"
            }
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Promo banner",
          "settings": {
            "image": {
              "label": "Image"
            },
            "video": {
              "label": "Video"
            },
            "overlay_opacity": {
              "label": "Background opacity"
            },
            "overlay_opacity_img": {
              "label": "Image/video overlay opacity"
            },
            "text_position": {
              "label": "Content position",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Left center"
              },
              "options__4": {
                "label": "Center center"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Promo gallery"
      }
    },
    "image_with_products": {
      "name": "Image banner + products",
      "settings": {
        "image": {
          "label": "Image"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Image left, products right"
          },
          "options__2": {
            "label": "Products left, image right"
          }
        },
        "text_position": {
          "label": "Content position",
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Center left"
          },
          "options__5": {
            "label": "Center center"
          },
          "options__6": {
            "label": "Center right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          }
        },
        "products": {
          "label": "Products"
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "width_mobile": {
            "label": "Banner width",
            "options__1": {
              "label": "Full width"
            },
            "options__2": {
              "label": "Boxed width"
            }
          }
        }
      },
      "presets": {
        "name": "Image banner + products"
      }
    },
    "image_with_text": {
      "name": "Image with text",
      "settings": {
        "image": {
          "label": "Image"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Image left, content right"
          },
          "options__2": {
            "label": "Image right, content left"
          }
        },
        "text_position": {
          "label": "Content position",
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Center left"
          },
          "options__5": {
            "label": "Center center"
          },
          "options__6": {
            "label": "Center right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          }
        },
        "paragraph": "Customize your content using blocks. The positon will be automatically optimized for mobile."
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "title": {
          "name": "Heading"
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Information"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "first_link": {
              "header": "First button"
            },
            "second_link": {
              "header": "Second button"
            }
          }
        }
      },
      "presets": {
        "name": "Image with text"
      }
    },
    "video_with_text": {
      "name": "Video banner",
      "settings": {
        "image": {
          "label": "Cover image (optional)",
          "info": "Select an image to play the video in a popup, leave blank to play video on the page. 760 x 570px recommended."
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Video left, content right"
          },
          "options__2": {
            "label": "Video right, content left"
          }
        },
        "text_position": {
          "label": "Content position",
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Center left"
          },
          "options__5": {
            "label": "Center center"
          },
          "options__6": {
            "label": "Center right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          }
        },
        "paragraph": "Customize your content using blocks. The positon will be automatically optimized for mobile.",
        "video_url": {
          "label": "Video URL",
          "info": "Accepts Vimeo or YouTube links. You can also upload or select a video from your media library by using the 'Video' setting below."
        },
        "video_alt_text": {
          "label": "Video URL alt text",
          "info": "Describe the video to make it accessible for customers using screen readers."
        },
        "video": {
          "label": "Video"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "title": {
          "name": "Heading"
        },
        "text": {
          "name": "Text",
          "settings": {
            "text": {
              "label": "Text"
            }
          }
        },
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Information"
            }
          }
        },
        "buttons": {
          "name": "Buttons",
          "settings": {
            "first_link": {
              "header": "First button"
            },
            "second_link": {
              "header": "Second button"
            }
          }
        }
      },
      "presets": {
        "name": "Video banner"
      }
    },
    "image_comparison": {
      "name": "Image comparison",
      "settings": {
        "image_before": {
          "label": "Before image",
          "info": "For best results, use an image with a 3:2 aspect ratio. [Learn more](https://intercom.help/someoneyouknow/en/articles/6686981-section-image-comparison)"
        },
        "image_after": {
          "label": "After image",
          "info": "Make sure the dimension match the before image."
        },
        "drag_color_palette": {
          "label": "Drag button/line color scheme"
        },
        "drag_direction": {
          "label": "Drag direction",
          "options__1": {
            "label": "Horizontal"
          },
          "options__2": {
            "label": "Vertical"
          }
        },
        "drag_position": {
          "label": "Initial drag position"
        },
        "labels": {
          "label": "Show labels"
        },
        "label_before": {
          "label": "Before image label"
        },
        "label_after": {
          "label": "After image label"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        }
      },
      "presets": {
        "name": "Image comparison"
      }
    },
    "collection_list": {
      "name": "Collection list",
      "settings": {
        "collections": {
          "label": "Collections"
        },
        "image_ratio": {
          "label": "Image ratio",
          "info": "Add images by editing your collections [Learn more](https://intercom.help/someoneyouknow/en/articles/6208057-section-collection-list)",
          "options__1": {
            "label": "Portrait"
          },
          "options__2": {
            "label": "Square"
          },
          "options__3": {
            "label": "Landscape"
          }
        },
        "fill_images": {
          "label": "Fill images"
        },
        "layout": {
          "label": "Layout",
          "info": "Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208057-section-collection-list)",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Grid"
          }
        },
        "custom_image_ratio": {
          "label": "Image ratio",
          "info": "For landscape images, select 75%. For square, select 100%. For portrait, select 125%."
        },
        "show_in_circle": {
          "label": "Show images in circle",
          "info": "Only applicable when image ratio is set to 100%."
        },
        "number_of_items": {
          "label": "Collections per row"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "collection_heading": {
          "header": "Heading collections"
        },
        "collection_title_position": {
          "label": "Heading position collections",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "show_collection_titles_underline": {
          "label": "Show underline collection titles"
        },
        "show_collection_titles": {
          "label": "Show collection titles"
        },
        "collection_title_size": {
          "label": "Heading size collections",
          "options__1": {
            "label": "Extra small"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          },
          "options__5": {
            "label": "Extra large"
          }
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "presets": {
        "name": "Collection list"
      }
    },
    "slideshow": {
      "name": "Slideshow",
      "settings": {
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "autoplay": {
          "label": "Enable autoplay"
        },
        "autoplay_seconds": {
          "label": "Seconds between slides"
        },
        "enable_controls": {
          "label": "Show play / pause button"
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "blocks": {
        "slide": {
          "name": "Slide",
          "settings": {
            "image": {
              "label": "Image",
              "info": "Slideshow can be used in three different layouts. For more information and recommended image sizes [click here](https://intercom.help/someoneyouknow/en/articles/6208058-section-slideshow)."
            },
            "layout": {
              "label": "Layout",
              "options__1": {
                "label": "Image left, content right"
              },
              "options__2": {
                "label": "Image right, content left"
              },
              "options__3": {
                "label": "Background image"
              }
            },
            "overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "video": {
              "label": "Video",
              "info": "Video plays in the page."
            },
            "text_position": {
              "label": "Content position",
              "options__1": {
                "label": "Top left"
              },
              "options__2": {
                "label": "Top center"
              },
              "options__3": {
                "label": "Top right"
              },
              "options__4": {
                "label": "Center left"
              },
              "options__5": {
                "label": "Center center"
              },
              "options__6": {
                "label": "Center right"
              },
              "options__7": {
                "label": "Bottom left"
              },
              "options__8": {
                "label": "Bottom center"
              },
              "options__9": {
                "label": "Bottom right"
              }
            },
            "buttons": {
              "heading": "Buttons",
              "first_link": {
                "header": "First button"
              },
              "second_link": {
                "header": "Second button"
              }
            },
            "mobile": {
              "header": "Mobile layout",
              "image_mobile": {
                "label": "Mobile image (optional)"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Slideshow"
      }
    },
    "image_hotspots": {
      "name": "Image hotspot banner",
      "settings": {
        "image": {
          "label": "Image"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "hotspot_height": {
          "label": "Hotspot height"
        },
        "hotspot_color_palette": {
          "label": "Hotspot color"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Image left, content right"
          },
          "options__2": {
            "label": "Image right, content left"
          }
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "enable_custom_height": {
          "label": "Enable custom minimal height"
        },
        "custom_height": {
          "label": "Custom minimal height"
        },
        "content": {
          "header": "Content"
        },
        "text_position": {
          "label": "Content position",
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Center left"
          },
          "options__5": {
            "label": "Center center"
          },
          "options__6": {
            "label": "Center right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "image_mobile": {
            "label": "Mobile image (optional)"
          }
        }
      },
      "blocks": {
        "hotspot": {
          "name": "Hotspot",
          "settings": {
            "product": {
              "label": "Product (optional)",
              "info": "Select a product or the content can be filled below."
            },
            "content": {
              "header": "Content",
              "paragraph": "Only applicable when there is no product selected.",
              "text_position": {
                "label": "Content position",
                "options__1": {
                  "label": "Left"
                },
                "options__2": {
                  "label": "Center"
                },
                "options__3": {
                  "label": "Right"
                }
              },
              "title": {
                "label": "Heading"
              },
              "text": {
                "label": "Text"
              }
            },
            "position": {
              "header": "Position",
              "position_left": {
                "label": "Horizontal position"
              },
              "position_top": {
                "label": "Vertical position"
              },
              "position_left_mobile": {
                "label": "Horizontal position mobile"
              },
              "position_top_mobile": {
                "label": "Vertical position mobile"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Image hotspot banner"
      }
    },
    "image_specs": {
      "name": "Image specs banner",
      "settings": {
        "image": {
          "label": "Image"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "hotspot_height": {
          "label": "Hotspot size"
        },
        "hotspot_style": {
          "label": "Hotspot style"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Image left, content right"
          },
          "options__2": {
            "label": "Image right, content left"
          }
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "enable_custom_height": {
          "label": "Enable custom minimal height"
        },
        "custom_height": {
          "label": "Custom minimal height"
        },
        "content": {
          "header": "Content"
        },
        "text_position": {
          "label": "Content position",
          "options__1": {
            "label": "Top left"
          },
          "options__2": {
            "label": "Top center"
          },
          "options__3": {
            "label": "Top right"
          },
          "options__4": {
            "label": "Center left"
          },
          "options__5": {
            "label": "Center center"
          },
          "options__6": {
            "label": "Center right"
          },
          "options__7": {
            "label": "Bottom left"
          },
          "options__8": {
            "label": "Bottom center"
          },
          "options__9": {
            "label": "Bottom right"
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "image_mobile": {
            "label": "Mobile image (optional)"
          }
        }
      },
      "blocks": {
        "hotspot": {
          "name": "Hotspot",
          "settings": {
            "text": {
              "label": "Text"
            },
            "position": {
              "header": "Position",
              "position_left": {
                "label": "Horizontal position"
              },
              "position_top": {
                "label": "Vertical position"
              },
              "position_left_mobile": {
                "label": "Horizontal position mobile"
              },
              "position_top_mobile": {
                "label": "Vertical position mobile"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Image specs banner"
      }
    },
    "contact_form": {
      "name": "Contact form",
      "settings": {
        "paragraph": "The following inputs are required for the form to submit successfully: Email and either a Message or Text input.",
        "alignment": {
          "label": "Position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "button_style": {
          "label": "Button style"
        },
        "compact": {
          "label": "Compact view"
        },
        "show_placeholder": {
          "label": "Show input field placeholders"
        }
      },
      "blocks": {
        "text": {
          "name": "Text",
          "settings": {
            "title": {
              "label": "Text label"
            },
            "placeholder": {
              "label": "Text placeholder"
            },
            "required": {
              "label": "Required field"
            }
          }
        },
        "textarea": {
          "name": "Message",
          "settings": {
            "title": {
              "label": "Message label"
            },
            "placeholder": {
              "label": "Message placeholder"
            },
            "required": {
              "label": "Required field"
            }
          }
        },
        "email": {
          "name": "Email",
          "settings": {
            "title": {
              "label": "Email label"
            },
            "placeholder": {
              "label": "Email placeholder"
            }
          }
        },
        "tel": {
          "name": "Telephone",
          "settings": {
            "title": {
              "label": "Telephone label"
            },
            "placeholder": {
              "label": "Telephone placeholder"
            },
            "required": {
              "label": "Required field"
            }
          }
        },
        "checkbox": {
          "name": "Checkbox",
          "settings": {
            "paragraph": "You can fill in maximum 6 options. If you want less options, fill in less. Not all 6 are required.",
            "title": {
              "label": "Checkbox label"
            },
            "options__1": {
              "label": "First option"
            },
            "options__2": {
              "label": "Second option"
            },
            "options__3": {
              "label": "Third option"
            },
            "options__4": {
              "label": "Fourth option"
            },
            "options__5": {
              "label": "Fifth option"
            },
            "options__6": {
              "label": "Sixth option"
            },
            "required": {
              "label": "Required field"
            }
          }
        },
        "radio": {
          "name": "Radio buttons",
          "settings": {
            "paragraph": "You can fill in maximum 6 options. If you want less options, fill in less. Not all 6 are required.",
            "title": {
              "label": "Radio buttons label"
            },
            "options__1": {
              "label": "First option"
            },
            "options__2": {
              "label": "Second option"
            },
            "options__3": {
              "label": "Third option"
            },
            "options__4": {
              "label": "Fourth option"
            },
            "options__5": {
              "label": "Fifth option"
            },
            "options__6": {
              "label": "Sixth option"
            },
            "required": {
              "label": "Required field"
            }
          }
        },
        "select": {
          "name": "Select",
          "settings": {
            "title": {
              "label": "Select label"
            },
            "options__1": {
              "label": "Option 1"
            },
            "options__2": {
              "label": "Option 2"
            },
            "options__3": {
              "label": "Option 3"
            },
            "options__4": {
              "label": "Option 4"
            },
            "options__5": {
              "label": "Option 5"
            },
            "options__6": {
              "label": "Option 6"
            },
            "required": {
              "label": "Required field"
            }
          }
        }
      },
      "presets": {
        "name": "Contact form"
      }
    },
    "counters": {
      "name": "Counters",
      "settings": {
        "image": {
          "label": "Background image",
          "info": "For best results with full background setting, use an image with a 3:2 aspect ratio. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208060-section-counters)"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "text_position": {
          "label": "Content position",
          "info": "Customize your content using blocks.",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          },
          "options__3": {
            "label": "Right"
          }
        },
        "numbers_boxed_overlay_opacity": {
          "label": "Background for the counters opacity"
        },
        "numbers_accent_color": {
          "label": "Show the counter digits in accent color"
        }
      },
      "blocks": {
        "counter": {
          "name": "Counter",
          "settings": {
            "number": {
              "label": "Heading"
            },
            "text": {
              "label": "Description"
            }
          }
        }
      },
      "presets": {
        "name": "Counters"
      }
    },
    "countdown": {
      "name": "Countdown",
      "settings": {
        "image": {
          "label": "Image"
        },
        "video": {
          "label": "Video"
        },
        "overlay_opacity": {
          "label": "Image/video overlay opacity"
        },
        "height": {
          "label": "Height",
          "options__1": {
            "label": "Extra small"
          },
          "options__2": {
            "label": "Small"
          },
          "options__3": {
            "label": "Medium"
          },
          "options__4": {
            "label": "Large"
          }
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "colors": {
          "header": "Colors",
          "countdown_bg_color": {
            "label": "Timer"
          },
          "countdown_text_color": {
            "label": "Timer digit"
          }
        },
        "content": {
          "header": "Content",
          "text_position": {
            "label": "Content position",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Center"
            }
          }
        },
        "countdown": {
          "header": "Countdown timer",
          "countdown_start": {
            "label": "Start date + time",
            "info": "When do you want the countdown timer to start? Before this time, the banner will not be shown."
          },
          "countdown_end": {
            "label": "End date + time"
          },
          "hide_banner_after_countdown": {
            "label": "Hide banner after timer ends"
          },
          "ended_title": {
            "label": "Heading after timer ends"
          },
          "ended_text": {
            "label": "Description after timer ends"
          }
        }
      },
      "presets": {
        "name": "Countdown"
      }
    },
    "logo_list": {
      "name": "Brand logo slider",
      "settings": {
        "number_of_items": {
          "label": "Logos per row",
          "info": "If list includes more logos than shown, it will be a slider automatically. Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208061-section-logo-list)"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        }
      },
      "blocks": {
        "logo": {
          "name": "Logo",
          "settings": {
            "image": {
              "label": "Logo",
              "info": "230 x 90px .png recommended."
            },
            "image_svg": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "link": {
              "label": "Link"
            }
          }
        }
      },
      "presets": {
        "name": "Brand logo slider"
      }
    },
    "newsletter": {
      "name": "Newsletter",
      "settings": {
        "image": {
          "label": "Background image (optional)",
          "info": "For best results with full background setting, use an image with a 3:2 aspect ratio. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208062-section-newsletter)"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "width": {
          "label": "Width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        },
        "checkbox": {
          "header": "Checkbox",
          "enable_newsletter_terms_checkbox": {
            "label": "Show checkbox"
          },
          "newsletter_terms_text": {
            "label": "Label for the checkbox",
            "info": "For example, place a link to your privacy policy."
          }
        }
      },
      "presets": {
        "name": "Newsletter"
      }
    },
    "recently_viewed_products": {
      "name": "Recently viewed products",
      "settings": {
        "number_of_items": {
          "label": "Products per row",
          "info": "If list includes more products than shown, it will be a slider automatically. Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208063-section-recently-viewed-products)"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        }
      },
      "presets": {
        "name": "Recently viewed products"
      }
    },
    "blog_posts": {
      "name": "Blog posts",
      "settings": {
        "blog": {
          "label": "Blog"
        },
        "number_of_items": {
          "label": "Blog posts per row",
          "info": "From 5 blog posts it generates another layout. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208065-section-blog-posts)"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "blog_posts": {
          "header": "Blog posts",
          "show_image": {
            "label": "Show featured image"
          },
          "show_date": {
            "label": "Show date"
          },
          "show_author": {
            "label": "Show author"
          },
          "show_excerpt": {
            "label": "Show excerpt"
          },
          "button_style_post": {
            "label": "Button style"
          }
        }
      },
      "presets": {
        "name": "Blog posts"
      }
    },
    "rich_text": {
      "name": "Rich text",
      "settings": {
        "overlay_opacity": {
          "label": "Background opacity"
        },
        "text_alignment": {
          "label": "Text alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "content_width": {
          "label": "Content width",
          "info": "When you choose 1280px, the content will be full width. Image/video width can be selected separately within the block settings."
        },
        "height": {
          "label": "Banner height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "width": {
          "label": "Banner width",
          "options__1": {
            "label": "Boxed width"
          },
          "options__2": {
            "label": "Full width"
          }
        }
      },
      "blocks": {
        "title": {
          "name": "Heading"
        },
        "content": {
          "name": "Content",
          "settings": {
            "page": {
              "label": "Content"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "button": {
          "name": "Button"
        },
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        }
      },
      "presets": {
        "name": "Rich text"
      }
    },
    "icon_text_blocks": {
      "name": "Multicolumn",
      "settings": {
        "items_width": {
          "label": "Columns per row"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "mobile": {
          "layout_mobile": {
            "label": "Layout",
            "options__1": {
              "label": "Slider"
            },
            "options__2": {
              "label": "In rows"
            }
          }
        },
        "blocks": {
          "header": "Columns",
          "blocks_text_alignment": {
            "label": "Alignment columns",
            "info": "Customize this section using blocks",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Center"
            }
          },
          "blocks_title_size": {
            "label": "Heading size columns"
          }
        }
      },
      "blocks": {
        "text": {
          "name": "Column",
          "settings": {
            "icon": {
              "label": "Icon",
              "info": "Icon uses highlight color for accents.",
              "options__1": {
                "label": "No icon"
              },
              "options__2": {
                "label": "Group"
              },
              "options__3": {
                "label": "Notification"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Verified"
              },
              "options__6": {
                "label": "Truck"
              },
              "options__7": {
                "label": "Image"
              },
              "options__8": {
                "label": "Phone call"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Shopping bag"
              },
              "options__11": {
                "label": "Global shipping"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Box"
              },
              "options__14": {
                "label": "Delivery box"
              },
              "options__15": {
                "label": "Statistic"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "Email"
              },
              "options__18": {
                "label": "Coin"
              },
              "options__19": {
                "label": "24 hour clock"
              },
              "options__20": {
                "label": "Question"
              },
              "options__21": {
                "label": "24/7 on call"
              },
              "options__22": {
                "label": "Speech bubbles"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobile payment"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Secure"
              }
            },
            "image": {
              "label": "Custom image",
              "info": "110 x 110px .png recommended"
            },
            "image_svg": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "image_height": {
              "label": "Image height"
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Text"
            },
            "link_color": {
              "label": "Text link color",
              "options__1": {
                "label": "Text"
              },
              "options__2": {
                "label": "Primary button"
              }
            }
          }
        }
      },
      "presets": {
        "name": "Multicolumn"
      }
    },
    "testimonials": {
      "name": "Testimonials",
      "settings": {
        "layout": {
          "label": "Testimonials layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Rows"
          }
        },
        "items_width": {
          "label": "Testimonials per row"
        },
        "autoplay": {
          "label": "Enable autoplay"
        },
        "autoplay_seconds": {
          "label": "Seconds between slides"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "testimonials": {
          "header": "Testimonial"
        },
        "testimonials_boxed_overlay_opacity": {
          "label": "Background opacity"
        },
        "color_scheme_score": {
          "label": "Color score"
        }
      },
      "blocks": {
        "testimonial": {
          "name": "Testimonial",
          "settings": {
            "text": {
              "label": "Text"
            },
            "score": {
              "label": "Score"
            },
            "author": {
              "label": "Author",
              "info": "Leave blank when the author is anonymous."
            },
            "place": {
              "label": "Title",
              "info": "Function, place, country, role, date."
            }
          }
        }
      },
      "presets": {
        "name": "Testimonials"
      }
    },
    "featured_collection": {
      "name": "Featured collection",
      "settings": {
        "collection": {
          "label": "Collection"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Grid"
          }
        },
        "limit": {
          "label": "Maximum products to show"
        },
        "number_of_items": {
          "label": "Products per row",
          "info": "If list includes more products than shown, it will be a slider automatically. Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208069-section-featured-collection)"
        },
        "text_alignment": {
          "label": "Heading position",
          "info": "If ‘Next to the products’ is selected, it shows a max of 3 products per row.",
          "options__1": {
            "label": "Next to the products"
          },
          "options__2": {
            "label": "Left"
          },
          "options__3": {
            "label": "Center"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "presets": {
        "name": "Featured collection"
      }
    },
    "featured_products": {
      "name": "Featured products",
      "settings": {
        "products": {
          "label": "Products"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Grid"
          }
        },
        "number_of_items": {
          "label": "Products per row",
          "info": "If list includes more products than shown, it will be a slider automatically. Swipe is automatically activated on mobile. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208069-section-featured-collection)"
        },
        "text_alignment": {
          "label": "Heading position",
          "info": "If ‘Next to the products’ is selected, it shows a max of 3 products per row.",
          "options__1": {
            "label": "Next to the products"
          },
          "options__2": {
            "label": "Left"
          },
          "options__3": {
            "label": "Center"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "presets": {
        "name": "Featured products"
      }
    },
    "featured_product": {
      "name": "Featured product",
      "settings": {
        "product": {
          "label": "Product"
        }
      },
      "presets": {
        "name": "Featured product"
      }
    },
    "usp_bar": {
      "name": "Information bar",
      "settings": {
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Rows"
          }
        },
        "autoplay": {
          "label": "Enable autoplay"
        },
        "autoplay_seconds": {
          "label": "Seconds between slides"
        },
        "show_arrows": {
          "label": "Show arrows"
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "blocks": {
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Heading"
            }
          }
        }
      },
      "presets": {
        "name": "Information bar"
      }
    },
    "events_calendar": {
      "name": "Events calendar",
      "settings": {
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        }
      },
      "blocks": {
        "event": {
          "name": "Event",
          "settings": {
            "image": {
              "label": "Image"
            },
            "date_color_palette": {
              "label": "Date label color"
            },
            "date": {
              "label": "Date"
            },
            "time": {
              "label": "Time"
            },
            "location": {
              "label": "Location"
            },
            "location_url": {
              "label": "Location link"
            },
            "mobile": {
              "header": "Mobile layout"
            }
          }
        }
      },
      "presets": {
        "name": "Events calendar"
      }
    },
    "faq": {
      "name": "Collapsible content",
      "settings": {
        "alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "compact": {
          "label": "Compact view"
        }
      },
      "blocks": {
        "text": {
          "name": "Collapsible row",
          "settings": {
            "faq_question": {
              "label": "Heading",
              "info": "Include a heading that explains the content."
            },
            "icon": {
              "label": "Icon",
              "info": "Icon uses highlight color for accents.",
              "options__1": {
                "label": "No icon"
              },
              "options__2": {
                "label": "Group"
              },
              "options__3": {
                "label": "Notification"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Verified"
              },
              "options__6": {
                "label": "Truck"
              },
              "options__7": {
                "label": "Image"
              },
              "options__8": {
                "label": "Phone call"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Shopping bag"
              },
              "options__11": {
                "label": "Global shipping"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Box"
              },
              "options__14": {
                "label": "Delivery box"
              },
              "options__15": {
                "label": "Statistic"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "Email"
              },
              "options__18": {
                "label": "Coin"
              },
              "options__19": {
                "label": "24 hour clock"
              },
              "options__20": {
                "label": "Question"
              },
              "options__21": {
                "label": "24/7 on call"
              },
              "options__22": {
                "label": "Speech bubbles"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobile payment"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Secure"
              }
            },
            "header_image": {
              "label": "Custom image",
              "info": "50 x 50px .png recommended"
            },
            "header_image_svg": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Max. image width"
            },
            "content": {
              "header": "Content",
              "paragraph": "All content can be filled static or with metafields. All content can be used seperate or together in this block. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)",
              "faq_answer": {
                "label": "Text"
              },
              "page": {
                "label": "Page content"
              },
              "image": {
                "label": "Image"
              },
              "image_width": {
                "label": "Image width"
              }
            },
            "custom_code": {
              "header": "Custom code",
              "liquid": {
                "label": "Liquid"
              },
              "html": {
                "label": "HTML"
              }
            },
            "form": {
              "header": "Contact form",
              "show_contact_form": {
                "label": "Show contact form",
                "info": "A standard contact form will appear below the content."
              }
            }
          }
        }
      },
      "presets": {
        "name": "Collapsible content"
      }
    },
    "custom_liquid": {
      "name": "Custom Liquid",
      "settings": {
        "custom_liquid": {
          "label": "Custom Liquid",
          "info": "Add app snippets or other Liquid code to create advanced customizations."
        }
      },
      "presets": {
        "name": "Custom Liquid"
      }
    },
    "custom_html_snippet": {
      "name": "Custom HTML",
      "settings": {
        "custom_html_snippet": {
          "label": "Custom HTML",
          "info": "Add app snippets, embed code or other HTML code to create advanced customizations."
        }
      },
      "presets": {
        "name": "Custom HTML"
      }
    },
    "spacer": {
      "name": "Spacing",
      "settings": {
        "height": {
          "label": "Height"
        },
        "mobile": {
          "header": "Mobile layout",
          "height_mobile": {
            "label": "Height"
          }
        }
      },
      "presets": {
        "name": "Spacing"
      }
    },
    "shop_the_look": {
      "name": "Shop the look",
      "settings": {
        "sticky": {
          "label": "Enable sticky scroll"
        },
        "image": {
          "label": "Image"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "layout": {
          "label": "Layout",
          "options__1": {
            "label": "Image left, products right"
          },
          "options__2": {
            "label": "Products left, image right"
          }
        },
        "banner_width": {
          "label": "Image width",
          "options__1": {
            "label": "1/2"
          },
          "options__2": {
            "label": "2/3"
          },
          "options__3": {
            "label": "3/4"
          }
        },
        "products": {
          "label": "Products"
        },
        "products_per_row": {
          "label": "Products per row",
          "options__1": {
            "label": "1"
          },
          "options__2": {
            "label": "2"
          }
        },
        "mobile": {
          "header": "Mobile layout"
        }
      },
      "presets": {
        "name": "Shop the look"
      }
    },
    "image_with_banners": {
      "name": "Image with banners",
      "settings": {
        "design": {
          "label": "Layout",
          "options__1": {
            "label": "Seperator"
          },
          "options__2": {
            "label": "Background"
          }
        },
        "image": {
          "label": "Image"
        },
        "overlay_opacity_img": {
          "label": "Image overlay opacity"
        },
        "color_palette": {
          "label": "Image color scheme"
        },
        "color_palette_banners": {
          "label": "Banners color scheme",
          "info": "Only applicable for the first banner and for banners with a background."
        },
        "content": {
          "header": "Content",
          "enable_content": {
            "label": "Show content on image",
            "info": "This will override the first banner."
          },
          "text_position": {
            "label": "Content position",
            "options__1": {
              "label": "Top left"
            },
            "options__2": {
              "label": "Top center"
            },
            "options__3": {
              "label": "Top right"
            },
            "options__4": {
              "label": "Center left"
            },
            "options__5": {
              "label": "Center center"
            },
            "options__6": {
              "label": "Center right"
            },
            "options__7": {
              "label": "Bottom left"
            },
            "options__8": {
              "label": "Bottom center"
            },
            "options__9": {
              "label": "Bottom right"
            }
          }
        },
        "layout_mobile": {
          "header": "Mobile layout",
          "image_mobile": {
            "label": "Mobile image (optional)"
          },
          "mobile_height": {
            "info": "Only applicable when the first banner is not visible."
          },
          "label": "Layout",
          "options__1": {
            "label": "Slider"
          },
          "options__2": {
            "label": "Grid"
          }
        }
      },
      "blocks": {
        "image": {
          "name": "Image banner",
          "show_banner": {
            "label": "Show banner"
          },
          "product": {
            "label": "Product",
            "info": "Select a product or the content can be filled below."
          },
          "height": {
            "label": "Aspect ratio"
          }
        }
      },
      "presets": {
        "name": "Image with banners"
      }
    }
  },
  "static_sections": {
    "announcement_bar": {
      "name": "Announcement bar",
      "settings": {
        "paragraph": "The announcement bar will remain visible in the theme editor after clicking the 'close' icon. [Read more](https://intercom.help/someoneyouknow/en/articles/6208055-header-settings)",
        "color_palette": {
          "label": "Color scheme",
          "options__1": {
            "label": "Lightest"
          },
          "options__2": {
            "label": "Darkest"
          },
          "options__3": {
            "label": "Light"
          },
          "options__4": {
            "label": "Dark"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent light"
          },
          "options__7": {
            "label": "Gradient light"
          },
          "options__8": {
            "label": "Gradient dark"
          }
        },
        "text": {
          "label": "Text"
        }
      }
    },
    "header": {
      "name": "Header",
      "settings": {
        "layout": {
          "label": "Layout",
          "info": "Layout 7 and 8 do not show the menu button, more information on header layouts can be found [here](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings).",
          "options__1": {
            "label": "Logo left, menubar, extended search",
            "group": "Logo left"
          },
          "options__2": {
            "label": "Logo left, menubar, variant extended search",
            "group": "Logo left"
          },
          "options__3": {
            "label": "Logo left, menubar, compact search right",
            "group": "Logo left"
          },
          "options__4": {
            "label": "Logo left, menubar, extended search right",
            "group": "Logo left"
          },
          "options__5": {
            "label": "Logo left, in-header navigation, compact search",
            "group": "Logo left"
          },
          "options__6": {
            "label": "Logo center, menubar, compact search",
            "group": "Logo center"
          },
          "options__7": {
            "label": "Logo center, menubar, extended search",
            "group": "Logo center"
          },
          "options__8": {
            "label": "Logo center, in-header navigation, compact search",
            "group": "Logo center"
          }
        },
        "width": {
          "label": "Width",
          "info": "When you choose 2000px, the header will be full width."
        },
        "header_height": {
          "label": "Minimal height",
          "options__1": {
            "label": "Small"
          },
          "options__2": {
            "label": "Medium"
          },
          "options__3": {
            "label": "Large"
          }
        },
        "show_header_border": {
          "label": "Show header border",
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Bottom"
          },
          "options__3": {
            "label": "Top and bottom"
          }
        },
        "header_border_opacity": {
          "label": "Header border opacity"
        },
        "header_border_width": {
          "label": "Header borders width"
        },
        "header_shadow": {
          "label": "Show shadow"
        },
        "header_shadow_opacity": {
          "label": "Header shadow opacity"
        },
        "header_icons": {
          "label": "Show text instead of icons",
          "info": "The account icon, cart icon, compact search icon, currency selector and language selector (if used) will be replaced with text."
        },
        "colors": {
          "header": "Colors",
          "top_bar_color": {
            "label": "Top bar color scheme"
          },
          "header_color": {
            "label": "Main bar color scheme"
          },
          "menubar_color": {
            "label": "Menu bar color scheme"
          }
        },
        "top_bar": {
          "header": "Top bar",
          "show_top_bar": {
            "label": "Enable top bar"
          },
          "top_bar_height": {
            "label": "Top bar height",
            "options__1": {
              "label": "Extra small"
            },
            "options__2": {
              "label": "Small"
            },
            "options__3": {
              "label": "Medium"
            },
            "options__4": {
              "label": "Large"
            }
          },
          "top_bar_font": {
            "label": "Top bar font"
          },
          "top_bar_font_size": {
            "label": "Top bar font size",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            },
            "options__4": {
              "label": "Extra large"
            }
          },
          "enable_country_selector": {
            "label": "Show country selector"
          },
          "enable_language_selector": {
            "label": "Show language selector"
          },
          "navbar_menu_item_text": {
            "label": "Extra menu item in top bar"
          },
          "navbar_menu_item_link": {
            "label": "Link"
          }
        },
        "information": {
          "header": "Top bar information",
          "enable_usp_slider": {
            "label": "Enable slider"
          },
          "enable_usp_slider_arrows": {
            "label": "Show slider arrows"
          },
          "enable_usp_slider_autoplay": {
            "label": "Enable autoplay"
          },
          "usp_slider_autoplay_seconds": {
            "label": "Seconds between slides"
          },
          "paragraph": "Add information using blocks in the sections area."
        },
        "enable_country_selector": {
          "label": "Show country selector"
        },
        "enable_language_selector": {
          "label": "Show language selector"
        },
        "searchbar": {
          "header": "Search bar",
          "searchbar_font_size": {
            "label": "Search bar font size",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            },
            "options__4": {
              "label": "Extra large"
            }
          },
          "searchbar_height": {
            "label": "Search bar height"
          },
          "searchbar_width": {
            "label": "Search bar width",
            "info": "Not applicable for a centered search bar or compact search icon. When you choose 600px, the search bar will be full width."
          },
          "show_searchbar_border": {
            "label": "Show search bar border"
          }
        },
        "sticky_header": {
          "header": "Sticky header",
          "enable_sticky_header": {
            "label": "Enable sticky header"
          },
          "sticky": {
            "label": "Sticky header or menu",
            "info": "Sticky menu is only applicable when a layout with menubar is enabled and 'Enable scroll effect' is disabled.",
            "options__1": {
              "label": "Sticky menu"
            },
            "options__2": {
              "label": "Sticky header"
            }
          }
        },
        "cart_icon": {
          "header": "Cart icon",
          "cart_icon": {
            "label": "Cart icon",
            "options__1": {
              "label": "Basket"
            },
            "options__2": {
              "label": "Bag"
            },
            "options__3": {
              "label": "Cart"
            }
          },
          "cart_icon_color": {
            "label": "Cart items indicator color scheme"
          },
          "show_cart_total": {
            "label": "Show total price"
          }
        },
        "navigation_menu": {
          "header": "Navigation menu",
          "menubar_menu": {
            "label": "Menu"
          },
          "menubar_height": {
            "label": "Menubar height",
            "options__1": {
              "label": "Small"
            },
            "options__2": {
              "label": "Medium"
            },
            "options__3": {
              "label": "Large"
            }
          },
          "menu_layout": {
            "label": "Menu type",
            "options__1": {
              "label": "Mega menu"
            },
            "options__2": {
              "label": "Dropdown menu"
            }
          },
          "menu_scroll": {
            "label": "Enable scroll effect",
            "info": "Only applicable when 'Alignment menu items' is set to 'Left'."
          },
          "menu_compact": {
            "label": "Decrease space between menu-items"
          },
          "show_dropdown_images": {
            "label": "Show collection images in menus",
            "info": "Show the collection image before the title."
          },
          "show_dropdown_images_subs": {
            "label": "Show collection images in submenus"
          },
          "show_dropdown_images_autofill": {
            "label": "Auto-fill collection images",
            "info": "Automatically use the first product images when missing the collection image."
          },
          "show_dropdown_images_rounded": {
            "label": "Show collection images in a circle"
          },
          "menubar_alignment": {
            "label": "Alignment menu items",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Centered"
            },
            "options__3": {
              "label": "Right"
            },
            "options__4": {
              "label": "Divide evenly"
            }
          },
          "main_menu_items_clickable": {
            "label": "Main menu items clickable"
          },
          "menu_show_chevrons": {
            "label": "Enable arrows for submenu items"
          },
          "sale_highlight": {
            "label": "Highlight sale menu item"
          },
          "sale_highlight_item": {
            "label": "Sale menu item"
          }
        },
        "megamenu": {
          "header": "Megamenu",
          "custom_menu_items_per_columns": {
            "label": "Custom menu items per column",
            "info": "By default, the items in the megamenu will be divided into columns based but this can be overruled by setting a custom number of items per column below."
          },
          "menu_items_limit": {
            "label": "Menu items per column",
            "info": "Note that submenu items will not be divided into separate columns."
          },
          "submenu_items_limit": {
            "label": "Submenu items per column",
            "info": "The last submenu items will be followed by a 'show more' link in the megamenu"
          },
          "main_menu_show_lines": {
            "label": "Show column border lines"
          }
        },
        "menu_typography": {
          "header": "Menu items",
          "menu_font": {
            "label": "Font"
          },
          "menu_font_weight": {
            "label": "Font weight"
          },
          "menu_font_size": {
            "label": "Font size"
          },
          "menu_letter_spacing": {
            "label": "Letter spacing"
          },
          "menubar_caps": {
            "label": "Menu items ALL CAPS"
          }
        },
        "menu_subs_typography": {
          "header": "Submenu items",
          "enable_menu_subs_font_size": {
            "label": "Enable custom font size"
          }
        },
        "socials": {
          "header": "Socials",
          "enable_socials": {
            "label": "Show social media accounts",
            "info": "For more info on how to connect your social media accounts click [here](https://intercom.help/someoneyouknow/en/articles/6208055-header-settings)."
          }
        },
        "trustmark": {
          "header": "Trustmark",
          "extra_image": {
            "label": "Image",
            "info": "Max. 150 x 90px .png or .svg recommended."
          },
          "extra_image_width": {
            "label": "Max. image width"
          },
          "extra_image_link": {
            "label": "Trustmark link"
          }
        },
        "online_store": {
          "header": "Online store",
          "disable_online_store": {
            "label": "Disable cart and account"
          },
          "disable_account_dropdown": {
            "label": "Disable account dropdown"
          }
        },
        "button": {
          "enable_button": {
            "info": "By default the button will link to the login page and will overrule the account icon, but this behaviour can be overruled by adding a custom link to the button."
          },
          "button_url": {
            "label": "Link (optional)",
            "info": "This will overrule the button link to the login page."
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "layout_mobile": {
            "label": "Header layout",
            "options__1": {
              "label": "Logo left"
            },
            "options__2": {
              "label": "Logo centered"
            },
            "options__3": {
              "label": "Logo right"
            }
          },
          "mobile_menu_color": {
            "label": "Menu color scheme"
          },
          "menu_icon": {
            "label": "Menu icon",
            "options__1": {
              "label": "Primary"
            },
            "options__2": {
              "label": "Secondary"
            },
            "options__3": {
              "label": "Tertiary"
            }
          },
          "search_compact_mobile": {
            "label": "Enable compact search icon"
          },
          "search_mobile_bd": {
            "label": "Show searchbar border",
            "options__1": {
              "label": "None"
            },
            "options__2": {
              "label": "Top"
            },
            "options__3": {
              "label": "Bottom"
            },
            "options__4": {
              "label": "Top and bottom"
            }
          },
          "searchbar_height_mobile": {
            "label": "Search bar height"
          },
          "spacing_mobile": {
            "info": "Only applicable for non-transparent header and when the first section is not a full width section or breadcrumbs."
          }
        }
      },
      "blocks": {
        "liquid": {
          "name": "Custom Liquid",
          "settings": {
            "liquid": {
              "label": "Custom Liquid",
              "info": "Add app snippets or other Liquid code to create advanced customizations."
            },
            "location": {
              "label": "Location",
              "options__1": {
                "label": "Top bar"
              },
              "options__2": {
                "label": "Main bar"
              }
            },
            "show_mobile": {
              "label": "Show on mobile"
            }
          }
        },
        "usp": {
          "name": "Top bar information",
          "settings": {
            "usp": {
              "label": "Heading",
              "info": "When the line of text is longer than a mobile screen width can handle, it will show a ‘more info’ link that opens a popup. Wrap your text between | to use accent color. For example: Heading |continued| text."
            },
            "show_check": {
              "label": "Show check icon"
            },
            "image": {
              "label": "Custom image"
            },
            "image_width": {
              "label": "Image width"
            }
          }
        },
        "button_menu": {
          "name": "Menu button",
          "settings": {
            "show_mobile": {
              "label": "Use this menu in mobile navigation"
            },
            "button_menu": {
              "label": "Menu"
            },
            "button_color_palette": {
              "label": "Button color scheme",
              "options__7": {
                "label": "Positive"
              },
              "options__8": {
                "label": "Buy button"
              },
              "options__9": {
                "label": "Dynamic buy button"
              }
            },
            "button_style": {
              "label": "Button styling",
              "options__1": {
                "label": "Fill"
              },
              "options__2": {
                "label": "No fill"
              }
            }
          }
        },
        "promo_banners": {
          "name": "Menu subs with promo banner",
          "settings": {
            "menu_item": {
              "label": "Menu item",
              "info": "Copy and paste the name of an item created in your menu to create a dropdown menu. [Read more](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings#h_4fb96ad5c6)"
            },
            "text_alignment": {
              "label": "Heading position",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              }
            },
            "layout": {
              "label": "Banner layout",
              "info": "Only applicable when both banners are enabled.",
              "options__1": {
                "label": "Columns"
              },
              "options__2": {
                "label": "Rows"
              }
            },
            "banner_1": {
              "header": "First banner",
              "enable_banner_1": {
                "label": "Enable first banner"
              }
            },
            "banner_2": {
              "header": "Second banner",
              "enable_banner_2": {
                "label": "Enable second banner"
              }
            }
          }
        },
        "promo_collection_list": {
          "name": "Menu promo collections",
          "settings": {
            "menu_item": {
              "label": "Menu item",
              "info": "Copy and paste the name of an item created in your menu to create a dropdown menu. [Read more](https://intercom.help/someoneyouknow/en/articles/8789865-header-settings#h_4fb96ad5c6)"
            }
          }
        }
      }
    },
    "footer": {
      "name": "Footer",
      "settings": {
        "footer_color": {
          "label": "Main color scheme",
          "options__1": {
            "label": "Lightest"
          },
          "options__2": {
            "label": "Darkest"
          },
          "options__3": {
            "label": "Light"
          },
          "options__4": {
            "label": "Dark"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent light"
          },
          "options__7": {
            "label": "Gradient light"
          },
          "options__8": {
            "label": "Gradient dark"
          }
        },
        "bottom_bar": {
          "header": "Bottom bar",
          "bottom_color": {
            "label": "Color scheme",
            "options__1": {
              "label": "Lightest"
            },
            "options__2": {
              "label": "Darkest"
            },
            "options__3": {
              "label": "Light"
            },
            "options__4": {
              "label": "Accent light"
            },
            "options__5": {
              "label": "Accent"
            },
            "options__6": {
              "label": "Dark"
            },
            "options__7": {
              "label": "Gradient light"
            },
            "options__8": {
              "label": "Gradient dark"
            }
          },
          "bottom_img": {
            "label": "Image",
            "info": "A logo or a trustmark is commonly used for the footer bottombar."
          },
          "bottom_img_width": {
            "label": "Image width"
          },
          "bottom_img_link": {
            "label": "Link"
          },
          "enable_country_selector": {
            "label": "Show country selector",
            "info": "Make sure 'show country selector' is enabled in the header settings."
          },
          "enable_language_selector": {
            "label": "Show language selector",
            "info": "Make sure 'show language selector' is enabled in the header settings."
          },
          "enable_follow_on_shop": {
            "label": "Enable Follow on Shop",
            "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
          }
        },
        "payment_methods": {
          "header": "Payment methods",
          "show_payment_methods": {
            "label": "Show payment methods"
          }
        }
      },
      "blocks": {
        "menu": {
          "name": "Menu",
          "settings": {
            "menu": {
              "label": "Menu"
            },
            "split_menu": {
              "label": "Split menu",
              "info": "This will show a large menu in two parts, the other blocks will always be shown first."
            }
          }
        },
        "text": {
          "name": "Text",
          "settings": {
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Text"
            }
          }
        },
        "html": {
          "name": "HTML",
          "settings": {
            "html": {
              "label": "HTML"
            }
          }
        },
        "socials_newsletter": {
          "name": "Socials & newsletter",
          "settings": {
            "title": {
              "label": "Heading"
            },
            "show_socials": {
              "label": "Show social media icons"
            },
            "enable_follow_on_shop": {
              "label": "Enable Follow on Shop",
              "info": "To allow customers to follow your store on the Shop app from your storefront, Shop Pay must be enabled. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/follow-on-shop)"
            },
            "newsletter": {
              "header": "Newsletter"
            },
            "show_newsletter": {
              "label": "Show newsletter"
            },
            "footer_button_style": {
              "label": "Button style"
            },
            "checkbox": {
              "header": "Checkbox",
              "enable_newsletter_terms_checkbox": {
                "label": "Show checkbox"
              },
              "newsletter_terms_text": {
                "label": "Label for the checkbox",
                "info": "For example, place a link to your privacy policy."
              }
            }
          }
        },
        "customer_support": {
          "name": "Customer service",
          "settings": {
            "title": {
              "label": "Heading"
            },
            "customer_support_img": {
              "label": "Image",
              "info": "For best results use an image with a max. width of 320px."
            },
            "customer_support_img_width": {
              "label": "Image width"
            },
            "customer_support_offset_right": {
              "label": "Image right offset"
            },
            "customer_support_offset_bottom": {
              "label": "Image bottom offset"
            },
            "extra_customer_support": {
              "label": "Text",
              "info": "Do you want to show any other info about your company? You can show the opening hours, for example."
            },
            "show_phone_link": {
              "label": "Show call button"
            },
            "show_mail_link": {
              "label": "Show mail button"
            },
            "show_whatsapp_link": {
              "label": "Show WhatsApp button"
            },
            "mobile": {
              "header": "Mobile layout",
              "customer_support_offset_right_mobile": {
                "label": "Image right offset"
              },
              "customer_support_offset_bottom_mobile": {
                "label": "Image bottom offset"
              }
            }
          }
        }
      }
    },
    "breadcrumbs": {
      "name": "Breadcrumbs",
      "settings": {
        "hide_mobile": {
          "label": "Hide on mobile"
        }
      }
    },
    "product_recommendations": {
      "name": "Product recommendations",
      "settings": {
        "paragraph": "Dynamic recommendations use order and product information to change and improve over time. [Learn more](https://help.shopify.com/en/themes/development/recommended-products#recommendation-logic)",
        "recommended_products_qty": {
          "label": "Maximum products to show"
        },
        "number_of_items": {
          "label": "Products per row"
        },
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "mobile_collapse": {
            "label": "Collapse by default"
          }
        }
      }
    },
    "product_reviews": {
      "name": "Product reviews",
      "settings": {
        "text_alignment": {
          "label": "Heading position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "mobile_collapse": {
            "label": "Collapse by default"
          }
        }
      }
    },
    "page_service_menu": {
      "name": "Menu",
      "settings": {
        "menu": {
          "label": "Menu",
          "info": "Create a menu containing all of your info pages. [Learn more](https://intercom.help/someoneyouknow/en/collections/3431583-theme-xtra-2-0-for-shopify-manuals)"
        }
      }
    },
    "page_service_info_blocks": {
      "name": "Information banner",
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "information": {
          "name": "Information banner",
          "settings": {
            "color_palette": {
              "label": "Color scheme",
              "options__1": {
                "label": "Lightest"
              },
              "options__2": {
                "label": "Darkest"
              },
              "options__3": {
                "label": "Light"
              },
              "options__4": {
                "label": "Accent light"
              },
              "options__5": {
                "label": "Accent"
              },
              "options__6": {
                "label": "Dark"
              },
              "options__7": {
                "label": "Gradient light"
              },
              "options__8": {
                "label": "Gradient dark"
              }
            },
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Text"
            },
            "show_login_links": {
              "label": "Show sign in and register links for customers"
            },
            "show_shop_address": {
              "label": "Show shop address"
            },
            "show_maps_url": {
              "label": "Show a link to Google Maps after the address"
            },
            "show_phone_link": {
              "label": "Show call button"
            },
            "show_mail_link": {
              "label": "Show mail button"
            },
            "show_whatsapp_link": {
              "label": "Show WhatsApp button"
            }
          }
        }
      }
    },
    "google_maps": {
      "name": "Google Maps",
      "settings": {
        "google_maps_iframe": {
          "label": "Google Maps embed code",
          "info": "Show your shops' location in Google Maps. You can find the embed code by searching your store in Google Maps, clicking 'Share' and then choosing 'Embed a map'. [Learn more](https://support.google.com/maps/answer/144361)"
        }
      }
    },
    "sticky_add_to_cart": {
      "name": "Sticky add to cart bar",
      "settings": {
        "enable": {
          "label": "Enable sticky add to cart bar",
          "info": "Not applicable for products with more than 250 variants."
        },
        "position": {
          "label": "Position",
          "options__1": {
            "label": "Top"
          },
          "options__2": {
            "label": "Bottom"
          }
        },
        "button_style": {
          "label": "Button style"
        },
        "show_variant_picker": {
          "label": "Show variant picker"
        },
        "show_unavailable_variants": {
          "label": "Show unavailable variants"
        },
        "show_amount_selection": {
          "label": "Show amount selection"
        },
        "show_tax": {
          "label": "Show 'incl.- or excl. tax' text"
        },
        "preorder": {
          "label": "Show pre-order button"
        },
        "hide_in_theme_editor": {
          "label": "Hide sticky add to cart bar in the theme editor"
        },
        "mobile": {
          "header": "Mobile layout",
          "show_mobile": {
            "label": "Show on mobile"
          }
        }
      }
    }
  },
  "main": {
    "product": {
      "name": "Product",
      "settings": {
        "thumbs": {
          "header": "Thumbnails",
          "show_thumbs_desktop": {
            "label": "Show on desktop"
          },
          "show_thumbs_mobile": {
            "label": "Show on mobile"
          },
          "images_layout": {
            "label": "Thumbnails layout",
            "options__1": {
              "label": "Vertical"
            },
            "options__2": {
              "label": "Horizontal"
            }
          }
        },
        "product_description": {
          "header": "Product description",
          "show_product_description": {
            "label": "Show product description"
          },
          "product_description_enable_read_more": {
            "label": "Enable 'read more' link"
          }
        },
        "pros_and_cons": {
          "header": "Pros and cons",
          "pros": {
            "label": "Pros"
          },
          "cons": {
            "label": "Cons"
          },
          "paragraph": "Pros and cons can be setup with metafields. More information on metafields and this feature can be found [here](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)."
        },
        "specifications": {
          "header": "Product specifications",
          "specifications": {
            "label": "Specifications"
          },
          "paragraph": "Product specifications can be setup with metafields. More information on metafields and this feature can be found [here](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)."
        },
        "mobile": {
          "header": "Mobile layout",
          "product_description_mobile_collapse": {
            "label": "Collapse product description by default"
          },
          "mobile_tabs_title_size": {
            "label": "Tabs heading size"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "custom_liquid": {
          "name": "Custom Liquid",
          "settings": {
            "custom_liquid": {
              "label": "Custom Liquid",
              "info": "Add app snippets or other Liquid code to create advanced customizations."
            }
          }
        },
        "custom_html_snippet": {
          "name": "Custom HTML",
          "settings": {
            "custom_html_snippet": {
              "label": "Custom HTML",
              "info": "Add app snippets, embed code or other HTML code to create advanced customizations."
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "layout": {
              "label": "Layout",
              "info": "Title is only applicable as a block when used ‘right’.",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Right"
              }
            },
            "title_size": {
              "label": "Heading size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              },
              "options__4": {
                "label": "Extra large"
              }
            },
            "title": {
              "label": "Custom title",
              "info": "Leave empty to show the product title."
            },
            "vendor": {
              "header": "Vendor",
              "show_vendor_brand": {
                "label": "Show 'Vendor' label"
              },
              "show_vendor_name": {
                "label": "Show vendor name"
              },
              "paragraph": "It will show like this when both selected: Vendor Vendor name"
            },
            "product_rating": {
              "header": "Product ratings",
              "show_product_rating": {
                "label": "Show product ratings",
                "info": "The ratings will be automatically shown from your review app!"
              }
            },
            "share_buttons": {
              "header": "Share buttons",
              "share_whatsapp": {
                "label": "Show WhatsApp icon"
              },
              "share_facebook": {
                "label": "Show Facebook icon"
              },
              "share_twitter": {
                "label": "Show X icon"
              },
              "share_pinterest": {
                "label": "Show Pinterest icon"
              },
              "share_messenger": {
                "label": "Show Messenger icon"
              },
              "share_email": {
                "label": "Show Email icon"
              }
            },
            "mobile": {
              "header": "Mobile layout",
              "layout_mobile": {
                "label": "Layout",
                "info": "This changes the position of the product title based on the main product image.",
                "options__1": {
                  "label": "Top"
                },
                "options__2": {
                  "label": "Bottom"
                }
              }
            }
          }
        },
        "quantity_rules": {
          "name": "Quantity rules",
          "settings": {
            "paragraph": "Quantity rules is only available for Shopify Plus merchants. [Learn more](https://help.shopify.com/en/manual/b2b/catalogs/quantity-pricing)",
            "paragraph-2": "A placeholder text will be shown in the theme editor. The text will be replaced by the actual quantity rules when the product is published."
          }
        },
        "volume_pricing": {
          "name": "Volume pricing",
          "settings": {
            "paragraph": "Volume pricing is only available for Shopify Plus merchants. [Learn more](https://help.shopify.com/en/manual/b2b/catalogs/quantity-pricing)",
            "paragraph-2": "A placeholder text will be shown in the theme editor. The text will be replaced by the actual volume pricing when the product is published."
          }
        },
        "short_description": {
          "name": "Short product description",
          "settings": {
            "show_read_more": {
              "label": "Show a 'read more' link which scrolls to the product description"
            },
            "paragraph": "Check out the general theme settings to set up a custom short description. You can find the settings under the tab named 'Products'.",
            "text": {
              "label": "Custom short description",
              "info": "Leave empty to show the default short description title."
            }
          }
        },
        "price": {
          "name": "Price",
          "settings": {
            "show_tax": {
              "label": "Show 'incl.- or excl. tax' text"
            }
          }
        },
        "codes": {
          "name": "Product codes",
          "settings": {
            "show_sku": {
              "label": "Show SKU"
            },
            "show_barcode": {
              "label": "Show barcode"
            }
          }
        },
        "inventory": {
          "name": "Inventory & delivery time",
          "settings": {
            "paragraph": "You can find the settings in the general theme settings under the tab named 'Products'."
          }
        },
        "upsell": {
          "name": "Bulk upsell",
          "settings": {
            "heading_products": "Products",
            "heading_colors": "Colors",
            "color_scheme": {
              "label": "Color scheme",
              "lightest": {
                "label": "Lightest"
              },
              "darkest": {
                "label": "Darkest"
              },
              "light": {
                "label": "Light"
              },
              "dark": {
                "label": "Dark"
              },
              "accent": {
                "label": "Accent"
              },
              "accent_light": {
                "label": "Accent light"
              },
              "gradient_light": {
                "label": "Gradient light"
              },
              "gradient_dark": {
                "label": "Gradient dark"
              },
              "primary_button": {
                "label": "Primary button"
              },
              "secondary_button": {
                "label": "Secondary button"
              },
              "buy_button": {
                "label": "Buy button"
              }
            },
            "button_color_scheme": {
              "label": "Button color scheme"
            },
            "variant_selector_color_scheme": {
              "label": "Variant selector color scheme"
            },
            "product_list": {
              "label": "Products"
            },
            "visible_products": {
                "label": "Show products"
            },
            "show_out_of_stock_products": {
              "label": "Show out of stock products"
            },
            "variant_in_popup": {
              "label": "Show variants in popup",
              "info": "This will open a ‘Choose variant’ popup, instead of a variant selector in the product card."
            },
            "header": {
              "label": "Content"
            },
            "heading_position": {
              "label": "Content position",
              "options__left": {
                  "label": "Left"
              },
              "options__center": {
                  "label": "Center"
              }
            },
            "heading_size": {
              "label": "Heading size",
              "options__small": {
                  "label": "Small"
              },
              "options__medium": {
                  "label": "Medium"
              },
              "options__large": {
                  "label": "Large"
              },
              "options__extra_large": {
                  "label": "Extra large"
              }
            },
            "heading": {
              "label": "Heading"
            }
          }
        },
        "urgency": {
          "name": "Stock urgency",
          "settings": {
              "color_palette": {
                "lightest": {
                  "label": "Lightest"
                },
                "darkest": {
                  "label": "Darkest"
                },
                "light": {
                  "label": "Light"
                },
                "dark": {
                  "label": "Dark"
                },
                "accent": {
                  "label": "Accent"
                },
                "accent_light": {
                  "label": "Accent light"
                },
                "gradient_light": {
                  "label": "Gradient light"
                },
                "gradient_dark": {
                  "label": "Gradient dark"
                },
                "primary_button": {
                  "label": "Primary button"
                },
                "secondary_button": {
                  "label": "Secondary button"
                },
                "buty_button": {
                  "label": "Buy button"
                }
            },
            "stock": {
              "header": "Stock",
              "product": {
                "label": "Product"
              },
              "min_stock": {
                "label": "Show urgency bar when the variant inventory is below",
                "info": "The stock urgency bar will only show if the stock level is below this amount. Or use metafields below."
              },
              "min_stock_meta": {
                "label": "Metafield stock",
                "info": "This will override ‘show urgency bar when the variant inventory is below’, above. Learn how to use this metafield [here](https://help.shopify.com/en/manual/metafields)."
              },
              "show_level": {
                "label": "Show variant's current stock amount",
                "info": "This will show the current stock amount in the urgency bar. Tip: you can also use the variable *inventory* in your text below. "
              },
              "show_bar_unavailable": {
                "label": "Show bar when variant stock is 0 or below",
                "info": "When stock is empty, you can choose to show an empty bar or hide it. When showing it, use the out of stock message below. "
              },
              "show_bar_available_nostock": {
                "label": "Show bar when variant is out of stock and still purchasable.",
                "info": "Show bar when the product variant is 0 or below 0 and 'Continue selling when out of stock' checkbox is on in product settings in Shopify admin."
              }
            },
            "layout": {
              "header": "Layout",
              "layout": {
                "label": "Stock urgency layout",
                "small": {
                  "label": "Small bar"
                },
                "medium": {
                  "label": "Large bar"
                }
              },
              "position_stock_message": {
                "label": "Show stock message",
                "above": {
                  "label": "Above bar"
                },
                "below": {
                  "label": "Below bar"
                }
              }
            },
            "colors": {
              "header": "Colors",
              "enable_background": {
                "label": "Enable background"
              },
              "color_palette": {
                "label": "Color scheme"
              },
              "color_progressbar": {
                "label": "Stock urgency bar",
                "info": "Used for solid background color."
              },
              "gradient_progressbar": {
                "label": "Stock urgency bar gradient",
                "info": "Gradient overrules the solid background color."
              },
              "background_progress_bar": {
                "label": "Progress bar color",
                "info": "This is the background color of the progress bar."
              },
              "tooltip_background": {
                "label": "Tooltip background"
              },
              "tooltip_text_color": {
                "label": "Tooltip label text"
              },
              "show_background": {
                "label": "Show background color"
              }
            },
            "content": {
              "header": "Content",
              "message": {
                "label": "Stock message text",
                "info": "Use the variable *inventory* to show your current stock level, like shown above in the default message."
              },
              "outofstock_message": {
                "label": "Out of stock message text"
              }
            }
          }
        },
        "variant_selection": {
          "name": "Variant picker",
          "settings": {
            "selection_type": {
              "label": "Picker type",
              "options__1": {
                "label": "Dropdown"
              },
              "options__2": {
                "label": "Buttons"
              }
            },
            "options": {
              "label": "Enable separate option pickers",
              "info": "[Combined listing](https://apps.shopify.com/combined-listings) products and products with more than 250 variants will always show separate option pickers."
            },
            "show_variant_images": {
              "label": "Show images in variants"
            },
            "show_unavailable_variants": {
              "label": "Show unavailable variants",
              "info": "Unavailable variants can only be hidden when separate option pickers are not applicable for the product."
            },
            "show_single_options": {
              "label": "Show options with only one variant"
            },
            "enable_selling_plans": {
              "label": "Enable subscriptions",
              "info": "For more information on how to set up subscriptions [click here](https://help.shopify.com/en/manual/products/purchase-options/subscriptions)."
            }
          }
        },
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Information"
            }
          }
        },
        "trustbadge": {
          "name": "Trustbadge",
          "settings": {
            "paragraph": "You can find the settings in the general theme settings under the tab named 'Trustbadge'."
          }
        },
        "shipping_timer": {
          "name": "Shipping timer",
          "settings": {
            "paragraph": "You can find the settings in the general theme settings under the tab named 'Shipping and delivery'."
          }
        },
        "content": {
          "name": "Content",
          "settings": {
            "enable_tab": {
              "label": "Show in accordion"
            },
            "collapse": {
              "label": "Collapse accordion by default"
            },
            "title_size": {
              "info": "Only applicable when 'Show in accordion' is disabled"
            },
            "title": {
              "label": "Heading"
            },
            "icon": {
              "label": "Icon",
              "info": "Icon uses highlight color for accents.",
              "options__1": {
                "label": "No icon"
              },
              "options__2": {
                "label": "Group"
              },
              "options__3": {
                "label": "Notification"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Verified"
              },
              "options__6": {
                "label": "Truck"
              },
              "options__7": {
                "label": "Image"
              },
              "options__8": {
                "label": "Phone call"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Shopping bag"
              },
              "options__11": {
                "label": "Global shipping"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Box"
              },
              "options__14": {
                "label": "Delivery box"
              },
              "options__15": {
                "label": "Statistic"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "Email"
              },
              "options__18": {
                "label": "Coin"
              },
              "options__19": {
                "label": "24 hour clock"
              },
              "options__20": {
                "label": "Question"
              },
              "options__21": {
                "label": "24/7 on call"
              },
              "options__22": {
                "label": "Speech bubbles"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobile payment"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Secure"
              }
            },
            "header_image": {
              "label": "Custom image",
              "info": "50 x 50px .png recommended"
            },
            "header_image_svg": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Max. image width"
            },
            "content": {
              "header": "Content",
              "paragraph": "All content can be filled static or with metafields. All content can be used seperate or together in this block. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208042-productpage)",
              "text": {
                "label": "Text"
              },
              "page": {
                "label": "Page content"
              },
              "image": {
                "label": "Image"
              },
              "image_width": {
                "label": "Image width"
              }
            },
            "custom_code": {
              "header": "Custom code",
              "liquid": {
                "label": "Liquid"
              },
              "html": {
                "label": "HTML"
              }
            },
            "form": {
              "header": "Contact form",
              "show_contact_form": {
                "label": "Show contact form",
                "info": "A standard contact form will appear below the content."
              }
            }
          }
        },
        "complementary_products": {
          "name": "Complementary products",
          "settings": {
            "paragraph": "This block only works with the Shopify Search & Discovery app. If no additional products are set, placeholder products will be shown (only in the theme editor). To select complementary products add the Search & Discovery app. [Learn more](https://help.shopify.com/en/manual/online-store/search-and-discovery/product-recommendations)",
            "enable_tab": {
              "label": "Show in accordion"
            },
            "collapse": {
              "label": "Collapse accordion by default"
            },
            "title": {
              "label": "Heading"
            },
            "icon": {
              "label": "Icon",
              "info": "Icon uses highlight color for accents.",
              "options__1": {
                "label": "No icon"
              },
              "options__2": {
                "label": "Group"
              },
              "options__3": {
                "label": "Notification"
              },
              "options__4": {
                "label": "Cloud data"
              },
              "options__5": {
                "label": "Verified"
              },
              "options__6": {
                "label": "Truck"
              },
              "options__7": {
                "label": "Image"
              },
              "options__8": {
                "label": "Phone call"
              },
              "options__9": {
                "label": "Filters"
              },
              "options__10": {
                "label": "Shopping bag"
              },
              "options__11": {
                "label": "Global shipping"
              },
              "options__12": {
                "label": "Barcode"
              },
              "options__13": {
                "label": "Box"
              },
              "options__14": {
                "label": "Delivery box"
              },
              "options__15": {
                "label": "Statistic"
              },
              "options__16": {
                "label": "Review"
              },
              "options__17": {
                "label": "Email"
              },
              "options__18": {
                "label": "Coin"
              },
              "options__19": {
                "label": "24 hour clock"
              },
              "options__20": {
                "label": "Question"
              },
              "options__21": {
                "label": "24/7 on call"
              },
              "options__22": {
                "label": "Speech bubbles"
              },
              "options__23": {
                "label": "Coupon"
              },
              "options__24": {
                "label": "Mobile payment"
              },
              "options__25": {
                "label": "Calculator"
              },
              "options__26": {
                "label": "Secure"
              }
            },
            "header_image": {
              "label": "Custom image",
              "info": "50 x 50px .png recommended"
            },
            "header_image_svg": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            },
            "header_image_width": {
              "label": "Max. image width"
            },
            "products": {
              "header": "Products",
              "layout": {
                "label": "Layout",
                "options__1": {
                  "label": "Grid / swipe"
                },
                "options__2": {
                  "label": "List"
                }
              },
              "number_of_items": {
                "label": "Products per row"
              }
            },
            "quick_buy": {
              "header": "Quick shop",
              "paragraph": "This will show an ‘add to cart’ button on your product card.",
              "enable_quick_buy_desktop": {
                "label": "Show on desktop"
              },
              "enable_quick_buy_mobile": {
                "label": "Show on mobile"
              },
              "enable_quick_buy_drawer": {
                "label": "Enable quick shop drawer",
                "info": "This will open a compact version of the productpage in a side drawer."
              },
              "enable_quick_buy_qty_selector": {
                "label": "Show amount selector",
                "info": "Not applicable when quick shop drawer is enabled."
              },
              "enable_color_picker": {
                "label": "Show color picker"
              },
              "enable_quick_buy_compact": {
                "label": "Enable compact button",
                "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
              }
            },
            "products_layout": {
              "header": "Layout products",
              "paragraph": "If used, show the following information on the productcards.",
              "products_show_image": {
                "label": "Show image"
              },
              "products_show_labels": {
                "label": "Show labels"
              },
              "products_show_vendor": {
                "label": "Show vendor"
              },
              "products_show_title": {
                "label": "Show title"
              },
              "products_show_rating": {
                "label": "Show rating"
              },
              "products_show_price": {
                "label": "Show price"
              },
              "products_show_stock": {
                "label": "Show stock"
              }
            }
          }
        },
        "buy_button": {
          "name": "Buy button",
          "settings": {
            "button_style": {
              "label": "Styling",
              "options__1": {
                "label": "Fill"
              },
              "options__2": {
                "label": "No fill"
              }
            },
            "show_amount_selection": {
              "label": "Show amount selection"
            },
            "show_dynamic_buybutton": {
              "label": "Show dynamic buy button",
              "info": "Customers will see their preferred payment method, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/en/manual/online-store/themes/dynamic-checkout)"
            },
            "button_style_dynamic_buybutton": {
              "label": "Dynamic buy button styling",
              "info": "Not applicable for all dynamic buy buttons."
            },
            "preorder": {
              "header": "Pre-order button",
              "preorder": {
                "label": "Show pre-order button",
                "info": "This shows a pre-order button as buy button. Only applicable when product has 'continue selling when out of stock' setting active."
              },
              "preorder_button_text": {
                "label": "Button label"
              },
              "preorder_label": {
                "label": "Delivery label",
                "info": "Add a delivery message to the pre-order button with metafields or general text content. If you want to show this information in your order mails, click [here](https://community.shopify.com/c/shopify-design/product-pages-get-customization-information-for-products/td-p/616503) for documentation."
              },
              "preorder_label_color_palette": {
                "label": "Delivery label color scheme",
                "options__1": {
                  "label": "Lightest"
                },
                "options__2": {
                  "label": "Darkest"
                },
                "options__3": {
                  "label": "Light"
                },
                "options__4": {
                  "label": "Dark"
                },
                "options__5": {
                  "label": "Accent"
                },
                "options__6": {
                  "label": "Accent light"
                },
                "options__7": {
                  "label": "Regular button"
                },
                "options__8": {
                  "label": "Buy button"
                },
                "options__9": {
                  "label": "Dynamic buy button"
                }
              },
              "preorder_show_dynamic_buybutton": {
                "label": "Show dynamic buy button for pre-orders"
              }
            },
            "gift_card": {
              "header": "Gift card",
              "show_gift_card_recipient": {
                "label": "Show recipient information form for gift card products",
                "info": "Gift card products can optionally be sent direct to a recipient on a scheduled date along with a personal message. [Learn more](https:\/\/help.shopify.com\/manual\/online-store\/themes\/customizing-themes\/add-gift-card-recipient-fields)"
              }
            }
          }
        }
      }
    },
    "collection": {
      "name": "Collection",
      "settings": {
        "layout": {
          "header": "Layout",
          "pagination_qty": {
            "label": "Products per page"
          },
          "pagination_type": {
            "label": "Pagination",
            "options__1": {
              "label": "Pages"
            },
            "options__2": {
              "label": "'Show more' button"
            }
          },
          "show_amount_of_products_in_collection": {
            "label": "Show total amount of products"
          },
          "productview_type": {
            "label": "Product cards layout",
            "options__1": {
              "label": "Grid"
            },
            "options__2": {
              "label": "List"
            }
          },
          "products_per_row": {
            "label": "Products per row",
            "info": "Only applicable for the product card layout 'Grid'."
          }
        },
        "show_collection_image": {
          "label": "Show collection image"
        },
        "filters": {
          "header": "Filter and menu alignment",
          "enable_filters": {
            "label": "Show filters"
          },
          "enable_menu": {
            "label": "Show menu"
          },
          "enable_collections": {
            "label": "Show collections"
          },
          "filters_menu_alignment": {
            "label": "Alignment",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Right"
            }
          },
          "filter_menu": {
            "label": "Menu",
            "info": "A menu which will be used to display related collections. Click [here](https://intercom.help/someoneyouknow/en/articles/6208044-collections) for more info on how to build this menu."
          },
          "collections_menu": {
            "label": "Collections",
            "info": "Select related collections and/or choose a menu above. Duplicate items will be hidden automatically."
          },
          "filter_sticky": {
            "label": "Enable sticky filters"
          },
          "filters_show_more_limit": {
            "label": "Enable 'Show more' link after amount of filters"
          }
        },
        "filter_swatches": {
          "header": "Visual filters",
          "paragraph": "For more information on how to set up visual filters [click here](https://help.shopify.com/en/manual/online-store/search-and-discovery/filters#visual-filters).",
          "image_swatches_size": {
            "label": "Image size",
            "options__1": {
              "label": "Extra small"
            },
            "options__2": {
              "label": "Small"
            },
            "options__3": {
              "label": "Medium"
            },
            "options__4": {
              "label": "Large"
            }
          },
          "image_swatches_show_in_circle": {
            "label": "Show images in circle"
          },
          "image_swatches_fill_images": {
            "label": "Fill images"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "products_per_row_mobile": {
            "label": "Products per row",
            "info": "Only applicable for the product card layout 'Grid'.",
            "options__1": {
              "label": "1"
            },
            "options__2": {
              "label": "2"
            }
          },
          "filter_mobile_sticky": {
            "label": "Enable sticky filter button"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "text_alignment": {
              "label": "Content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              }
            },
            "title": {
              "label": "Custom title",
              "info": "Leave empty to show the collection title."
            }
          }
        },
        "description": {
          "name": "Description",
          "settings": {
            "text_alignment": {
              "label": "Content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              }
            },
            "enable_read_more": {
              "label": "Enable 'read more' link"
            },
            "text": {
              "label": "Custom description",
              "info": "Leave empty to show the collection description."
            }
          }
        },
        "product_grid": {
          "name": "Products"
        },
        "sorting": {
          "name": "Sorting options",
          "settings": {
            "show_sorting_bar": {
              "label": "Show sorting bar",
              "info": "Display the sorting dropdown and product count"
            }
          }
        }
      }
    },
    "search": {
      "name": "Search results",
      "settings": {
        "layout": {
          "header": "Layout",
          "pagination_qty": {
            "label": "Search results per page"
          },
          "pagination_type": {
            "label": "Pagination",
            "options__1": {
              "label": "Pages"
            },
            "options__2": {
              "label": "'Show more' button"
            }
          },
          "show_amount_of_search_results": {
            "label": "Show amount of search results"
          },
          "productview_type": {
            "label": "Product cards layout",
            "options__1": {
              "label": "Grid"
            },
            "options__2": {
              "label": "List"
            }
          },
          "products_per_row": {
            "label": "Products per row",
            "info": "Only applicable for the product card layout 'Grid'."
          }
        },
        "filters": {
          "header": "Filter and menu alignment",
          "enable_filters": {
            "label": "Show filters"
          },
          "enable_menu": {
            "label": "Show menu"
          },
          "enable_collections": {
            "label": "Show collections"
          },
          "filters_menu_alignment": {
            "label": "Alignment",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Right"
            }
          },
          "filter_menu": {
            "label": "Menu",
            "info": "A menu which will be used to display collections. Click [here](https://intercom.help/someoneyouknow/en/articles/6208044-collections) for more info on how to build this menu."
          },
          "collections_menu": {
            "label": "Collections",
            "info": "Select related collections and/or choose a menu above. Duplicate items will be hidden automatically."
          },
          "filter_sticky": {
            "label": "Enable sticky filters"
          },
          "filters_show_more_limit": {
            "label": "Enable 'Show more' link after amount of filters"
          }
        },
        "filter_swatches": {
          "header": "Visual filters",
          "paragraph": "For more information on how to set up visual filters [click here](https://help.shopify.com/en/manual/online-store/search-and-discovery/filters#visual-filters).",
          "image_swatches_size": {
            "label": "Image size",
            "options__1": {
              "label": "Extra small"
            },
            "options__2": {
              "label": "Small"
            },
            "options__3": {
              "label": "Medium"
            },
            "options__4": {
              "label": "Large"
            }
          },
          "image_swatches_show_in_circle": {
            "label": "Show images in circle"
          },
          "image_swatches_fill_images": {
            "label": "Fill images"
          }
        },
        "quick_buy": {
          "header": "Quick shop",
          "paragraph": "This will show an ‘add to cart’ button on your product card.",
          "enable_quick_buy_desktop": {
            "label": "Show on desktop"
          },
          "enable_quick_buy_mobile": {
            "label": "Show on mobile"
          },
          "enable_quick_buy_drawer": {
            "label": "Enable quick shop drawer",
            "info": "This will open a compact version of the productpage in a side drawer."
          },
          "enable_quick_buy_qty_selector": {
            "label": "Show amount selector",
            "info": "Not applicable when quick shop drawer is enabled."
          },
          "enable_color_picker": {
            "label": "Show color picker"
          },
          "enable_quick_buy_compact": {
            "label": "Enable compact button",
            "info": "This will show a shopping cart icon instead 'Add to cart', or just 'Options' instead of 'View options'."
          }
        },
        "blog_posts": {
          "header": "Blog posts",
          "show_image": {
            "label": "Show featured image"
          },
          "show_date": {
            "label": "Show date"
          },
          "show_author": {
            "label": "Show author"
          },
          "show_excerpt": {
            "label": "Show excerpt"
          },
          "button_style_post": {
            "label": "Button style"
          }
        },
        "mobile": {
          "header": "Mobile layout",
          "products_per_row_mobile": {
            "label": "Products per row",
            "info": "Only applicable for the product card layout 'Grid'.",
            "options__1": {
              "label": "1"
            },
            "options__2": {
              "label": "2"
            }
          },
          "filter_mobile_sticky": {
            "label": "Enable sticky filter button"
          }
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "title": {
          "name": "Title",
          "settings": {
            "text_alignment": {
              "label": "Content alignment",
              "options__1": {
                "label": "Left"
              },
              "options__2": {
                "label": "Center"
              }
            }
          }
        },
        "results": {
          "name": "Search results"
        },
        "sorting": {
          "name": "Sorting options",
          "settings": {
            "show_sorting_bar": {
              "label": "Show sorting bar",
              "info": "Display the sorting dropdown and product count"
            }
          }
        }
      }
    },
    "page": {
      "name": "Page",
      "settings": {
        "max_width": {
          "label": "Content maximum width",
          "info": "When you choose 1280px, the content will be full width."
        },
        "content_alignment": {
          "label": "Content position",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        },
        "text_alignment": {
          "label": "Text alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Center"
          }
        }
      },
      "blocks": {
        "title": {
          "name": "Title",
          "settings": {
            "title_size": {
              "label": "Heading size",
              "options__1": {
                "label": "Small"
              },
              "options__2": {
                "label": "Medium"
              },
              "options__3": {
                "label": "Large"
              },
              "options__4": {
                "label": "Extra large"
              }
            }
          }
        },
        "content": {
          "name": "Content"
        },
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        }
      }
    },
    "blog": {
      "name": "Blog posts",
      "settings": {
        "title": {
          "label": "Custom heading",
          "info": "Leave empty to show the blog title."
        },
        "show_tags": {
          "label": "Show blog tags"
        },
        "pagination_qty": {
          "label": "Blog posts per page"
        },
        "blog_posts": {
          "header": "Blog posts",
          "show_image": {
            "label": "Show featured image"
          },
          "show_date": {
            "label": "Show date"
          },
          "show_author": {
            "label": "Show author"
          },
          "show_excerpt": {
            "label": "Show excerpt"
          },
          "button_style_post": {
            "label": "Button style"
          }
        }
      }
    },
    "article": {
      "name": "Blog post",
      "settings": {
        "big_fontsize": {
          "label": "Use larger font size"
        },
        "compact": {
          "label": "Compact view"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "featured_image": {
          "name": "Featured image"
        },
        "excerpt": {
          "name": "Excerpt"
        },
        "content": {
          "name": "Content"
        },
        "tags": {
          "name": "Tags"
        },
        "comments": {
          "name": "Comments"
        },
        "title": {
          "name": "Title, author and date",
          "settings": {
            "show_banner": {
              "label": "Show title in full width banner",
              "info": "This will show your title in a banner over the Blog post featured image."
            },
            "overlay_opacity": {
              "label": "Image overlay opacity"
            },
            "color_palette": {
              "label": "Full width banner color scheme",
              "options__1": {
                "label": "Lightest"
              },
              "options__2": {
                "label": "Darkest"
              },
              "options__3": {
                "label": "Light"
              },
              "options__4": {
                "label": "Dark"
              },
              "options__5": {
                "label": "Accent"
              },
              "options__6": {
                "label": "Accent light"
              },
              "options__7": {
                "label": "Gradient light"
              },
              "options__8": {
                "label": "Gradient dark"
              }
            },
            "show_date": {
              "label": "Show date"
            },
            "show_author": {
              "label": "Show author"
            },
            "show_article_reading_time": {
              "label": "Show estimated reading time"
            }
          }
        },
        "share_buttons": {
          "name": "Social share buttons",
          "settings": {
            "share_whatsapp": {
              "label": "Show WhatsApp icon"
            },
            "share_facebook": {
              "label": "Show Facebook icon"
            },
            "share_twitter": {
              "label": "Show X icon"
            },
            "share_pinterest": {
              "label": "Show Pinterest icon"
            },
            "share_messenger": {
              "label": "Show Messenger icon"
            },
            "share_email": {
              "label": "Show Email icon"
            }
          }
        }
      }
    },
    "list_collection": {
      "name": "Collection list",
      "settings": {
        "image_ratio": {
          "label": "Image ratio",
          "options__1": {
            "label": "Portrait"
          },
          "options__2": {
            "label": "Square"
          },
          "options__3": {
            "label": "Landscape"
          }
        },
        "fill_images": {
          "label": "Fill images"
        },
        "pagination_qty": {
          "label": "Collections per page"
        },
        "number_of_items": {
          "label": "Collections per row"
        }
      }
    },
    "cart": {
      "name": "Cart",
      "settings": {
        "show_dynamic_buybutton": {
          "label": "Show dynamic buy button",
          "info": "Customers will see their preferred payment method, like PayPal or Apple Pay. [Learn more](https://help.shopify.com/en/manual/online-store/themes/dynamic-checkout)"
        },
        "enable_discount_tab": {
          "label": "Show discount code redeem button"
        },
        "show_payment_methods": {
          "label": "Show payment methods"
        },
        "show_order_notes": {
          "label": "Show order notes"
        },
        "order_notes_alignment": {
          "label": "Order notes alignment",
          "options__1": {
            "label": "Left"
          },
          "options__2": {
            "label": "Right"
          }
        },
        "checkbox": {
          "header": "Checkbox",
          "enable_terms_checkbox": {
            "label": "Show checkbox"
          },
          "terms_text": {
            "label": "Label for the checkbox",
            "info": "For example, place a link to your privacy policy."
          }
        }
      },
      "blocks": {
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Heading"
            }
          }
        },
        "tab": {
          "name": "Tab",
          "settings": {
            "title": {
              "label": "Heading"
            },
            "text": {
              "label": "Content"
            }
          }
        },
        "shipping_timer": {
          "name": "Shipping timer",
          "settings": {
            "paragraph": "You can find the settings in the general theme settings under the tab named 'Shipping and delivery'."
          }
        }
      }
    },
    "account_dashboard": {
      "name": "Customer dashboard",
      "settings": {
        "show_breadcrumbs": {
          "label": "Show breadcrumbs",
          "options__1": {
            "label": "On desktop"
          },
          "options__2": {
            "label": "On mobile"
          },
          "options__3": {
            "label": "On desktop and mobile"
          },
          "options__4": {
            "label": "Don't show"
          }
        },
        "banner": {
          "header": "Banner",
          "image": {
            "label": "Image"
          },
          "overlay_opacity": {
            "label": "Image overlay opacity"
          },
          "color_palette": {
            "label": "Color scheme",
            "options__1": {
              "label": "Lightest"
            },
            "options__2": {
              "label": "Darkest"
            },
            "options__3": {
              "label": "Light"
            },
            "options__4": {
              "label": "Dark"
            },
            "options__5": {
              "label": "Accent"
            },
            "options__6": {
              "label": "Accent light"
            },
            "options__7": {
              "label": "Gradient light"
            },
            "options__8": {
              "label": "Gradient dark"
            }
          },
          "text_position": {
            "label": "Content position",
            "options__1": {
              "label": "Left"
            },
            "options__2": {
              "label": "Center"
            },
            "options__3": {
              "label": "Right"
            }
          }
        },
        "customer_service": {
          "header": "Customer service",
          "text": {
            "label": "Text"
          },
          "show_phone_link": {
            "label": "Show call button"
          },
          "show_mail_link": {
            "label": "Show mail button"
          },
          "show_whatsapp_link": {
            "label": "Show WhatsApp button"
          }
        }
      }
    },
    "account_login": {
      "name": "Sign in",
      "blocks": {
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Heading"
            }
          }
        }
      }
    },
    "account_register": {
      "name": "Register",
      "blocks": {
        "usp": {
          "name": "Information",
          "settings": {
            "usp": {
              "label": "Heading"
            }
          }
        },
        "newsletter_checkbox": {
          "name": "Newsletter checkbox",
          "settings": {
            "paragraph": "Customize your form texts in the theme translations. [Learn more](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "custom_checkbox": {
          "name": "Custom checkbox",
          "settings": {
            "custom_checkbox_text": {
              "label": "Label for the checkbox",
              "info": "For example, place a link to your privacy policy."
            },
            "required": {
              "label": "Required field"
            }
          }
        }
      }
    },
    "404": {
      "name": "404 page",
      "settings": {
        "image": {
          "label": "Background",
          "info": "For best results with full background setting, use an image with a 3:2 aspect ratio. [Learn more](https://intercom.help/someoneyouknow/en/articles/6208050-404-page)"
        },
        "overlay_opacity": {
          "label": "Image overlay opacity"
        },
        "color_palette": {
          "label": "Color scheme",
          "options__1": {
            "label": "Lightest"
          },
          "options__2": {
            "label": "Darkest"
          },
          "options__3": {
            "label": "Light"
          },
          "options__4": {
            "label": "Dark"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent light"
          },
          "options__7": {
            "label": "Gradient light"
          },
          "options__8": {
            "label": "Gradient dark"
          }
        },
        "video": {
          "label": "Video",
          "info": "Video plays in the page."
        },
        "link_text": {
          "label": "Button label"
        },
        "link_url": {
          "label": "Link"
        },
        "button_color_palette": {
          "label": "Button color scheme",
          "options__1": {
            "label": "Lightest"
          },
          "options__2": {
            "label": "Darkest"
          },
          "options__3": {
            "label": "Light"
          },
          "options__4": {
            "label": "Dark"
          },
          "options__5": {
            "label": "Accent"
          },
          "options__6": {
            "label": "Accent light"
          }
        },
        "show_link": {
          "label": "Show link instead of button"
        }
      }
    },
    "password": {
      "name": "Password page",
      "settings": {
        "image": {
          "label": "Image",
          "info": "Recommended image ratio: 2:3"
        }
      },
      "blocks": {
        "spacer": {
          "name": "Spacing",
          "settings": {
            "height": {
              "label": "Height"
            }
          }
        },
        "logo": {
          "name": "Logo",
          "settings": {
            "logo": {
              "label": "Logo image"
            },
            "logo_width": {
              "label": "Logo width"
            },
            "svg_logo": {
              "label": "Use .svg format",
              "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
            }
          }
        },
        "social_sharing": {
          "name": "Social media icons"
        },
        "message": {
          "name": "Message",
          "settings": {
            "paragraph": "Customize your message in the 'Preferences' section of your Online Store. [Learn more](https://help.shopify.com/en/manual/online-store/themes/password-page#add-password-protection-to-your-online-store)"
          }
        },
        "newsletter_form": {
          "name": "Newsletter form",
          "settings": {
            "paragraph": "Customize your form texts in the theme translations. [Learn more](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "password_form": {
          "name": "Password form",
          "settings": {
            "paragraph": "Customize your form texts in the theme translations. [Learn more](https://help.shopify.com/en/manual/online-store/themes/customizing-themes/language/translate-theme)"
          }
        },
        "title": {
          "name": "Title"
        }
      }
    },
    "giftcard": {
      "name": "Giftcard",
      "settings": {
        "image": {
          "label": "Image",
          "info": "Recommended image ratio: 2:3"
        },
        "logo": {
          "show_logo": {
            "label": "Show logo / shop name"
          },
          "header": "Logo",
          "logo": {
            "label": "Logo image"
          },
          "logo_width": {
            "label": "Logo width"
          },
          "svg_logo": {
            "label": "Use .svg format",
            "info": "Upload your .svg file in 'Files' in the Shopify admin and paste the url to the file here. More info on how to upload the file can be found [here](https://help.shopify.com/en/manual/sell-online/online-store/file-uploads)."
          },
          "show_logo_giftcard": {
            "label": "Show logo / shop name on the giftcard"
          },
          "logo_width_on_giftcard": {
            "label": "Logo width on the giftcard"
          }
        },
        "giftcard": {
          "header": "Giftcard",
          "giftcard_background_color": {
            "label": "Giftcard background"
          },
          "giftcard_text_color": {
            "label": "Giftcard text"
          }
        }
      }
    }
  }
}
