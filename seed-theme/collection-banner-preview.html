<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Collection Banner Preview</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Lato', sans-serif;
            background: #f5f5f5;
        }
        
        .collection-banner-section {
            position: relative;
            width: 100%;
            min-height: 400px;
            padding-bottom: 120px;
            overflow: visible;
        }

        .collection-banner-wrapper {
            position: relative;
            width: 100%;
            height: 100%;
        }

        .collection-banner-placeholder {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 400px;
            background: #E1F2FA;
            z-index: 1;
        }

        .collection-banner-content {
            position: relative;
            z-index: 2;
            padding: 60px 20px 120px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .collection-banner-text {
            max-width: 500px;
        }

        .collection-breadcrumbs {
            font-family: '<PERSON><PERSON>', sans-serif;
            font-size: 16px;
            line-height: 22.4px;
            color: rgba(74, 74, 74, 0.71);
            margin-bottom: 20px;
            display: inline-block;
        }

        .collection-breadcrumbs a {
            color: rgba(74, 74, 74, 0.71);
            text-decoration: none;
        }

        .collection-breadcrumbs span {
            margin: 0 4px;
        }

        .collection-banner-title {
            font-family: 'Merriweather', serif;
            font-size: 30px;
            font-weight: 400;
            line-height: 42px;
            letter-spacing: 0.12px;
            color: rgb(74, 74, 74);
            margin: 20px 0 10px 0;
            max-width: 345px;
        }

        .collection-banner-description {
            font-family: 'Lato', sans-serif;
            font-size: 16px;
            line-height: 22.4px;
            color: rgb(74, 74, 74);
            margin-top: 20px;
            max-width: 345px;
        }

        .collection-features-card {
            position: absolute;
            bottom: -83px;
            left: 50%;
            transform: translateX(-50%);
            background: rgb(255, 255, 255);
            border-radius: 20px;
            box-shadow: rgba(0, 0, 0, 0.25) 0px 0px 24px 0px;
            padding: 15px;
            max-width: 1180px;
            width: calc(100% - 40px);
            z-index: 3;
        }

        .collection-features-grid {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 30px;
        }

        .collection-feature-item {
            flex: 1;
            min-width: 200px;
            max-width: 300px;
            text-align: center;
            position: relative;
        }

        .collection-feature-icon {
            position: relative;
            width: 70px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .collection-feature-svg {
            width: 70px;
            height: 70px;
        }

        .collection-feature-icon-placeholder {
            width: 40px;
            height: 40px;
            background: #e0e0e0;
            border-radius: 50%;
            margin: 15px auto;
        }

        .collection-feature-title {
            font-family: 'Lato', sans-serif;
            font-size: 16px;
            line-height: 22.4px;
            color: rgba(74, 74, 74, 0.8);
            margin: 0 0 5px 0;
            font-weight: 600;
        }

        .collection-feature-description {
            font-family: 'Lato', sans-serif;
            font-size: 16px;
            line-height: 19.2px;
            color: rgba(74, 74, 74, 0.6);
            margin: 0;
        }

        .next-section {
            margin-top: 100px;
            padding: 40px 20px;
            background: white;
            text-align: center;
        }

        @media (max-width: 768px) {
            .collection-banner-section {
                padding-bottom: 100px;
            }

            .collection-banner-content {
                padding: 40px 20px 80px;
            }

            .collection-banner-title {
                font-size: 24px;
                line-height: 32px;
            }

            .collection-features-card {
                bottom: -60px;
                width: calc(100% - 20px);
                padding: 10px;
            }

            .collection-features-grid {
                flex-direction: column;
                gap: 20px;
            }

            .collection-feature-item {
                max-width: 100%;
            }
        }

        @media (max-width: 480px) {
            .collection-banner-section {
                padding-bottom: 80px;
            }

            .collection-banner-content {
                padding: 30px 15px 60px;
            }

            .collection-banner-title {
                font-size: 20px;
                line-height: 28px;
            }

            .collection-breadcrumbs {
                font-size: 14px;
            }

            .collection-features-card {
                bottom: -50px;
            }
        }
    </style>
</head>
<body>
    <!-- SVG Icons -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display: none;">
        <symbol id="money-back" viewBox="0 0 70 70">
            <circle cx="35" cy="35" r="30" fill="#4A90E2" stroke="#fff" stroke-width="2"/>
            <path d="M25 35 L32 42 L45 28" stroke="#fff" stroke-width="3" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
            <circle cx="35" cy="35" r="15" fill="none" stroke="#fff" stroke-width="2"/>
            <text x="35" y="40" text-anchor="middle" fill="#fff" font-size="8" font-family="Arial">$</text>
        </symbol>
        
        <symbol id="happy-feet" viewBox="0 0 70 70">
            <ellipse cx="35" cy="45" rx="20" ry="15" fill="#50C878"/>
            <ellipse cx="30" cy="35" rx="8" ry="12" fill="#50C878"/>
            <ellipse cx="40" cy="35" rx="8" ry="12" fill="#50C878"/>
            <circle cx="28" cy="30" r="3" fill="#fff"/>
            <circle cx="42" cy="30" r="3" fill="#fff"/>
            <path d="M30 40 Q35 45 40 40" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round"/>
        </symbol>
        
        <symbol id="fsa-hsa" viewBox="0 0 70 70">
            <rect x="15" y="20" width="40" height="30" rx="5" fill="#E74C3C" stroke="#fff" stroke-width="2"/>
            <rect x="20" y="25" width="30" height="20" fill="#fff"/>
            <path d="M30 30 L30 40 M25 35 L35 35" stroke="#E74C3C" stroke-width="2" stroke-linecap="round"/>
            <circle cx="50" cy="25" r="8" fill="#2ECC71"/>
            <path d="M46 25 L49 28 L54 22" stroke="#fff" stroke-width="2" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
        </symbol>
    </svg>

    <section class="collection-banner-section">
        <div class="collection-banner-wrapper">
            <div class="collection-banner-placeholder"></div>
            
            <div class="collection-banner-content">
                <div class="collection-banner-text">
                    <nav class="collection-breadcrumbs">
                        <a href="#">Home</a>
                        <span>/</span>
                        <a href="#">Shop</a>
                        <span>/</span>
                        <span>Upsteps For Everyday</span>
                    </nav>
                    
                    <h1 class="collection-banner-title">Up to 70% OFF | BACK TO SCHOOL</h1>
                    
                    <div class="collection-banner-description">
                        60% less expensive than any other foot specialist with a 100% Satisfaction Guarantee - or your money back!
                    </div>
                </div>
            </div>
            
            <div class="collection-features-card">
                <div class="collection-features-grid">
                    <div class="collection-feature-item">
                        <div class="collection-feature-icon">
                            <div class="collection-feature-icon-placeholder"></div>
                        </div>
                        <h3 class="collection-feature-title">180-day money-back guarantee*</h3>
                        <p class="collection-feature-description">We're so sure they'll work, we're giving you 6 months to try them - no strings attached.</p>
                    </div>

                    <div class="collection-feature-item">
                        <div class="collection-feature-icon">
                            <div class="collection-feature-icon-placeholder"></div>
                        </div>
                        <h3 class="collection-feature-title">Over 200k happy feet</h3>
                        <p class="collection-feature-description">That's 1 billion daily steps walked painlessly and comfortably thanks to Upsteps!</p>
                    </div>

                    <div class="collection-feature-item">
                        <div class="collection-feature-icon">
                            <div class="collection-feature-icon-placeholder"></div>
                        </div>
                        <h3 class="collection-feature-title">FSA/HSA eligible</h3>
                        <p class="collection-feature-description">Use your FSA and HSA dollars before they expire, you can use them to buy custom orthotics at Upstep.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="next-section">
        <h2>Next Section Content</h2>
        <p>This demonstrates the spacing after the banner section.</p>
    </div>
</body>
</html>
