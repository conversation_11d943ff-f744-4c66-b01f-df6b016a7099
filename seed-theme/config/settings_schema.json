[{"name": "theme_info", "theme_name": "Xtra", "theme_version": "6.1.0", "theme_author": "Someone You Know", "theme_documentation_url": "https://intercom.help/someoneyouknow/en/collections/3431583-theme-xtra-2-0-for-shopify-manuals", "theme_support_url": "https://someoneyouknow.online/"}, {"name": "t:settings_schema.accessibility.name", "settings": [{"type": "image_picker", "id": "favicon", "label": "t:settings_schema.accessibility.settings.favicon.label", "info": "t:settings_schema.accessibility.settings.favicon.info"}, {"type": "header", "content": "t:settings_schema.accessibility.settings.accessibility.header"}, {"id": "show_accessibility", "type": "checkbox", "label": "t:settings_schema.accessibility.settings.accessibility.show_accessibility.label", "info": "t:settings_schema.accessibility.settings.accessibility.show_accessibility.info", "default": true}, {"type": "checkbox", "id": "enable_accessibility_default", "label": "t:settings_schema.accessibility.settings.accessibility.enable_accessibility_default.label", "info": "t:settings_schema.accessibility.settings.accessibility.enable_accessibility_default.info", "default": false}, {"id": "back_to_top_button", "type": "checkbox", "label": "t:settings_schema.accessibility.settings.accessibility.back_to_top_button.label", "default": false}]}, {"name": "t:settings_schema.logos.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logos.settings.logo.label"}, {"id": "logo_width", "type": "range", "label": "t:settings_schema.logos.settings.logo_width.label", "info": "t:settings_schema.logos.settings.logo_width.info", "min": 50, "max": 300, "step": 5, "unit": "px", "default": 130}, {"type": "header", "content": "t:settings_schema.logos.settings.mobile.header"}, {"type": "image_picker", "id": "mobile_logo", "label": "t:settings_schema.logos.settings.mobile.mobile_logo.label", "info": "t:settings_schema.logos.settings.mobile.mobile_logo.info"}, {"id": "logo_width_mobile", "type": "range", "label": "t:settings_schema.logos.settings.mobile.logo_width_mobile.label", "info": "t:settings_schema.logos.settings.mobile.logo_width_mobile.info", "min": 10, "max": 300, "step": 5, "unit": "px", "default": 80}]}, {"name": "t:settings_schema.layout.name", "settings": [{"type": "range", "id": "width", "label": "t:settings_schema.layout.settings.width.label", "info": "t:settings_schema.layout.settings.width.info", "min": 1000, "max": 2000, "step": 20, "unit": "px", "default": 1280}]}, {"name": "t:settings_schema.colors.name", "settings": [{"type": "color_scheme_group", "id": "color_schemes", "definition": [{"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.background.header"}, {"type": "color", "id": "primary_bg", "label": "t:settings_schema.colors.settings.color_scheme.background.primary_bg.label", "default": "#FFFFFF"}, {"type": "color_background", "id": "primary_bg_gradient", "label": "t:settings_schema.colors.settings.color_scheme.background.primary_bg_gradient.label", "info": "t:settings_schema.colors.settings.color_scheme.background.primary_bg_gradient.info"}, {"type": "color", "id": "secondary_bg", "label": "t:settings_schema.colors.settings.color_scheme.background.secondary_bg.label", "default": "#fbf8f4"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.typography.header"}, {"type": "color", "id": "title_color", "label": "t:settings_schema.colors.settings.color_scheme.typography.title_color.label", "default": "#000000"}, {"type": "color_background", "id": "title_color_gradient", "label": "t:settings_schema.colors.settings.color_scheme.typography.title_color_gradient.label", "info": "t:settings_schema.colors.settings.color_scheme.typography.title_color_gradient.info"}, {"type": "color", "id": "primary_fg", "label": "t:settings_schema.colors.settings.color_scheme.typography.primary_fg.label", "default": "#333333"}, {"type": "color", "id": "link_hover_color", "label": "t:settings_schema.colors.settings.color_scheme.typography.link_hover_color.label", "default": "#FF6602"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.primary_button.header"}, {"type": "color", "id": "primary_button_bg", "label": "t:settings_schema.colors.settings.color_scheme.primary_button.primary_button_bg.label", "default": "#FF6602"}, {"type": "color", "id": "primary_button_fg", "label": "t:settings_schema.colors.settings.color_scheme.primary_button.primary_button_fg.label", "default": "#ffffff"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.secondary_button.header"}, {"type": "color", "id": "secondary_button_bg", "label": "t:settings_schema.colors.settings.color_scheme.secondary_button.secondary_button_bg.label", "default": "#FF6602"}, {"type": "color", "id": "secondary_button_fg", "label": "t:settings_schema.colors.settings.color_scheme.secondary_button.secondary_button_fg.label", "default": "#ffffff"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.tertiary_button.header"}, {"type": "color", "id": "tertiary_button_bg", "label": "t:settings_schema.colors.settings.color_scheme.tertiary_button.tertiary_button_bg.label", "default": "#FF6602"}, {"type": "color", "id": "tertiary_button_fg", "label": "t:settings_schema.colors.settings.color_scheme.tertiary_button.tertiary_button_fg.label", "default": "#ffffff"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.inputs.header"}, {"type": "color", "id": "input_bg", "label": "t:settings_schema.colors.settings.color_scheme.inputs.input_bg.label", "default": "#ffffff"}, {"type": "color", "id": "input_fg", "label": "t:settings_schema.colors.settings.color_scheme.inputs.input_fg.label", "default": "#000000"}, {"type": "header", "content": "t:settings_schema.colors.settings.color_scheme.general.header"}, {"type": "color", "id": "primary_bd", "label": "t:settings_schema.colors.settings.color_scheme.general.primary_bd.label", "default": "#e5e5e5"}, {"type": "color", "id": "accent", "label": "t:settings_schema.colors.settings.color_scheme.general.accent.label", "default": "#f9af11"}, {"type": "color_background", "id": "accent_gradient", "label": "t:settings_schema.colors.settings.color_scheme.general.accent_gradient.label", "info": "t:settings_schema.colors.settings.color_scheme.general.accent_gradient.info"}], "role": {"background": {"solid": "primary_bg", "gradient": "primary_bg_gradient"}, "text": "primary_fg", "links": "primary_fg", "icons": "primary_fg", "primary_button": "primary_button_bg", "on_primary_button": "primary_button_fg", "primary_button_border": "primary_button_bg", "secondary_button": "secondary_button_bg", "on_secondary_button": "secondary_button_fg", "secondary_button_border": "secondary_button_bg"}}, {"type": "color_scheme", "id": "default_color_scheme", "label": "t:settings_schema.colors.settings.default_color_scheme.label", "info": "t:settings_schema.colors.settings.default_color_scheme.info", "default": "scheme-1"}, {"type": "header", "content": "t:settings_schema.colors.settings.general.header"}, {"type": "color", "id": "positive_vibes", "label": "t:settings_schema.colors.settings.general.positive_vibes.label", "default": "#6BB099"}, {"type": "color", "id": "negative_vibes", "label": "t:settings_schema.colors.settings.general.negative_vibes.label", "default": "#e52a2a"}, {"type": "header", "content": "t:settings_schema.colors.settings.text_selection.header"}, {"type": "color", "id": "text_selection_bg", "label": "t:settings_schema.colors.settings.text_selection.text_selection_bg.label", "info": "t:settings_schema.colors.settings.text_selection.text_selection_bg.info", "default": "#3366cc"}, {"type": "color", "id": "text_selection_color", "label": "t:settings_schema.colors.settings.text_selection.text_selection_color.label", "info": "t:settings_schema.colors.settings.text_selection.text_selection_color.info", "default": "#ffffff"}, {"type": "header", "content": "t:settings_schema.colors.settings.buttons.header"}, {"type": "color", "id": "buy_button_color", "label": "t:settings_schema.colors.settings.buttons.buy_button_color.label", "default": "#FF6602"}, {"type": "color", "id": "buy_button_text_color", "label": "t:settings_schema.colors.settings.buttons.buy_button_text_color.label", "default": "#ffffff"}, {"type": "color", "id": "dynamic_buy_button_color", "label": "t:settings_schema.colors.settings.buttons.dynamic_buy_button_color.label", "default": "#FF6602"}, {"type": "color", "id": "dynamic_buy_button_text_color", "label": "t:settings_schema.colors.settings.buttons.dynamic_buy_button_text_color.label", "default": "#ffffff"}, {"type": "color", "id": "preorder_button_color", "label": "t:settings_schema.colors.settings.buttons.preorder_button_color.label", "default": "#EFB34C"}, {"type": "color", "id": "preorder_button_text_color", "label": "t:settings_schema.colors.settings.buttons.preorder_button_text_color.label", "default": "#ffffff"}, {"type": "color", "id": "unavailable_button_color", "label": "t:settings_schema.colors.settings.buttons.unavailable_button_color.label", "default": "#e5e5e5"}, {"type": "color", "id": "unavailable_button_text_color", "label": "t:settings_schema.colors.settings.buttons.unavailable_button_text_color.label", "default": "#ffffff"}, {"type": "select", "id": "checkout_button_style", "label": "t:settings_schema.colors.settings.buttons.checkout_button_style.label", "options": [{"value": "primary plain", "label": "t:global.button.button_style.primary.label", "group": "t:global.button.button_style.group.plain"}, {"value": "secondary plain", "label": "t:global.button.button_style.secondary.label", "group": "t:global.button.button_style.group.plain"}, {"value": "tertiary plain", "label": "t:global.button.button_style.tertiary.label", "group": "t:global.button.button_style.group.plain"}, {"value": "buy_button plain", "label": "t:global.button.button_style.buy_button.label", "group": "t:global.button.button_style.group.plain"}, {"value": "dynamic_buy_button plain", "label": "t:global.button.button_style.dynamic_buy_button.label", "group": "t:global.button.button_style.group.plain"}, {"value": "primary inv", "label": "t:global.button.button_style.primary.label", "group": "t:global.button.button_style.group.inv"}, {"value": "secondary inv", "label": "t:global.button.button_style.secondary.label", "group": "t:global.button.button_style.group.inv"}, {"value": "tertiary inv", "label": "t:global.button.button_style.tertiary.label", "group": "t:global.button.button_style.group.inv"}, {"value": "buy_button inv", "label": "t:global.button.button_style.buy_button.label", "group": "t:global.button.button_style.group.inv"}, {"value": "dynamic_buy_button inv", "label": "t:global.button.button_style.dynamic_buy_button.label", "group": "t:global.button.button_style.group.inv"}], "default": "dynamic_buy_button plain"}, {"type": "color_scheme", "id": "dropdown_color", "label": "t:settings_schema.colors.settings.dropdown_color.label", "info": "t:settings_schema.colors.settings.dropdown_color.info", "default": "scheme-1"}, {"type": "header", "content": "t:settings_schema.colors.settings.product_prices.header"}, {"type": "color", "id": "price_color", "label": "t:settings_schema.colors.settings.product_prices.price_color.label", "default": "#333333"}, {"type": "color", "id": "compare_at_price_color", "label": "t:settings_schema.colors.settings.product_prices.compare_at_price_color.label", "default": "#e52a2a"}, {"type": "header", "content": "t:settings_schema.colors.settings.product_label.header"}, {"type": "paragraph", "content": "t:settings_schema.colors.settings.product_label.paragraph"}, {"type": "color", "id": "product_label_color", "label": "t:settings_schema.colors.settings.product_label.product_label_color.label", "default": "#333333"}, {"type": "color", "id": "product_label_text_color", "label": "t:settings_schema.colors.settings.product_label.product_label_text_color.label", "default": "#ffffff"}, {"type": "color", "id": "sale_label_color", "label": "t:settings_schema.colors.settings.product_label.sale_label_color.label", "default": "#e52a2a"}, {"type": "color", "id": "sale_label_text_color", "label": "t:settings_schema.colors.settings.product_label.sale_label_text_color.label", "default": "#ffffff"}]}, {"name": "t:settings_schema.typography.name", "settings": [{"type": "header", "content": "t:settings_schema.typography.settings.fonts.header"}, {"type": "paragraph", "content": "t:settings_schema.typography.settings.fonts.paragraph"}, {"type": "font_picker", "id": "heading_font", "label": "t:settings_schema.typography.settings.fonts.primary_font.label", "default": "helvetica_n4"}, {"type": "font_picker", "id": "body_font", "label": "t:settings_schema.typography.settings.fonts.secondary_font.label", "default": "helvetica_n4"}, {"type": "header", "content": "t:settings_schema.typography.settings.custom_fonts.header"}, {"type": "paragraph", "content": "t:settings_schema.typography.settings.custom_fonts.paragraph"}, {"type": "checkbox", "id": "enable_custom_primary_font", "label": "t:settings_schema.typography.settings.custom_fonts.enable_custom_primary_font.label"}, {"type": "text", "id": "custom_primary_font_name", "label": "t:settings_schema.typography.settings.custom_fonts.custom_primary_font_name.label", "visible_if": "{{ settings.enable_custom_primary_font }}"}, {"type": "url", "id": "custom_primary_font_file", "label": "t:settings_schema.typography.settings.custom_fonts.custom_primary_font_file.label", "info": "t:settings_schema.typography.settings.custom_fonts.custom_primary_font_file.info", "visible_if": "{{ settings.enable_custom_primary_font }}"}, {"type": "textarea", "id": "custom_primary_font_snippet", "label": "t:settings_schema.typography.settings.custom_fonts.custom_primary_font_snippet.label", "info": "t:settings_schema.typography.settings.custom_fonts.custom_primary_font_snippet.info", "visible_if": "{{ settings.enable_custom_primary_font }}"}, {"type": "checkbox", "id": "enable_custom_secondary_font", "label": "t:settings_schema.typography.settings.custom_fonts.enable_custom_secondary_font.label"}, {"type": "text", "id": "custom_secondary_font_name", "label": "t:settings_schema.typography.settings.custom_fonts.custom_secondary_font_name.label", "visible_if": "{{ settings.enable_custom_secondary_font }}"}, {"type": "url", "id": "custom_secondary_font_file", "label": "t:settings_schema.typography.settings.custom_fonts.custom_secondary_font_file.label", "info": "t:settings_schema.typography.settings.custom_fonts.custom_secondary_font_file.info", "visible_if": "{{ settings.enable_custom_secondary_font }}"}, {"type": "textarea", "id": "custom_secondary_font_snippet", "label": "t:settings_schema.typography.settings.custom_fonts.custom_secondary_font_snippet.label", "info": "t:settings_schema.typography.settings.custom_fonts.custom_secondary_font_snippet.info", "visible_if": "{{ settings.enable_custom_secondary_font }}"}, {"type": "header", "content": "t:settings_schema.typography.settings.headings.header"}, {"type": "range", "id": "h1_size", "label": "t:global.typography.title_size.h1.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h2_size", "label": "t:global.typography.title_size.h2.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h3_size", "label": "t:global.typography.title_size.h3.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h4_size", "label": "t:global.typography.title_size.h4.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h5_size", "label": "t:global.typography.title_size.h5.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h6_size", "label": "t:global.typography.title_size.h6.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "heading_line_height", "label": "t:settings_schema.typography.settings.headings.heading_line_height.label", "min": 50, "max": 250, "step": 5, "default": 115}, {"type": "select", "id": "primary_case", "label": "t:settings_schema.typography.settings.headings.primary_case.label", "options": [{"value": "none", "label": "t:settings_schema.typography.settings.headings.primary_case.options__1.label"}, {"value": "uppercase", "label": "t:settings_schema.typography.settings.headings.primary_case.options__2.label"}], "default": "none"}, {"type": "range", "id": "primary_letter_spacing", "label": "t:settings_schema.typography.settings.headings.primary_letter_spacing.label", "min": -25, "max": 25, "step": 1, "default": 0}, {"type": "select", "id": "secondary_case", "label": "t:settings_schema.typography.settings.headings.secondary_case.label", "options": [{"value": "none", "label": "t:settings_schema.typography.settings.headings.secondary_case.options__1.label"}, {"value": "uppercase", "label": "t:settings_schema.typography.settings.headings.secondary_case.options__2.label"}], "default": "none"}, {"type": "range", "id": "secondary_letter_spacing", "label": "t:settings_schema.typography.settings.headings.secondary_letter_spacing.label", "min": -25, "max": 25, "step": 1, "default": 0}, {"type": "select", "id": "global_title_size", "label": "t:settings_schema.typography.settings.headings.global_title_size.label", "info": "t:settings_schema.typography.settings.headings.global_title_size.info", "options": [{"value": "h1", "label": "t:global.typography.title_size.h1.label"}, {"value": "h2", "label": "t:global.typography.title_size.h2.label"}, {"value": "h3", "label": "t:global.typography.title_size.h3.label"}, {"value": "h4", "label": "t:global.typography.title_size.h4.label"}, {"value": "h5", "label": "t:global.typography.title_size.h5.label"}], "default": "h1"}, {"type": "header", "content": "t:settings_schema.typography.settings.body.header"}, {"type": "select", "id": "body_font_family", "label": "t:settings_schema.typography.settings.body.body_font.label", "options": [{"value": "primary", "label": "t:settings_schema.typography.settings.body.body_font.options__1.label"}, {"value": "secondary", "label": "t:settings_schema.typography.settings.body.body_font.options__2.label"}], "default": "secondary"}, {"type": "range", "id": "body_font_size", "label": "t:settings_schema.typography.settings.body.body_font_size.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "base_font_size", "label": "Base font size", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "select", "id": "body_case", "label": "t:settings_schema.typography.settings.body.body_case.label", "options": [{"value": "none", "label": "t:settings_schema.typography.settings.body.body_case.options__1.label"}, {"value": "uppercase", "label": "t:settings_schema.typography.settings.body.body_case.options__2.label"}], "default": "none"}, {"type": "range", "id": "body_letter_spacing", "label": "t:settings_schema.typography.settings.body.body_letter_spacing.label", "min": -25, "max": 25, "step": 1, "default": 0}, {"type": "range", "id": "body_line_height", "label": "t:settings_schema.typography.settings.body.body_line_height.label", "min": 100, "max": 250, "step": 5, "default": 185}, {"type": "header", "content": "t:settings_schema.typography.settings.prices.header"}, {"type": "select", "id": "prices_font", "label": "t:settings_schema.typography.settings.prices.prices_font.label", "options": [{"value": "primary", "label": "t:settings_schema.typography.settings.prices.prices_font.options__1.label"}, {"value": "secondary", "label": "t:settings_schema.typography.settings.prices.prices_font.options__2.label"}], "default": "secondary"}, {"type": "select", "id": "prices_font_weight", "label": "t:settings_schema.typography.settings.prices.prices_font_weight.label", "options": [{"value": "100", "label": "t:global.typography.font_weight.100.label"}, {"value": "200", "label": "t:global.typography.font_weight.200.label"}, {"value": "300", "label": "t:global.typography.font_weight.300.label"}, {"value": "400", "label": "t:global.typography.font_weight.400.label"}, {"value": "500", "label": "t:global.typography.font_weight.500.label"}, {"value": "600", "label": "t:global.typography.font_weight.600.label"}, {"value": "700", "label": "t:global.typography.font_weight.700.label"}, {"value": "800", "label": "t:global.typography.font_weight.800.label"}, {"value": "900", "label": "t:global.typography.font_weight.900.label"}], "default": "800"}, {"type": "header", "content": "t:settings_schema.typography.settings.breadcrumbs.header"}, {"type": "select", "id": "breadcrumbs_font", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font.label", "options": [{"value": "primary", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font.options__1.label"}, {"value": "secondary", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font.options__2.label"}], "default": "secondary"}, {"type": "select", "id": "breadcrumbs_font_size", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font_size.label", "options": [{"value": "13px", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font_size.options__1.label"}, {"value": "14px", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font_size.options__2.label"}, {"value": "16px", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font_size.options__3.label"}, {"value": "18px", "label": "t:settings_schema.typography.settings.breadcrumbs.breadcrumbs_font_size.options__4.label"}], "default": "13px"}, {"type": "header", "content": "t:settings_schema.typography.settings.drawers.header"}, {"type": "select", "id": "drawers_font_size_heading", "label": "t:settings_schema.typography.settings.drawers.drawers_font_size_heading.label", "options": [{"value": "h1", "label": "t:global.typography.title_size.h1.label"}, {"value": "h2", "label": "t:global.typography.title_size.h2.label"}, {"value": "h3", "label": "t:global.typography.title_size.h3.label"}, {"value": "h4", "label": "t:global.typography.title_size.h4.label"}], "default": "h2"}, {"type": "header", "content": "t:settings_schema.typography.settings.hyphens.header"}, {"type": "checkbox", "id": "enable_hyphens", "label": "t:settings_schema.typography.settings.hyphens.enable_hyphens.label", "info": "t:settings_schema.typography.settings.hyphens.enable_hyphens.info", "default": true}, {"type": "header", "content": "t:settings_schema.typography.settings.mobile.header"}, {"type": "range", "id": "h1_size_mobile", "label": "t:global.typography.title_size.h1.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h2_size_mobile", "label": "t:global.typography.title_size.h2.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h3_size_mobile", "label": "t:global.typography.title_size.h3.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h4_size_mobile", "label": "t:global.typography.title_size.h4.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h5_size_mobile", "label": "t:global.typography.title_size.h5.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "range", "id": "h6_size_mobile", "label": "t:global.typography.title_size.h6.label", "min": 50, "max": 250, "step": 2, "unit": "%", "default": 100}, {"type": "select", "id": "body_font_size_mobile", "label": "t:settings_schema.typography.settings.mobile.body_font_size_mobile.label", "options": [{"value": "13px", "label": "t:settings_schema.typography.settings.mobile.body_font_size_mobile.options__1.label"}, {"value": "14px", "label": "t:settings_schema.typography.settings.mobile.body_font_size_mobile.options__2.label"}, {"value": "16px", "label": "t:settings_schema.typography.settings.mobile.body_font_size_mobile.options__3.label"}, {"value": "18px", "label": "t:settings_schema.typography.settings.mobile.body_font_size_mobile.options__4.label"}], "default": "14px"}]}, {"name": "t:settings_schema.images.name", "settings": [{"type": "header", "content": "t:settings_schema.images.settings.product_images.header"}, {"id": "product_image_ratio", "type": "select", "label": "t:settings_schema.images.settings.product_images.product_image_ratio.label", "options": [{"value": "310x430", "label": "t:settings_schema.images.settings.product_images.product_image_ratio.options__1.label"}, {"value": "310x310", "label": "t:settings_schema.images.settings.product_images.product_image_ratio.options__2.label"}, {"value": "430x310", "label": "t:settings_schema.images.settings.product_images.product_image_ratio.options__3.label"}], "default": "310x430"}, {"id": "fill_product_images", "type": "checkbox", "label": "t:settings_schema.images.settings.product_images.fill_product_images.label", "default": false}, {"id": "show_secondary_image", "type": "checkbox", "label": "t:settings_schema.images.settings.product_images.show_secondary_image.label"}, {"type": "header", "content": "t:settings_schema.images.settings.multiply.header"}, {"type": "paragraph", "content": "t:settings_schema.images.settings.multiply.paragraph"}, {"id": "multiply_product_images", "type": "select", "label": "t:settings_schema.images.settings.multiply.multiply_product_images.label", "options": [{"value": "none", "label": "t:settings_schema.images.settings.multiply.multiply_product_images.options__1.label"}, {"value": "multiply", "label": "t:settings_schema.images.settings.multiply.multiply_product_images.options__2.label"}, {"value": "multiply-bg", "label": "t:settings_schema.images.settings.multiply.multiply_product_images.options__3.label"}], "default": "none"}, {"type": "color_scheme", "id": "multiply_product_images_color_palette", "label": "t:settings_schema.images.settings.multiply.multiply_product_images_color_palette.label", "default": "scheme-3", "visible_if": "{{ settings.multiply_product_images == 'multiply-bg' }}"}, {"id": "multiply_collection_images", "type": "select", "label": "t:settings_schema.images.settings.multiply.multiply_collection_images.label", "options": [{"value": "none", "label": "t:settings_schema.images.settings.multiply.multiply_collection_images.options__1.label"}, {"value": "multiply", "label": "t:settings_schema.images.settings.multiply.multiply_collection_images.options__2.label"}, {"value": "multiply-bg", "label": "t:settings_schema.images.settings.multiply.multiply_collection_images.options__3.label"}], "default": "none"}, {"type": "color_scheme", "id": "multiply_collection_images_color_palette", "label": "t:settings_schema.images.settings.multiply.multiply_collection_images_color_palette.label", "default": "scheme-3", "visible_if": "{{ settings.multiply_collection_images == 'multiply-bg' }}"}, {"type": "header", "content": "t:settings_schema.images.settings.corner_radius.header"}, {"id": "everything_rounded", "type": "checkbox", "label": "t:settings_schema.images.settings.corner_radius.everything_rounded.label", "default": true}]}, {"name": "t:settings_schema.buttons.name", "settings": [{"type": "select", "id": "button_height", "label": "t:settings_schema.buttons.settings.button_height.label", "options": [{"value": "size-s", "label": "t:settings_schema.buttons.settings.button_height.options__1.label"}, {"value": "size-m", "label": "t:settings_schema.buttons.settings.button_height.options__2.label"}, {"value": "size-l", "label": "t:settings_schema.buttons.settings.button_height.options__3.label"}], "default": "size-s"}, {"type": "select", "id": "button_style", "label": "t:settings_schema.buttons.settings.button_style.label", "options": [{"value": "solid", "label": "t:settings_schema.buttons.settings.button_style.options__1.label"}, {"value": "plain", "label": "t:settings_schema.buttons.settings.button_style.options__2.label"}, {"value": "inv", "label": "t:settings_schema.buttons.settings.button_style.options__3.label"}], "default": "solid"}, {"type": "select", "id": "button_rounded", "label": "t:settings_schema.buttons.settings.button_rounded.label", "options": [{"value": "square", "label": "t:settings_schema.buttons.settings.button_rounded.options__1.label"}, {"value": "slightly-rounded", "label": "t:settings_schema.buttons.settings.button_rounded.options__2.label"}, {"value": "rounded", "label": "t:settings_schema.buttons.settings.button_rounded.options__3.label"}], "default": "slightly-rounded"}, {"id": "button_font", "type": "select", "label": "t:settings_schema.buttons.settings.button_font.label", "options": [{"label": "t:settings_schema.buttons.settings.button_font.options__1.label", "value": "title"}, {"label": "t:settings_schema.buttons.settings.button_font.options__2.label", "value": "body"}], "default": "body"}, {"type": "select", "id": "button_font_weight", "label": "t:settings_schema.buttons.settings.button_font_weight.label", "options": [{"value": "100", "label": "t:global.typography.font_weight.100.label"}, {"value": "200", "label": "t:global.typography.font_weight.200.label"}, {"value": "300", "label": "t:global.typography.font_weight.300.label"}, {"value": "400", "label": "t:global.typography.font_weight.400.label"}, {"value": "500", "label": "t:global.typography.font_weight.500.label"}, {"value": "600", "label": "t:global.typography.font_weight.600.label"}, {"value": "700", "label": "t:global.typography.font_weight.700.label"}, {"value": "800", "label": "t:global.typography.font_weight.800.label"}, {"value": "900", "label": "t:global.typography.font_weight.900.label"}], "default": "700"}, {"type": "select", "id": "button_case", "label": "t:settings_schema.buttons.settings.button_case.label", "options": [{"value": "none", "label": "t:settings_schema.buttons.settings.button_case.options__1.label"}, {"value": "uppercase", "label": "t:settings_schema.buttons.settings.button_case.options__2.label"}], "default": "none"}]}, {"name": "t:settings_schema.cart.name", "settings": [{"id": "enable_cart_drawer", "type": "checkbox", "label": "t:settings_schema.cart.settings.enable_cart_drawer.label", "info": "t:settings_schema.cart.settings.enable_cart_drawer.info", "default": true}, {"id": "empty_cart_text", "type": "richtext", "label": "t:settings_schema.cart.settings.empty_cart_text.label", "default": "<p>Your cart is empty..</p>"}, {"type": "header", "content": "t:settings_schema.cart.settings.cart_drawer_undo.header"}, {"id": "enable_cart_drawer_undo_remove", "type": "checkbox", "label": "t:settings_schema.cart.settings.cart_drawer_undo.enable_cart_drawer_undo_remove.label", "info": "t:settings_schema.cart.settings.cart_drawer_undo.enable_cart_drawer_undo_remove.info"}, {"id": "enable_cart_drawer_undo_remove_delay", "type": "checkbox", "label": "t:settings_schema.cart.settings.cart_drawer_undo.enable_cart_drawer_undo_remove_delay.label", "info": "t:settings_schema.cart.settings.cart_drawer_undo.enable_cart_drawer_undo_remove_delay.info", "visible_if": "{{ settings.enable_cart_drawer_undo_remove }}"}, {"id": "cart_drawer_undo_remove_delay", "type": "range", "label": "t:settings_schema.cart.settings.cart_drawer_undo.cart_drawer_undo_remove_delay.label", "min": 3, "max": 10, "step": 1, "default": 5, "unit": "sec", "visible_if": "{{ settings.enable_cart_drawer_undo_remove and settings.enable_cart_drawer_undo_remove_delay }}"}, {"id": "cart_drawer_checkout_button", "type": "checkbox", "label": "t:settings_schema.cart.settings.cart_drawer_checkout_button.label"}, {"type": "checkbox", "id": "cart_drawer_discount_tab", "label": "t:main.cart.settings.enable_discount_tab.label"}, {"id": "cart_drawer_order_notes", "type": "checkbox", "label": "t:settings_schema.cart.settings.cart_drawer_order_notes.label"}, {"type": "header", "content": "t:settings_schema.cart.settings.upsell.header"}, {"id": "enable_cart_drawer_upsell_complementary", "type": "checkbox", "label": "t:settings_schema.cart.settings.upsell.enable_cart_drawer_upsell_complementary.label", "info": "t:settings_schema.cart.settings.upsell.enable_cart_drawer_upsell_complementary.info"}, {"id": "enable_cart_drawer_upsell_related", "type": "checkbox", "label": "t:settings_schema.cart.settings.upsell.enable_cart_drawer_upsell_related.label"}, {"id": "enable_cart_drawer_upsell_variants", "type": "checkbox", "label": "t:settings_schema.cart.settings.upsell.enable_cart_drawer_upsell_variants.label", "info": "t:settings_schema.cart.settings.upsell.enable_cart_drawer_upsell_variants.info"}]}, {"name": "t:settings_schema.trustbadge.name", "settings": [{"type": "checkbox", "id": "show_trustbadge", "label": "t:settings_schema.trustbadge.settings.show_trustbadge.label", "info": "t:settings_schema.trustbadge.settings.show_trustbadge.info"}, {"id": "trustbadge_image", "type": "select", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.label", "options": [{"value": "none", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__1.label"}, {"value": "amazon pay", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__2.label"}, {"value": "authorize.net", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__3.label"}, {"value": "klarna", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__4.label"}, {"value": "opayo", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__5.label"}, {"value": "paypal", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__6.label"}, {"value": "shop pay", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__7.label"}, {"value": "skrill", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__8.label"}, {"value": "stripe", "label": "t:settings_schema.trustbadge.settings.trustbadge_image.options__9.label"}], "default": "none"}, {"type": "image_picker", "id": "trustbadge_custom_image", "label": "t:settings_schema.trustbadge.settings.trustbadge_custom_image.label", "info": "t:settings_schema.trustbadge.settings.trustbadge_custom_image.info"}, {"type": "richtext", "id": "trustbadge_text", "label": "t:settings_schema.trustbadge.settings.trustbadge_text.label", "default": "<p>Guaranteed <b>secure & safe</b> checkout.</p>"}]}, {"name": "t:settings_schema.search_drawer.name", "settings": [{"id": "enable_search_drawer", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.enable_search_drawer.label", "default": true}, {"type": "header", "content": "t:settings_schema.search_drawer.settings.search_behaviour.header"}, {"id": "search_drawer_enable_suggestions", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.search_behaviour.search_drawer_enable_suggestions.label", "default": true}, {"id": "search_drawer_show_price", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.search_behaviour.search_drawer_show_price.label", "default": true}, {"id": "search_drawer_show_vendor", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.search_behaviour.search_drawer_show_vendor.label", "default": false}, {"type": "header", "content": "t:settings_schema.search_drawer.settings.resources.header"}, {"id": "search_drawer_enable_collections", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.resources.search_drawer_enable_collections.label", "default": true}, {"id": "search_drawer_enable_products", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.resources.search_drawer_enable_products.label", "default": true}, {"id": "search_drawer_enable_pages", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.resources.search_drawer_enable_pages.label", "default": true}, {"id": "search_drawer_enable_articles", "type": "checkbox", "label": "t:settings_schema.search_drawer.settings.resources.search_drawer_enable_articles.label", "default": true}, {"type": "header", "content": "t:settings_schema.search_drawer.settings.popular_collections.header"}, {"id": "search_drawer_popular_collections_title", "type": "text", "label": "t:settings_schema.search_drawer.settings.popular_collections.search_drawer_popular_collections_title.label", "info": "t:settings_schema.search_drawer.settings.popular_collections.search_drawer_popular_collections_title.info", "default": "Popular collections"}, {"id": "search_drawer_popular_collections", "type": "collection_list", "label": "t:settings_schema.search_drawer.settings.popular_collections.search_drawer_popular_collections.label", "limit": 5}, {"type": "header", "content": "t:settings_schema.search_drawer.settings.popular_products.header"}, {"id": "search_drawer_popular_products_title", "type": "text", "label": "t:settings_schema.search_drawer.settings.popular_products.search_drawer_popular_products_title.label", "default": "Popular products"}, {"id": "search_drawer_popular_products", "type": "product_list", "label": "t:settings_schema.search_drawer.settings.popular_products.search_drawer_popular_products.label", "limit": 4}]}, {"name": "t:settings_schema.products.name", "settings": [{"type": "select", "id": "productcards_text_alignment", "label": "t:settings_schema.products.settings.productcards_text_alignment.label", "options": [{"value": "left", "label": "t:settings_schema.products.settings.productcards_text_alignment.options__1.label"}, {"value": "center", "label": "t:settings_schema.products.settings.productcards_text_alignment.options__2.label"}, {"value": "right", "label": "t:settings_schema.products.settings.productcards_text_alignment.options__3.label"}], "default": "left"}, {"type": "header", "content": "t:settings_schema.products.settings.labels.header"}, {"id": "show_sale_label", "type": "checkbox", "label": "t:settings_schema.products.settings.labels.show_sale_label.label", "default": true}, {"type": "select", "id": "sale_label_price", "label": "t:settings_schema.products.settings.labels.sale_label_price.label", "options": [{"value": "none", "label": "t:settings_schema.products.settings.labels.sale_label_price.options__1.label"}, {"value": "percentage", "label": "t:settings_schema.products.settings.labels.sale_label_price.options__2.label"}, {"value": "amount", "label": "t:settings_schema.products.settings.labels.sale_label_price.options__3.label"}], "default": "percentage"}, {"id": "show_stock_label", "type": "checkbox", "label": "t:settings_schema.products.settings.labels.show_stock_label.label", "info": "t:settings_schema.products.settings.labels.show_stock_label.info", "default": true}, {"type": "range", "id": "stock_label_qty", "label": "t:settings_schema.products.settings.labels.stock_label_qty.label", "min": 1, "max": 20, "step": 1, "default": 5}, {"id": "show_price_varies", "type": "checkbox", "label": "t:settings_schema.products.settings.labels.show_price_varies.label", "info": "t:settings_schema.products.settings.labels.show_price_varies.info", "default": true}, {"type": "text", "id": "product_custom_label", "label": "t:settings_schema.products.settings.labels.product_custom_label.label", "placeholder": "custom.label", "default": "custom.label", "info": "t:settings_schema.products.settings.labels.product_custom_label.info"}, {"type": "header", "content": "t:settings_schema.products.settings.stock.header"}, {"id": "show_product_stock", "type": "checkbox", "label": "t:settings_schema.products.settings.stock.show_product_stock.label", "default": true}, {"type": "range", "id": "show_product_stock_qty", "label": "t:settings_schema.products.settings.stock.show_product_stock_qty.label", "info": "t:settings_schema.products.settings.stock.show_product_stock_qty.info", "min": 1, "max": 100, "step": 1, "default": 20}, {"id": "show_product_stock_always", "type": "checkbox", "label": "t:settings_schema.products.settings.stock.show_product_stock_always.label", "default": false}, {"type": "header", "content": "t:settings_schema.products.settings.ratings.header"}, {"id": "show_product_rating", "type": "checkbox", "label": "t:settings_schema.products.settings.ratings.show_product_rating.label", "info": "t:settings_schema.products.settings.ratings.show_product_rating.info", "default": true}, {"type": "header", "content": "t:settings_schema.products.settings.vendor.header"}, {"id": "show_vendor", "type": "checkbox", "label": "t:settings_schema.products.settings.vendor.show_vendor.label", "default": true}, {"type": "header", "content": "t:settings_schema.products.settings.preorder.header"}, {"id": "show_preorder", "type": "checkbox", "label": "t:settings_schema.products.settings.preorder.show_preorder.label", "info": "t:settings_schema.products.settings.preorder.show_preorder.info", "default": false}, {"id": "show_preorder_inventory", "type": "checkbox", "label": "t:settings_schema.products.settings.preorder.show_preorder_inventory.label", "default": true}, {"id": "preorder_button_text", "type": "text", "label": "t:settings_schema.products.settings.preorder.preorder_button_text.label", "default": "Pre-order"}, {"type": "header", "content": "t:settings_schema.products.settings.short_product_description.header"}, {"type": "select", "id": "product_short_description", "label": "t:settings_schema.products.settings.short_product_description.product_short_description.label", "info": "t:settings_schema.products.settings.short_product_description.product_short_description.info", "options": [{"value": "none", "label": "t:settings_schema.products.settings.short_product_description.product_short_description.options__1.label"}, {"value": "product_description", "label": "t:settings_schema.products.settings.short_product_description.product_short_description.options__2.label"}, {"value": "custom", "label": "t:settings_schema.products.settings.short_product_description.product_short_description.options__3.label"}], "default": "product_description"}, {"type": "text", "id": "product_short_description_text", "label": "t:settings_schema.products.settings.short_product_description.product_short_description_text.label", "placeholder": "custom.short_description", "info": "t:settings_schema.products.settings.short_product_description.product_short_description_text.info"}, {"type": "header", "content": "t:settings_schema.products.settings.titles.header"}, {"type": "checkbox", "id": "product_titles_caps", "label": "t:settings_schema.products.settings.titles.product_titles_caps.label"}, {"type": "header", "content": "t:settings_schema.products.settings.color_swatches.header"}, {"type": "checkbox", "id": "enable_color_swatches", "label": "t:settings_schema.products.settings.color_swatches.enable_color_swatches.label", "info": "t:settings_schema.products.settings.color_swatches.enable_color_swatches.info"}, {"type": "textarea", "id": "color_swatch_name", "label": "t:settings_schema.products.settings.color_swatches.color_swatch_name.label", "info": "t:settings_schema.products.settings.color_swatches.color_swatch_name.info", "default": "Color"}, {"type": "textarea", "id": "color_swatch_hexes", "label": "t:settings_schema.products.settings.color_swatches.color_swatch_hexes.label", "info": "t:settings_schema.products.settings.color_swatches.color_swatch_hexes.info"}, {"type": "checkbox", "id": "color_swatches_variant_image", "label": "t:settings_schema.products.settings.color_swatches.color_swatches_variant_image.label"}]}, {"name": "t:settings_schema.shop_the_look.name", "settings": [{"type": "paragraph", "content": "t:settings_schema.shop_the_look.settings.paragraph"}, {"id": "shop_the_look_title", "type": "richtext", "label": "t:settings_schema.shop_the_look.settings.shop_the_look_title.label", "default": "<h2>Shop the look</h3>"}, {"id": "shop_the_look_subtitle", "type": "inline_richtext", "label": "t:settings_schema.shop_the_look.settings.shop_the_look_subtitle.label"}, {"id": "shop_the_look_items", "type": "range", "label": "t:settings_schema.shop_the_look.settings.shop_the_look_items.label", "info": "t:settings_schema.shop_the_look.settings.shop_the_look_items.info", "min": 4, "max": 6, "step": 1, "default": 5}, {"type": "header", "content": "t:sections.recently_viewed_products.settings.quick_buy.header"}, {"type": "paragraph", "content": "t:sections.recently_viewed_products.settings.quick_buy.paragraph"}, {"id": "enable_quick_buy_desktop", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_desktop.label", "default": true}, {"id": "enable_quick_buy_mobile", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_mobile.label", "default": true}, {"id": "enable_quick_buy_drawer", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_drawer.label", "info": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_drawer.info", "visible_if": "{{ settings.enable_quick_buy_desktop or settings.enable_quick_buy_mobile }}"}, {"id": "enable_quick_buy_qty_selector", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_qty_selector.label", "info": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_qty_selector.info", "default": true, "visible_if": "{{ settings.enable_quick_buy_desktop or settings.enable_quick_buy_mobile }}"}, {"id": "enable_color_picker", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_color_picker.label", "visible_if": "{{ settings.enable_quick_buy_desktop or settings.enable_quick_buy_mobile }}"}, {"id": "enable_quick_buy_compact", "type": "checkbox", "label": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_compact.label", "info": "t:sections.recently_viewed_products.settings.quick_buy.enable_quick_buy_compact.info", "visible_if": "{{ settings.enable_quick_buy_desktop or settings.enable_quick_buy_mobile }}"}]}, {"name": "t:settings_schema.shipping_delivery.name", "settings": [{"type": "header", "content": "t:settings_schema.shipping_delivery.settings.free_shipping.header"}, {"type": "checkbox", "id": "enable_free_shipping", "label": "t:settings_schema.shipping_delivery.settings.free_shipping.enable_free_shipping.label", "info": "t:settings_schema.shipping_delivery.settings.free_shipping.enable_free_shipping.info"}, {"type": "number", "id": "free_shipping_amount", "label": "t:settings_schema.shipping_delivery.settings.free_shipping.free_shipping_amount.label"}, {"type": "header", "content": "t:settings_schema.shipping_delivery.settings.deliverytime.header"}, {"type": "paragraph", "content": "t:settings_schema.shipping_delivery.settings.deliverytime.paragraph"}, {"id": "product_deliverytime_in_stock", "type": "text", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.product_deliverytime_in_stock.label", "default": "custom.deliverytime_in_stock", "placeholder": "custom.deliverytime_in_stock"}, {"id": "product_deliverytime_not_in_stock", "type": "text", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.product_deliverytime_not_in_stock.label", "default": "custom.deliverytime_not_in_stock", "placeholder": "custom.deliverytime_not_in_stock"}, {"id": "default_product_deliverytime_in_stock", "type": "text", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.default_product_deliverytime_in_stock.label", "placeholder": "delivered within 24 hours"}, {"id": "default_product_deliverytime_not_in_stock", "type": "text", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.default_product_deliverytime_not_in_stock.label", "placeholder": "delivered within 2 weeks"}, {"type": "text", "id": "product_deliverytime_info", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.product_deliverytime_info.label", "placeholder": "custom.deliverytime_info"}, {"type": "richtext", "id": "default_product_deliverytime_info", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.default_product_deliverytime_info.label", "info": "t:settings_schema.shipping_delivery.settings.deliverytime.default_product_deliverytime_info.info"}, {"type": "checkbox", "id": "show_deliverytime_always", "label": "t:settings_schema.shipping_delivery.settings.deliverytime.show_deliverytime_always.label"}, {"type": "header", "content": "t:settings_schema.shipping_delivery.settings.shipping_timer.header"}, {"type": "paragraph", "content": "t:settings_schema.shipping_delivery.settings.shipping_timer.paragraph"}, {"id": "shipping_timer_show_until", "type": "select", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.label", "options": [{"value": "01:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__1.label"}, {"value": "02:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__2.label"}, {"value": "03:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__3.label"}, {"value": "04:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__4.label"}, {"value": "05:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__5.label"}, {"value": "06:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__6.label"}, {"value": "07:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__7.label"}, {"value": "08:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__8.label"}, {"value": "09:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__9.label"}, {"value": "10:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__10.label"}, {"value": "11:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__11.label"}, {"value": "12:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__12.label"}, {"value": "13:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__13.label"}, {"value": "14:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__14.label"}, {"value": "15:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__15.label"}, {"value": "16:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__16.label"}, {"value": "17:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__17.label"}, {"value": "18:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__18.label"}, {"value": "19:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__19.label"}, {"value": "20:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__20.label"}, {"value": "21:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__21.label"}, {"value": "22:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__22.label"}, {"value": "23:00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__23.label"}, {"value": "23:59", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_until.options__24.label"}], "default": "17:00"}, {"id": "shipping_timer_show_from", "type": "select", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.label", "options": [{"value": "00", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__1.label"}, {"value": "01", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__2.label"}, {"value": "02", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__3.label"}, {"value": "03", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__4.label"}, {"value": "04", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__5.label"}, {"value": "05", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__6.label"}, {"value": "06", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__7.label"}, {"value": "07", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__8.label"}, {"value": "08", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__9.label"}, {"value": "09", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__10.label"}, {"value": "10", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__11.label"}, {"value": "11", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__12.label"}, {"value": "12", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__13.label"}, {"value": "13", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__14.label"}, {"value": "14", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__15.label"}, {"value": "15", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__16.label"}, {"value": "16", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__17.label"}, {"value": "17", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__18.label"}, {"value": "18", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__19.label"}, {"value": "19", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__20.label"}, {"value": "20", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__21.label"}, {"value": "21", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__22.label"}, {"value": "22", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__23.label"}, {"value": "23", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_from.options__24.label"}], "default": "07"}, {"id": "shipping_timer_show_unavailable", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_show_unavailable.label", "default": true}, {"type": "paragraph", "content": "t:settings_schema.shipping_delivery.settings.shipping_timer.paragraph__2"}, {"id": "shipping_timer_enable_1", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_monday.label", "default": true}, {"id": "shipping_timer_enable_2", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_tuesday.label", "default": true}, {"id": "shipping_timer_enable_3", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_wednesday.label", "default": true}, {"id": "shipping_timer_enable_4", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_thursday.label", "default": true}, {"id": "shipping_timer_enable_5", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_friday.label", "default": true}, {"id": "shipping_timer_enable_6", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_saturday.label", "default": false}, {"id": "shipping_timer_enable_7", "type": "checkbox", "label": "t:settings_schema.shipping_delivery.settings.shipping_timer.shipping_timer_enable_sunday.label", "default": false}]}, {"name": "t:settings_schema.social_media.name", "settings": [{"type": "number", "id": "whatsapp", "label": "t:settings_schema.social_media.settings.whatsapp.label"}, {"type": "header", "content": "t:settings_schema.social_media.settings.header"}, {"type": "paragraph", "content": "t:settings_schema.social_media.settings.paragraph"}, {"type": "url", "id": "social_instagram", "label": "t:settings_schema.social_media.settings.social_instagram.label"}, {"type": "text", "id": "social_instagram_name", "label": "t:settings_schema.social_media.settings.social_instagram_name.label"}, {"type": "url", "id": "social_pinterest", "label": "t:settings_schema.social_media.settings.social_pinterest.label"}, {"type": "text", "id": "social_pinterest_name", "label": "t:settings_schema.social_media.settings.social_pinterest_name.label"}, {"type": "url", "id": "social_youtube", "label": "t:settings_schema.social_media.settings.social_youtube.label"}, {"type": "text", "id": "social_youtube_name", "label": "t:settings_schema.social_media.settings.social_youtube_name.label"}, {"type": "url", "id": "social_facebook", "label": "t:settings_schema.social_media.settings.social_facebook.label"}, {"type": "text", "id": "social_facebook_name", "label": "t:settings_schema.social_media.settings.social_facebook_name.label"}, {"type": "url", "id": "social_twitter", "label": "t:settings_schema.social_media.settings.social_twitter.label"}, {"type": "text", "id": "social_twitter_name", "label": "t:settings_schema.social_media.settings.social_twitter_name.label"}, {"type": "url", "id": "social_tiktok", "label": "t:settings_schema.social_media.settings.social_tiktok.label"}, {"type": "text", "id": "social_tiktok_name", "label": "t:settings_schema.social_media.settings.social_tiktok_name.label"}, {"type": "url", "id": "social_tumblr", "label": "t:settings_schema.social_media.settings.social_tumblr.label"}, {"type": "text", "id": "social_tumblr_name", "label": "t:settings_schema.social_media.settings.social_tumblr_name.label"}, {"type": "url", "id": "social_snapchat", "label": "t:settings_schema.social_media.settings.social_snapchat.label"}, {"type": "text", "id": "social_snapchat_name", "label": "t:settings_schema.social_media.settings.social_snapchat_name.label"}]}, {"name": "t:settings_schema.newsletter.name", "settings": [{"id": "show_newsletterpopup", "type": "checkbox", "label": "t:settings_schema.newsletter.settings.newsletter_popup.show_newsletterpopup.label", "default": false}, {"id": "newsletter_popup_testmode", "type": "checkbox", "label": "t:settings_schema.newsletter.settings.newsletter_popup.newsletter_popup_testmode.label", "info": "t:settings_schema.newsletter.settings.newsletter_popup.newsletter_popup_testmode.info"}, {"id": "newsletter_popup_seconds", "type": "range", "label": "t:settings_schema.newsletter.settings.newsletter_popup.newsletter_popup_seconds.label", "min": 0, "max": 30, "step": 1, "unit": "sec", "default": 5}, {"id": "newsletter_popup_image", "type": "image_picker", "label": "t:settings_schema.newsletter.settings.newsletter_popup.newsletter_popup_image.label", "info": "t:settings_schema.newsletter.settings.newsletter_popup.newsletter_popup_image.info"}, {"type": "header", "content": "t:settings_schema.newsletter.settings.checkbox.header"}, {"id": "enable_newsletter_terms_checkbox", "type": "checkbox", "label": "t:settings_schema.newsletter.settings.checkbox.enable_newsletter_terms_checkbox.label"}, {"id": "newsletter_terms_text", "type": "richtext", "label": "t:settings_schema.newsletter.settings.checkbox.newsletter_terms_text.label", "info": "t:settings_schema.newsletter.settings.checkbox.newsletter_terms_text.info"}]}, {"name": "t:settings_schema.age_verify_popup.name", "settings": [{"id": "show_age_verify_popup", "type": "checkbox", "label": "t:settings_schema.age_verify_popup.settings.show_age_verify_popup.label", "info": "t:settings_schema.age_verify_popup.settings.show_age_verify_popup.info"}, {"id": "age_verify_popup_testmode", "type": "checkbox", "label": "t:settings_schema.age_verify_popup.settings.age_verify_popup_testmode.label", "info": "t:settings_schema.age_verify_popup.settings.age_verify_popup_testmode.info"}, {"type": "image_picker", "id": "age_verify_popup_image", "label": "t:settings_schema.age_verify_popup.settings.age_verify_popup_image.label", "info": "t:settings_schema.age_verify_popup.settings.age_verify_popup_image.info"}, {"type": "header", "content": "t:settings_schema.age_verify_popup.settings.verify.header"}, {"id": "age_verify_popup_title", "type": "text", "label": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_title.label", "default": "Age verification"}, {"id": "age_verify_popup_text", "type": "richtext", "label": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_text.label", "default": "<p>Please confirm that you are 18 years or older to enter the shop.</p>"}, {"id": "age_verify_popup_cookie_text", "type": "richtext", "label": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_cookie_text.label", "info": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_cookie_text.info", "default": "<p>Our shop uses cookies, by confirming you are agreeing to our <a href=\"/policies/terms-of-service\" target=\"_blank\" title=\"Terms of Service\">terms of service</a> and <a href=\"/policies/privacy-policy\" target=\"_blank\" title=\"Privacy Policy\">privacy policy</a></p>"}, {"id": "age_verify_popup_accept_text", "type": "text", "label": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_accept_text.label", "default": "Yes, I'm 18 years or older"}, {"id": "age_verify_popup_deny_text", "type": "text", "label": "t:settings_schema.age_verify_popup.settings.verify.age_verify_popup_deny_text.label", "default": "No"}, {"type": "header", "content": "t:settings_schema.age_verify_popup.settings.denied.header"}, {"type": "paragraph", "content": "t:settings_schema.age_verify_popup.settings.denied.paragraph"}, {"id": "age_verify_popup_denied_title", "type": "text", "label": "t:settings_schema.age_verify_popup.settings.denied.age_verify_popup_denied_title.label", "default": "Have patience..."}, {"id": "age_verify_popup_denied_text", "type": "richtext", "label": "t:settings_schema.age_verify_popup.settings.denied.age_verify_popup_denied_text.label", "default": "<p>You are not old enough to visit the shop yet.</p>"}]}, {"name": "t:settings_schema.cookies.name", "settings": [{"id": "show_cookiebanner", "type": "select", "label": "t:settings_schema.cookies.settings.show_cookiebanner.label", "info": "t:settings_schema.cookies.settings.show_cookiebanner.info", "options": [{"value": "banner", "label": "t:settings_schema.cookies.settings.show_cookiebanner.options__1.label"}, {"value": "popup", "label": "t:settings_schema.cookies.settings.show_cookiebanner.options__2.label"}, {"value": "none", "label": "t:settings_schema.cookies.settings.show_cookiebanner.options__3.label"}], "default": "none"}, {"id": "cookiebanner_testmode", "type": "checkbox", "label": "t:settings_schema.cookies.settings.cookiebanner_testmode.label", "info": "t:settings_schema.cookies.settings.cookiebanner_testmode.info"}, {"type": "image_picker", "id": "cookiebanner_image", "label": "t:settings_schema.cookies.settings.cookiebanner_image.label", "info": "t:settings_schema.cookies.settings.cookiebanner_image.info"}, {"id": "cookiebanner_title", "type": "text", "label": "t:settings_schema.cookies.settings.cookiebanner_title.label", "info": "t:settings_schema.cookies.settings.cookiebanner_title.info"}, {"id": "cookiebanner_text", "type": "richtext", "label": "t:settings_schema.cookies.settings.cookiebanner_text.label", "info": "t:settings_schema.cookies.settings.cookiebanner_text.info", "default": "<p>Our site uses cookies. By clicking on accept you agree with our <a href=\"/policies/terms-of-service\" target=\"_blank\" title=\"Terms of Service\">voorwaarden</a> and <a href=\"/policies/privacy-policy\" target=\"_blank\" title=\"Privacy Policy\">privacy policy</a>.</p>"}]}, {"name": "t:settings_schema.custom_scripts.name", "settings": [{"id": "custom_script_for_head", "type": "html", "label": "t:settings_schema.custom_scripts.settings.custom_script_for_head.label", "info": "t:settings_schema.custom_scripts.settings.custom_script_for_head.info"}, {"id": "custom_script_for_body", "type": "html", "label": "t:settings_schema.custom_scripts.settings.custom_script_for_body.label", "info": "t:settings_schema.custom_scripts.settings.custom_script_for_body.info"}]}]