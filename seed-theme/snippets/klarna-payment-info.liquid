{%- assign current_variant = product.selected_or_first_available_variant -%}
{%- if current_variant -%}
  <div class="klarna-payment-section" id="klarna-section-{{ section_id }}">
    <div class="klarna-badge-container">
      <div class="klarna-logo-default">
        <span class="klarna-text">KLARNA</span>
      </div>
      
      <div class="klarna-payment-text">
        <span class="klarna-payment-amount">4 interest-free payments of ${{ current_variant.price | divided_by: 4 | money_without_currency }}</span>
        <a href="#" class="klarna-learn-more" data-klarna-popup="{{ section_id }}">Learn more</a>
      </div>
    </div>
  </div>

  {%- comment -%} Klarna Popup {%- endcomment -%}
  <div class="klarna-popup-overlay" id="klarna-popup-{{ section_id }}">
    <div class="klarna-popup">
      <div class="klarna-popup-header">
        <div class="klarna-popup-logo">
          <span class="klarna-popup-title-text">KLARNA</span>
        </div>
        <button class="klarna-popup-close" data-klarna-close="{{ section_id }}">&times;</button>
      </div>
      
      <div class="klarna-popup-content">
        <h2 class="klarna-popup-main-title">Buy now. Pay with Klarna at your own pace.</h2>
        <p class="klarna-popup-subtitle">Get what you love, choose how you pay.</p>
        
        <div class="klarna-payment-options">
          <div class="klarna-option klarna-pay-full">
            <div class="klarna-option-price">${{ current_variant.price | money_without_currency }}</div>
            <div class="klarna-option-label">Pay now</div>
            <div class="klarna-option-badge">Pay in full</div>
          </div>
          
          <div class="klarna-option klarna-pay-installments">
            <div class="klarna-option-price">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
            <div class="klarna-option-label">Every 14 days</div>
            <div class="klarna-option-badge">Pay in 4</div>
            
            <div class="klarna-installment-schedule">
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-1"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">Today</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-2"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 2 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-3"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 4 weeks</div>
                </div>
              </div>
              <div class="klarna-installment">
                <div class="klarna-circle klarna-circle-4"></div>
                <div class="klarna-installment-text">
                  <div class="klarna-amount">${{ current_variant.price | divided_by: 4 | money_without_currency }}</div>
                  <div class="klarna-timing">In 6 weeks</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="klarna-summary">
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">APR</span>
            <span class="klarna-summary-value">0%</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total interest</span>
            <span class="klarna-summary-value">Free</span>
          </div>
          <div class="klarna-summary-item">
            <span class="klarna-summary-label">Total</span>
            <span class="klarna-summary-value">${{ current_variant.price | money_without_currency }}</span>
          </div>
        </div>
        
        <p class="klarna-disclaimer">For Pay in 4, CA resident loans made or arranged pursuant to California financing law license. NMLS # 1353190.</p>
        
        <div class="klarna-how-it-works">
          <h3>How it works</h3>
          <div class="klarna-step">
            <div class="klarna-step-number">1</div>
            <div class="klarna-step-content">
              <h4>At checkout select Klarna</h4>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">2</div>
            <div class="klarna-step-content">
              <h4>Choose your payment plan</h4>
              <p>Different payment plans may be shown depending on the purchase amount and credit score.</p>
            </div>
          </div>
          <div class="klarna-step">
            <div class="klarna-step-number">3</div>
            <div class="klarna-step-content">
              <h4>Complete your checkout</h4>
              <p>The amount will be charged based on the payment plan you chose.</p>
            </div>
          </div>
        </div>
        
        <button class="klarna-got-it-btn" data-klarna-close="{{ section_id }}">Got it</button>
      </div>
    </div>
  </div>

  <script>
  document.addEventListener('DOMContentLoaded', function() {
    const sectionId = '{{ section_id }}';

    // Open popup
    const learnMoreBtn = document.querySelector('[data-klarna-popup="' + sectionId + '"]');
    const popup = document.getElementById('klarna-popup-' + sectionId);

    if (learnMoreBtn && popup) {
      learnMoreBtn.addEventListener('click', function(e) {
        e.preventDefault();
        popup.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        document.body.classList.add('klarna-popup-open');
      });
    }

    // Close popup function
    function closePopup() {
      if (popup) {
        popup.style.display = 'none';
        document.body.style.overflow = '';
        document.body.classList.remove('klarna-popup-open');
      }
    }

    // Close popup
    const closeButtons = document.querySelectorAll('[data-klarna-close="' + sectionId + '"]');
    closeButtons.forEach(function(btn) {
      btn.addEventListener('click', closePopup);
    });

    // Close on overlay click
    if (popup) {
      popup.addEventListener('click', function(e) {
        if (e.target === popup) {
          closePopup();
        }
      });
    }

    // Update prices when variant changes
    const productForm = document.querySelector('#product-form-' + sectionId);
    if (productForm) {
      productForm.addEventListener('change', function(e) {
        if (e.target.name === 'id' || e.target.name.includes('options[')) {
          updateKlarnaPrices(sectionId);
        }
      });
    }

    function updateKlarnaPrices(sectionId) {
      const productForm = document.querySelector('#product-form-' + sectionId);
      if (!productForm) return;

      const variantIdInput = productForm.querySelector('[name="id"]');
      if (!variantIdInput) return;

      const variantId = variantIdInput.value;
      const productDataElement = document.querySelector('#ProductVariantsJSON-{{ product.id }}');

      if (!productDataElement) return;

      try {
        const productData = JSON.parse(productDataElement.textContent || '{}');
        const currentVariant = productData.variants?.find(variant => variant.id == variantId);

        if (currentVariant) {
          const installmentAmount = (currentVariant.price / 4 / 100).toFixed(2);
          const fullAmount = (currentVariant.price / 100).toFixed(2);

          // Update badge
          const badgeAmount = document.querySelector('#klarna-section-' + sectionId + ' .klarna-payment-amount');
          if (badgeAmount) {
            badgeAmount.textContent = '4 interest-free payments of $' + installmentAmount;
          }

          // Update popup amounts
          const popupAmounts = document.querySelectorAll('#klarna-popup-' + sectionId + ' .klarna-option-price');
          if (popupAmounts[0]) popupAmounts[0].textContent = '$' + fullAmount;
          if (popupAmounts[1]) popupAmounts[1].textContent = '$' + installmentAmount;

          // Update installment amounts
          const installmentAmounts = document.querySelectorAll('#klarna-popup-' + sectionId + ' .klarna-amount');
          installmentAmounts.forEach(function(amount) {
            amount.textContent = '$' + installmentAmount;
          });

          // Update total
          const totalAmount = document.querySelector('#klarna-popup-' + sectionId + ' .klarna-summary-item:last-child .klarna-summary-value');
          if (totalAmount) {
            totalAmount.textContent = '$' + fullAmount;
          }
        }
      } catch (error) {
        console.error('Error updating Klarna prices:', error);
      }
    }

    // Handle escape key to close popup
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape') {
        if (popup && popup.style.display === 'flex') {
          closePopup();
        }
      }
    });
  });
  </script>
{%- endif -%}
