{%- liquid
  assign section_id = 'countdown-timer-' | append: flash_sale_text | replace: ' ', '-' | downcase
  assign full_end_datetime = end_date | append: 'T' | append: end_time | append: ':00'
-%}

{{ 'countdown-timer.css' | asset_url | stylesheet_tag }}

<style>
  #{{ section_id }} {
    background: transparent;
    padding: {{ padding_top | default: 20 }}px 0 {{ padding_bottom | default: 20 }}px;
    text-align: center;
  }
  
  #{{ section_id }} .countdown-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
    gap: 20px;
  }
  
  #{{ section_id }} .countdown-text {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-wrap: wrap;
  }
  
  #{{ section_id }} .flash-sale {
    color: {{ text_color | default: '#1e306e' }};
    font-family: {{ font_family | default: 'Lato' }}, sans-serif;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.37px;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .main-offer {
    color: {{ text_color | default: '#1e306e' }};
    font-family: {{ font_family | default: 'Lato' }}, sans-serif;
    font-size: 22px;
    font-weight: 900;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .guarantee {
    color: {{ text_color | default: '#1e306e' }};
    font-family: {{ font_family | default: 'Lato' }}, sans-serif;
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 0.37px;
    line-height: 1.4;
    white-space: nowrap;
  }
  
  #{{ section_id }} .countdown-timer {
    display: flex;
    gap: 3px;
    align-items: center;
  }
  
  #{{ section_id }} .time-unit {
    background: {{ timer_bg_color | default: '#ffffff' }};
    border-radius: 10px;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 4px 8px 0px, rgba(0, 0, 0, 0.06) 0px 0px 2px 0px, rgba(0, 0, 0, 0.04) 0px 0px 1px 0px;
    width: 56px;
    height: 55px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin: 0 10px;
  }
  
  #{{ section_id }} .time-number {
    color: {{ timer_text_color | default: '#1e306e' }};
    font-family: {{ font_family | default: 'Lato' }}, sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.3;
  }
  
  #{{ section_id }} .time-label {
    position: absolute;
    bottom: 9px;
    left: 50%;
    transform: translateX(-50%);
    color: {{ timer_text_color | default: '#1e306e' }};
    font-family: {{ font_family | default: 'Lato' }}, sans-serif;
    font-size: 8px;
    letter-spacing: 0.1px;
    line-height: 1.3;
    text-transform: uppercase;
    white-space: nowrap;
  }
  
  #{{ section_id }}.countdown-ended {
    display: none;
  }
  
  {% unless enable_animations %}
  #{{ section_id }} .countdown-timer .time-unit {
    transition: none;
  }
  
  #{{ section_id }} .countdown-timer .time-unit:hover {
    transform: none;
    box-shadow: rgba(0, 0, 0, 0.04) 0px 4px 8px 0px, rgba(0, 0, 0, 0.06) 0px 0px 2px 0px, rgba(0, 0, 0, 0.04) 0px 0px 1px 0px;
  }
  
  #{{ section_id }} .countdown-timer .time-number {
    transition: none;
  }
  {% endunless %}
  
  @media (max-width: 768px) {
    #{{ section_id }} .countdown-container {
      flex-direction: column;
      gap: 15px;
    }
    
    #{{ section_id }} .countdown-text {
      flex-direction: column;
      gap: 10px;
      text-align: center;
    }
    
    #{{ section_id }} .flash-sale,
    #{{ section_id }} .main-offer,
    #{{ section_id }} .guarantee {
      font-size: 14px;
      white-space: normal;
    }
    
    #{{ section_id }} .main-offer {
      font-size: 18px;
    }
    
    #{{ section_id }} .time-unit {
      width: 48px;
      height: 47px;
      margin: 0 5px;
    }
    
    #{{ section_id }} .time-number {
      font-size: 18px;
    }
    
    #{{ section_id }} .time-label {
      font-size: 7px;
      bottom: 7px;
    }
  }
  
  @media (max-width: 480px) {
    #{{ section_id }} .countdown-timer {
      gap: 2px;
    }
    
    #{{ section_id }} .time-unit {
      width: 40px;
      height: 40px;
      margin: 0 3px;
    }
    
    #{{ section_id }} .time-number {
      font-size: 16px;
    }
    
    #{{ section_id }} .time-label {
      font-size: 6px;
      bottom: 5px;
    }
  }
</style>

<section id="{{ section_id }}" class="countdown-section">
  <div class="countdown-container">
    <div class="countdown-text">
      {%- if flash_sale_text != blank -%}
        <span class="flash-sale">{{ flash_sale_text }}</span>
      {%- endif -%}
      
      {%- if main_offer_text != blank -%}
        <span class="main-offer">{{ main_offer_text }}</span>
      {%- endif -%}
      
      {%- if guarantee_text != blank -%}
        <span class="guarantee">{{ guarantee_text }}</span>
      {%- endif -%}
    </div>
    
    {%- if end_date != blank -%}
      <div class="countdown-timer{% unless enable_animations %} no-animations{% endunless %}" data-end-date="{{ full_end_datetime }}" data-timezone-offset="{{ timezone_offset | default: 0 }}" data-auto-hide="{{ auto_hide_on_end }}" data-reset-type="{{ reset_type | default: 'none' }}" data-reset-time="{{ reset_time | default: '00:00' }}">
        {%- if show_days != false -%}
          <div class="time-unit">
            <span class="time-number" data-time="days">00</span>
            <span class="time-label">{{ days_label | default: 'DAYS' }}</span>
          </div>
        {%- endif -%}
        {%- if show_hours != false -%}
          <div class="time-unit">
            <span class="time-number" data-time="hours">00</span>
            <span class="time-label">{{ hours_label | default: 'HOURS' }}</span>
          </div>
        {%- endif -%}
        {%- if show_minutes != false -%}
          <div class="time-unit">
            <span class="time-number" data-time="minutes">00</span>
            <span class="time-label">{{ minutes_label | default: 'MIN' }}</span>
          </div>
        {%- endif -%}
        {%- if show_seconds != false -%}
          <div class="time-unit">
            <span class="time-number" data-time="seconds">00</span>
            <span class="time-label">{{ seconds_label | default: 'SEC' }}</span>
          </div>
        {%- endif -%}
      </div>
    {%- endif -%}
  </div>
</section>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const countdownTimer = document.querySelector('#{{ section_id }} .countdown-timer');
    if (!countdownTimer) return;
    
    const endDate = countdownTimer.dataset.endDate;
    const timezoneOffset = parseInt(countdownTimer.dataset.timezoneOffset) || 0;
    const autoHide = countdownTimer.dataset.autoHide === 'true';
    const resetType = countdownTimer.dataset.resetType;
    const resetTime = countdownTimer.dataset.resetTime;
    const sectionId = '{{ section_id }}';
    
    if (!endDate) return;
    
    let targetDate = new Date(endDate);
    targetDate.setHours(targetDate.getHours() + timezoneOffset);
    
    const storageKey = `countdown-${sectionId}-target`;
    const savedTarget = localStorage.getItem(storageKey);
    
    if (savedTarget && resetType !== 'none') {
      const savedDate = new Date(savedTarget);
      if (savedDate > new Date()) {
        targetDate = savedDate;
      }
    }
    
    let countdownInterval;
    
    function updateCountdown() {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;
      
      if (distance < 0) {
        handleCountdownEnd();
        return;
      }
      
      const days = Math.floor(distance / (1000 * 60 * 60 * 24));
      const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((distance % (1000 * 60)) / 1000);
      
      const daysEl = countdownTimer.querySelector('[data-time="days"]');
      const hoursEl = countdownTimer.querySelector('[data-time="hours"]');
      const minutesEl = countdownTimer.querySelector('[data-time="minutes"]');
      const secondsEl = countdownTimer.querySelector('[data-time="seconds"]');
      
      updateTimeElement(daysEl, days);
      updateTimeElement(hoursEl, hours);
      updateTimeElement(minutesEl, minutes);
      updateTimeElement(secondsEl, seconds);
      
      const sectionEl = document.getElementById(sectionId);
      if (distance < (24 * 60 * 60 * 1000)) {
        sectionEl.classList.add('urgent');
      } else {
        sectionEl.classList.remove('urgent');
      }
    }
    
    function updateTimeElement(element, newValue) {
      if (!element) return;
      
      const formattedValue = newValue.toString().padStart(2, '0');
      const currentValue = element.textContent;
      
      if (currentValue !== formattedValue) {
        {% if enable_animations %}
        element.dataset.prev = currentValue;
        element.classList.add('flip-animation');
        
        setTimeout(() => {
          element.textContent = formattedValue;
          element.classList.remove('flip-animation');
        }, 300);
        {% else %}
        element.textContent = formattedValue;
        {% endif %}
      }
    }
    
    function handleCountdownEnd() {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
      
      if (autoHide) {
        document.getElementById(sectionId).classList.add('countdown-ended');
        localStorage.setItem(`countdown-${sectionId}-hidden`, 'true');
      }
      
      if (resetType === 'daily' && resetTime) {
        resetCountdownDaily();
      } else if (resetType === 'weekly' && resetTime) {
        resetCountdownWeekly();
      }
    }
    
    function resetCountdownDaily() {
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const [hours, minutes] = (resetTime || '00:00').split(':');
      tomorrow.setHours(parseInt(hours) || 0, parseInt(minutes) || 0, 0, 0);
      
      targetDate = new Date(tomorrow.getTime() + (timezoneOffset * 60 * 60 * 1000));
      localStorage.setItem(storageKey, targetDate.toISOString());
      localStorage.removeItem(`countdown-${sectionId}-hidden`);
      document.getElementById(sectionId).classList.remove('countdown-ended');
      
      startCountdown();
    }
    
    function resetCountdownWeekly() {
      const nextWeek = new Date();
      nextWeek.setDate(nextWeek.getDate() + 7);
      const [hours, minutes] = (resetTime || '00:00').split(':');
      nextWeek.setHours(parseInt(hours) || 0, parseInt(minutes) || 0, 0, 0);
      
      targetDate = new Date(nextWeek.getTime() + (timezoneOffset * 60 * 60 * 1000));
      localStorage.setItem(storageKey, targetDate.toISOString());
      localStorage.removeItem(`countdown-${sectionId}-hidden`);
      document.getElementById(sectionId).classList.remove('countdown-ended');
      
      startCountdown();
    }
    
    function startCountdown() {
      if (countdownInterval) {
        clearInterval(countdownInterval);
      }
      updateCountdown();
      countdownInterval = setInterval(updateCountdown, 1000);
    }
    
    if (autoHide && localStorage.getItem(`countdown-${sectionId}-hidden`) === 'true') {
      const now = new Date().getTime();
      if (targetDate.getTime() > now) {
        localStorage.removeItem(`countdown-${sectionId}-hidden`);
        document.getElementById(sectionId).classList.remove('countdown-ended');
      } else {
        document.getElementById(sectionId).classList.add('countdown-ended');
        return;
      }
    }
    
    startCountdown();
  });
</script>
