#!/usr/bin/env python3
import json
import re

def update_color_schemes():
    # Read the settings_data.json file
    with open('config/settings_data.json', 'r') as f:
        data = json.load(f)
    
    # Function to add link_hover_color to a scheme if it doesn't exist
    def add_link_hover_color(scheme_settings):
        if 'link_hover_color' not in scheme_settings:
            # Use primary_button_bg as default hover color, or accent if available
            hover_color = scheme_settings.get('primary_button_bg', scheme_settings.get('accent', '#FF6602'))
            # Insert after primary_fg
            new_settings = {}
            for key, value in scheme_settings.items():
                new_settings[key] = value
                if key == 'primary_fg':
                    new_settings['link_hover_color'] = hover_color
            return new_settings
        return scheme_settings
    
    # Update current color schemes
    if 'color_schemes' in data['current']:
        for scheme_id, scheme_data in data['current']['color_schemes'].items():
            if 'settings' in scheme_data:
                data['current']['color_schemes'][scheme_id]['settings'] = add_link_hover_color(scheme_data['settings'])
    
    # Update presets
    if 'presets' in data:
        for preset_name, preset_data in data['presets'].items():
            if 'color_schemes' in preset_data:
                for scheme_id, scheme_data in preset_data['color_schemes'].items():
                    if 'settings' in scheme_data:
                        data['presets'][preset_name]['color_schemes'][scheme_id]['settings'] = add_link_hover_color(scheme_data['settings'])
    
    # Write back to file
    with open('config/settings_data.json', 'w') as f:
        json.dump(data, f, indent=2)
    
    print("Successfully updated all color schemes with link_hover_color field!")

if __name__ == "__main__":
    update_color_schemes()
